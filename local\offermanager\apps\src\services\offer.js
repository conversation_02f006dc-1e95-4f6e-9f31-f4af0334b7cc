import { ajax } from "@/helpers/moodle";

/**
 * Busca ofertas.
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function fetchOffers(params = {}) {
  try {
    return await ajax("local_offermanager_fetch", {
      search_string: params.search || "",
      type: params.type || null,
      only_active: params.onlyActive === true,
      page: params.page || 1,
      per_page: params.perPage || 25,
      sort_by: params.sortBy || "name",
      sort_direction: params.sortDesc ? "DESC" : "ASC",
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca uma oferta pelo ID.
 * @param {int} id
 * @returns {Promise<any>}
 */
export async function getOffer(id) {
  try {
    return await ajax("local_offermanager_get", {
      id,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Salva uma oferta.
 * @param {object} offerData
 * @returns {Promise<any>}
 */
export async function saveOffer(offerData) {
  try {
    return await ajax("local_offermanager_save", {
      id: offerData.id || 0,
      name: offerData.name,
      description: offerData.description || "",
      type: offerData.type || "",
      status: offerData.status || 0,
      audienceids: offerData.audiences || [],
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Deleta uma oferta.
 * @param {int} id
 * @returns {Promise<any>}
 */
export async function deleteOffer(id) {
  try {
    return await ajax("local_offermanager_delete", {
      id,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca tipos de oferta.
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function getOfferTypes(params = {}) {
  try {
    return await ajax("local_offermanager_get_types", {
      search_string: params.search || "",
      only_active: params.onlyActive === true,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca opções de tipos de oferta.
 * @returns {Promise<any>}
 */
export async function getTypeOptions() {
  try {
    return await ajax("local_offermanager_get_type_options", {});
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Remove um curso da oferta.
 * @param {int} offerId
 * @param {int} courseId
 * @returns {Promise<any>}
 */
export async function removeCourseFromOffer(offerId, courseId) {
  try {
    return await ajax("local_offermanager_delete_course", {
      offercourseid: courseId,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Altera o status de um curso na oferta.
 * @param {int} offerId
 * @param {int} courseId
 * @param {boolean} active
 * @returns {Promise<any>}
 */
export async function toggleCourseStatus(offerId, courseId, active) {
  try {
    return await ajax("local_offermanager_set_course_status", {
      id: courseId,
      status: active ? 1 : 0, // Convertendo boolean para int
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca públicos-alvo.
 * @param {string} query
 * @returns {Promise<any>}
 */
export async function searchAudiences(query) {
  try {
    const response = await ajax("local_offermanager_get_audiences", {
      offerid: 0,
    });

    const filteredAudiences = response.all_audiences.filter((audience) =>
      audience.name.toLowerCase().includes(query.toLowerCase())
    );

    return filteredAudiences.map((audience) => ({
      id: audience.id,
      name: audience.name,
    }));
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Atualiza públicos-alvo.
 * @param {int} offerId
 * @param {array} audienceIds
 * @returns {Promise<any>}
 */
export async function updateAudiences(offerId, audienceIds) {
  try {
    return await ajax("local_offermanager_update_audiences", {
      offerid: offerId,
      audienceids: audienceIds,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Altera o status de uma oferta.
 * @param {int} offerId
 * @param {boolean} status
 * @returns {Promise<any>}
 */
export async function toggleOfferStatus(offerId, status) {
  try {
    return await ajax("local_offermanager_set_status", {
      id: offerId,
      status: !status, // inverte o status atual
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca categorias.
 * @param {string} search - String para busca por nome de categoria
 * @param {int} offerId - ID da oferta (opcional)
 * @returns {Promise<any>}
 */
export async function getCategories(search = "", offerId = 0) {
  try {
    return await ajax("local_offermanager_get_categories", {
      search_string: search,
      offerid: offerId,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca cursos por categoria (POTENCIAIS).
 * @param {int} offerId
 * @param {int} categoryId
 * @param {string} search
 * @param {int} page
 * @param {int} perPage
 * @returns {Promise<any>}
 */
export async function getCoursesByCategory(
  offerId,
  categoryId,
  search = "",
  page = 1,
  perPage = 20
) {
  try {
    const params = {
      offerid: offerId,
      categoryid: categoryId,
      search_string: search || "",
      page: page,
      per_page: perPage,
      exclude_courseids: [],
    };

    return await ajax("local_offermanager_fetch_potential_courses", params);
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca cursos da oferta por nome.
 * @param {int} offerId
 * @param {string} search
 * @returns {Promise<any>}
 */
export async function searchCurrentCoursesByName(offerId, search = "") {
  try {
    const response = await ajax("local_offermanager_fetch_current_courses", {
      offerid: offerId,
      categoryid: 0,
      search_string: search,
      exclude_courseids: [],
    });
    return response;
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca cursos da oferta por categoria.
 * @param {int} offerId
 * @param {int} categoryId
 * @returns {Promise<any>}
 */
export async function searchCurrentCoursesByCategory(
  offerId,
  categoryId,
  search = "",
  exclude_courseids = [],
  only_active = false
) {
  try {
    return await ajax("local_offermanager_fetch_current_courses", {
      offerid: offerId,
      categoryid: categoryId,
      search_string: search,
      exclude_courseids: exclude_courseids || [],
      only_active: only_active,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Adiciona cursos à oferta.
 * @param {int} offerId
 * @param {array} courseIds
 * @returns {Promise<any>}
 */
export async function addCoursesToOffer(offerId, courseIds) {
  try {
    return await ajax("local_offermanager_add_courses", {
      offerid: offerId,
      courseids: courseIds,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca cursos atuais da oferta.
 * @param {int} offerId
 * @param {object} options
 * @returns {Promise<any>}
 */
export async function getCurrentCourses(offerId, options = {}) {
  try {
    if (options.sortBy === "name") {
      options.sortBy = "fullname";
    }

    if (options.sortBy === "courseClassCount") {
      options.sortBy = "class_counter";
    }

    return await ajax("local_offermanager_get_current_courses", {
      offerid: offerId,
      only_active: options.onlyActive || false,
      courseids: options.courseIds || [],
      page: options.page || 1,
      per_page: options.perPage || 100,
      sort_by: options.sortBy || "id",
      sort_direction: options.sortDesc ? "DESC" : "ASC",
      course_search: options.courseSearch || "",
      category_search: options.categorySearch || "",
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Cria uma nova turma
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function addClass(params) {
  try {
    // Lista de campos aceitos no objeto optional_fields
    const acceptedOptionalFields = [
      "enableenddate",
      "enddate",
      "enablepreenrolment",
      "preenrolmentstartdate",
      "preenrolmentenddate",
      "description",
      "enableenrolperiod",
      "enrolperiod",
      "minusers",
      "maxusers",
      "roleid",
      "enablereenrol",
      "reenrolmentsituations",
      "enableextension",
      "extensionperiod",
      "extensiondaysavailable",
      "extensionmaxrequests",
      "extensionallowedsituations",
    ];

    const cleanParams = {
      optional_fields: {},
    };

    cleanParams.classname = params.classname;
    cleanParams.startdate = params.startdate;
    cleanParams.offercourseid = parseInt(params.offercourseid);
    cleanParams.teachers = [...params.teachers];
    cleanParams.enrol = params.enrol;

    // Copia apenas os campos aceitos e remove valores undefined, null, 0 ou string vazia
    if (params.optional_fields) {
      acceptedOptionalFields.forEach((field) => {
        if (field in params.optional_fields) {
          // Verifica se o valor não é undefined, null, 0 ou string vazia
          const value = params.optional_fields[field];

          // Campos numéricos que devem ser tratados especialmente
          const numericFields = [
            "enrolperiod",
            "extensionperiod",
            "extensiondaysavailable",
            "extensionmaxrequests",
            "minusers",
            "maxusers",
          ];

          // Se for um campo numérico e o valor for 0, null, undefined ou string vazia, não inclui
          if (numericFields.includes(field)) {
            if (
              value !== 0 &&
              value !== null &&
              value !== undefined &&
              value !== ""
            ) {
              cleanParams.optional_fields[field] = value;
            }
          }
          // Para campos booleanos, sempre inclui
          else if (typeof value === "boolean") {
            cleanParams.optional_fields[field] = value;
          }
          // Para arrays, inclui apenas se não for vazio
          else if (Array.isArray(value)) {
            if (value.length > 0) {
              cleanParams.optional_fields[field] = value;
            }
          }
          // Para outros tipos, inclui apenas se não for null, undefined ou string vazia
          else if (value !== null && value !== undefined && value !== "") {
            cleanParams.optional_fields[field] = value;
          }
        }
      });
    }

    // Verificar se todos os campos obrigatórios estão presentes
    const requiredFields = ["offercourseid", "classname", "startdate", "enrol"];
    const missingFields = requiredFields.filter((field) => !cleanParams[field]);

    if (missingFields.length > 0) {
      console.error("Campos obrigatórios ausentes no serviço:", missingFields);
      throw new Error(
        `Campos obrigatórios ausentes: ${missingFields.join(", ")}`
      );
    }

    const response = await ajax("local_offermanager_add_class", cleanParams);

    return response;
  } catch (error) {
    console.error("Erro ao criar turma:", error);
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca dados de uma turma
 * @param {int} offerclassid
 * @returns {Promise<any>}
 */
export async function getClass(offerclassid) {
  try {
    return await ajax("local_offermanager_get_class", {
      offerclassid,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca dados de um curso
 * @param {int} offercourseid
 * @returns {Promise<any>}
 */
export async function getCourse(offercourseid) {
  try {
    return await ajax("local_offermanager_get_course", {
      offercourseid,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca dados de um curso de oferta
 * @param {int} offercourseid
 * @returns {Promise<any>}
 */
export async function getClasses(offercourseid) {
  try {
    return await ajax("local_offermanager_get_classes", {
      offercourseid,
    });
  } catch (error) {
    console.error("Error fetching:", error);
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Atualiza uma turma
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function updateClass(params) {
  try {
    // Lista de campos aceitos no objeto optional_fields
    const acceptedOptionalFields = [
      "enableenddate",
      "enddate",
      "enablepreenrolment",
      "preenrolmentstartdate",
      "preenrolmentenddate",
      "description",
      "enableenrolperiod",
      "enrolperiod",
      "minusers",
      "maxusers",
      "roleid",
      "enablereenrol",
      "reenrolmentsituations",
      "enableextension",
      "extensionperiod",
      "extensiondaysavailable",
      "extensionmaxrequests",
      "extensionallowedsituations",
    ];

    // Cria um novo objeto com apenas os campos aceitos para atualização
    const cleanParams = {
      offerclassid: params.offerclassid,
      classname: params.classname,
      startdate: params.startdate,
      teachers: params.teachers,
      optional_fields: {},
    };

    // O campo enrol não deve ser enviado na atualização, apenas na criação
    // A API não espera esse parâmetro e retorna erro se ele for enviado

    // Copia apenas os campos aceitos e remove valores undefined, null, 0 ou string vazia
    if (params.optional_fields) {
      acceptedOptionalFields.forEach((field) => {
        if (field in params.optional_fields) {
          // Verifica se o valor não é undefined, null, 0 ou string vazia
          const value = params.optional_fields[field];

          // Campos numéricos que devem ser tratados especialmente
          const numericFields = [
            "enrolperiod",
            "extensionperiod",
            "extensiondaysavailable",
            "extensionmaxrequests",
            "minusers",
            "maxusers",
          ];

          // Se for um campo numérico e o valor for 0, null, undefined ou string vazia, não inclui
          if (numericFields.includes(field)) {
            if (
              value !== 0 &&
              value !== null &&
              value !== undefined &&
              value !== ""
            ) {
              cleanParams.optional_fields[field] = value;
            }
          }
          // Para campos booleanos, sempre inclui
          else if (typeof value === "boolean") {
            cleanParams.optional_fields[field] = value;
          }
          // Para arrays, inclui apenas se não for vazio
          else if (Array.isArray(value)) {
            if (value.length > 0) {
              cleanParams.optional_fields[field] = value;
            }
          }
          // Para outros tipos, inclui apenas se não for null, undefined ou string vazia
          else if (value !== null && value !== undefined && value !== "") {
            cleanParams.optional_fields[field] = value;
          }
        }
      });
    }

    console.log(
      "Campos enviados para a API de atualização:",
      Object.keys(cleanParams.optional_fields)
    );
    console.log(
      "Objeto completo enviado para a API de atualização:",
      cleanParams
    );

    // Remover o campo enrol se existir, pois a API não espera esse parâmetro na atualização
    if ("enrol" in cleanParams) {
      delete cleanParams.enrol;
    }

    const response = await ajax("local_offermanager_update_class", cleanParams);
    return response;
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Exclui uma turma
 * @param {int} offerclassid
 * @returns {Promise<any>}
 */
export async function deleteClass(offerclassid) {
  try {
    return await ajax("local_offermanager_delete_class", {
      offerclassid,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca professores potenciais para turma
 * @param {int} offercourseid
 * @param {int} offercourseid
 * @returns {Promise<any>}
 */
export async function getPotentialTeachers(
  offercourseid,
  offerclassid = 0,
  searchString = "",
  excludedUserids = []
) {
  try {
    const response = await ajax("local_offermanager_get_potential_teachers", {
      offercourseid: offercourseid,
      search_string: searchString,
      offerclassid: offerclassid,
      excluded_userids: excludedUserids,
    });
    return response;
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca lista de situações de matrícula
 * @returns {Promise<any>}
 */
export async function getSituationList() {
  try {
    return await ajax("local_offermanager_get_situation_list", {});
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Duplica uma turma
 * @param {int} offerclassid ID da turma a ser duplicada
 * @param {int} targetoffercourseid ID do curso de destino (obrigatório)
 * @returns {Promise<any>}
 */
export async function duplicateClass(offerclassid, targetoffercourseid) {
  try {
    if (!targetoffercourseid) {
      throw new Error(
        "É necessário especificar um curso de destino para duplicar a turma"
      );
    }

    // Garantir que os IDs sejam números
    const numericOfferClassId = parseInt(offerclassid, 10);
    const numericTargetOfferCourseId = parseInt(targetoffercourseid, 10);

    if (isNaN(numericOfferClassId) || isNaN(numericTargetOfferCourseId)) {
      throw new Error("IDs inválidos para duplicação de turma");
    }

    const params = {
      offerclassid: numericOfferClassId,
      targetoffercourseid: numericTargetOfferCourseId,
    };

    return await ajax("local_offermanager_duplicate_class", params);
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca cursos potenciais para duplicação de turma
 * @param {int} offerclassid ID da turma a ser duplicada
 * @returns {Promise<any>}
 */
export async function getPotentialDuplicationCourses(offerclassid) {
  try {
    return await ajax("local_offermanager_get_duplication_courses", {
      offerclassid: offerclassid,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca papéis disponíveis para o curso
 * @param {int} offercourseid
 * @returns {Promise<any>}
 */
export async function getCourseRoles(offercourseid) {
  try {
    return await ajax("local_offermanager_get_course_roles", {
      offercourseid,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca métodos de inscrição disponíveis
 * @param {boolean} enabled Retornar apenas plugins habilitados
 * @returns {Promise<any>}
 */
export async function getClassMethods(enabled = true) {
  try {
    return await ajax("local_offermanager_get_class_methods", {
      enabled,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Altera o status de uma turma.
 * @param {int} classId
 * @param {boolean} active
 * @returns {Promise<any>}
 */
export async function toggleClassStatus(classId, active) {
  try {
    return await ajax("local_offermanager_set_class_status", {
      id: classId,
      status: active ? 1 : 0,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}
