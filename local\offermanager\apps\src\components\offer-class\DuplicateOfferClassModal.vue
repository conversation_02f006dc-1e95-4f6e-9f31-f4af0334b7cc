<template>
  <div class="modal-backdrop" @click="$emit('close')">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">Duplicar Turma "{{ offerClass?.name }}"</h3>
        <button class="close-button" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <h3 class="section-title">SELECIONAR CURSO</h3>

        <div class="search-section">
          <div class="search-group">
            <Autocomplete
              v-model="selectedCategory"
              :items="categoryOptions"
              label="Categoria"
              placeholder="Pesquisar..."
              :input-max-width="250"
              :loading="loadingCategories"
              :show-filter-tags="false"
              :show-selected-in-input="true"
              :auto-open="true"
              :has-search-icon="true"
              :max-label-length="25"
              :no-results-text="
                categoryOptions.length === 0
                  ? 'Nenhuma categoria disponível'
                  : 'Nenhuma categoria encontrada'
              "
            />
          </div>

          <div class="search-group">
            <Autocomplete
              v-model="selectedCourse"
              :items="targetCourseOptions"
              label="Curso"
              placeholder="Pesquisar..."
              :input-max-width="250"
              :disabled="!selectedCategory"
              :loading="loadingCourses || loadingMoreCourses"
              :auto-open="true"
              :has-search-icon="true"
              :max-label-length="25"
              :no-results-text="courseNoResultsText"
              @select="handleCourseSelect"
              @load-more="loadMoreCourses"
              @search="handleCourseSearch"
              ref="courseAutocomplete"
            />
          </div>
        </div>

        <div class="table-container">
          <div
            v-if="selectedCoursesPreview.length === 0"
            class="empty-preview-message"
          >
            <p>Selecione cursos acima para duplicar a turma</p>
          </div>
          <CustomTable
            v-else
            :headers="tableHeaders"
            :items="filteredCourses"
            :sort-by="sortBy"
            :sort-desc="sortDesc"
            @sort="handleTableSort"
          >
            <template #item-actions="{ item }">
              <div class="action-buttons">
                <button
                  class="btn-action btn-delete"
                  @click="removeCourse(item)"
                  title="Remover da lista"
                >
                  <i class="fa fa-trash fa-fw"></i>
                </button>
              </div>
            </template>
          </CustomTable>
        </div>

        <Pagination
          v-if="selectedCoursesPreview.length > 0"
          v-model:current-page="currentPage"
          v-model:per-page="perPage"
          :total="selectedCoursesPreview.length"
        />
      </div>
      <div class="modal-footer">
        <CustomButton
          variant="primary"
          label="Duplicar"
          :is-loading="loading"
          :disabled="selectedCoursesPreview.length === 0"
          @click="handleConfirm"
        />

        <CustomButton
          variant="secondary"
          label="Cancelar"
          @click="$emit('close')"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Autocomplete from "@/components/Autocomplete.vue";
import CustomTable from "@/components/CustomTable.vue";
import Pagination from "@/components/Pagination.vue";
import CustomButton from "@/components/CustomButton.vue";
import {
  getPotentialDuplicationCourses,
  duplicateClass,
  getCategories,
} from "@/services/offer";

export default {
  name: "DuplicateOfferClassModal",

  components: {
    Autocomplete,
    CustomTable,
    Pagination,
    CustomButton,
  },

  props: {
    /** The offer class object to be duplicated */
    offerClass: {
      type: Object,
      default: null,
    },
    /** The parent course object containing the offer class */
    parentCourse: {
      type: Object,
      default: null,
    },
    /** The ID of the offer containing the class */
    offerId: {
      type: [Number, String],
      default: null,
    },
  },

  emits: ["close", "success", "loading", "error"],

  data() {
    return {
      // Category selection state
      selectedCategory: null,
      categoryOptions: [],
      loadingCategories: false,

      // Course selection state
      selectedCourse: null,
      targetCourseOptions: [],
      selectedCoursesPreview: [],

      // Loading states
      loading: false,
      loadingCourses: false,
      loadingMoreCourses: false,

      // Course pagination
      coursesPage: 1,
      coursesPerPage: 20,
      coursesTotalPages: 1,
      hasMoreCourses: false,

      // Table pagination and sorting
      currentPage: 1,
      perPage: 5,
      sortBy: "label",
      sortDesc: false,
      tableHeaders: [
        { text: "CURSO", value: "label", sortable: true },
        { text: "CATEGORIA", value: "category_name", sortable: true },
        { text: "AÇÕES", value: "actions", sortable: false, align: "right" },
      ],

      // Duplication process state
      duplicatingCourses: false,
      duplicatedCount: 0,
      totalToDuplicate: 0,

      // Existing courses cache
      existingCourses: [],
    };
  },

  computed: {
    /**
     * Returns appropriate text for course autocomplete when no results are found.
     * @returns {string} The no results text based on current state
     */
    courseNoResultsText() {
      if (!this.selectedCategory) {
        return "Selecione uma categoria primeiro";
      }
      if (this.loadingCourses || this.loadingMoreCourses) {
        return "Carregando cursos...";
      }
      return this.targetCourseOptions.length === 0
        ? "Nenhum curso disponível"
        : "Nenhum curso encontrado";
    },

    /**
     * Returns filtered and paginated courses for the preview table.
     * Applies sorting and pagination to the selected courses.
     * @returns {Array} Filtered and sorted courses for current page
     */
    filteredCourses() {
      const startIndex = (this.currentPage - 1) * this.perPage;
      const endIndex = startIndex + this.perPage;

      const sortedCourses = this.getSortedCourses();
      return sortedCourses.slice(startIndex, endIndex);
    },
  },

  watch: {
    /**
     * Resets form and loads categories when offer class changes
     */
    offerClass() {
      this.resetForm();
      this.getCategories();
    },

    /**
     * Resets form and loads categories when parent course changes
     */
    parentCourse() {
      this.resetForm();
      this.getCategories();
    },

    /**
     * Handles category selection change by resetting course options and loading new courses
     * @param {Object} newValue - The newly selected category
     */
    selectedCategory(newValue) {
      this.resetCourseSelection();
      if (newValue?.value) {
        this.getCoursesForCategory(newValue.value);
      }
    },
  },

  async created() {
    this.initializeComponent();
  },

  methods: {
    /**
     * Initializes the component by resetting form and loading categories
     */
    async initializeComponent() {
      this.resetForm();
      await this.getCategories();
    },

    /**
     * Resets all form data to initial state
     */
    resetForm() {
      this.resetCategorySelection();
      this.resetCourseSelection();
      this.resetPagination();
      this.resetDuplicationState();
      this.existingCourses = [];
    },

    /**
     * Resets category selection state
     */
    resetCategorySelection() {
      this.selectedCategory = null;
      this.categoryOptions = [];
      this.loadingCategories = false;
    },

    /**
     * Resets course selection state
     */
    resetCourseSelection() {
      this.selectedCourse = null;
      this.targetCourseOptions = [];
      this.selectedCoursesPreview = [];
      this.loadingCourses = false;
      this.loadingMoreCourses = false;
      this.resetCoursePagination();
    },

    /**
     * Resets course pagination state
     */
    resetCoursePagination() {
      this.coursesPage = 1;
      this.coursesTotalPages = 1;
      this.hasMoreCourses = false;
    },

    /**
     * Resets table pagination state
     */
    resetPagination() {
      this.currentPage = 1;
    },

    /**
     * Resets duplication process state
     */
    resetDuplicationState() {
      this.duplicatingCourses = false;
      this.duplicatedCount = 0;
      this.totalToDuplicate = 0;
    },

    /**
     * Sorts the selected courses based on current sort configuration
     * @returns {Array} Sorted array of courses
     */
    getSortedCourses() {
      return [...this.selectedCoursesPreview].sort((a, b) => {
        const aValue = a[this.sortBy];
        const bValue = b[this.sortBy];

        if (aValue < bValue) return this.sortDesc ? 1 : -1;
        if (aValue > bValue) return this.sortDesc ? -1 : 1;
        return 0;
      });
    },

    /**
     * Fetches and updates the list of categories available for duplication.
     * Maps the response to the format expected by the Autocomplete component.
     * @returns {Promise<void>}
     */
    async getCategories() {
      try {
        this.loadingCategories = true;
        this.categoryOptions = [];

        const response = await getCategories("", this.offerId);

        this.categoryOptions = response.map((category) => ({
          value: category.id,
          label: category.name,
        }));
      } catch (error) {
        this.handleError("Erro ao carregar categorias:", error);
      } finally {
        this.loadingCategories = false;
      }
    },

    /**
     * Fetches and updates the list of courses available for duplication,
     * filtered by category and search text with pagination support.
     * @param {string|number} categoryId - The selected category ID
     * @param {number} page - Page number for pagination
     * @param {boolean} append - Whether to append results or replace them
     * @param {string} searchText - Search term to filter courses
     * @returns {Promise<void>}
     */
    async getCoursesForCategory(
      categoryId,
      page = 1,
      append = false,
      searchText = ""
    ) {
      if (!categoryId || !this.offerClass) return;

      try {
        // Set loading state
        if (page === 1) {
          this.loadingCourses = true;
          if (!append) {
            this.targetCourseOptions = [];
          }
        } else {
          this.loadingMoreCourses = true;
        }

        const response = await getPotentialDuplicationCourses(
          this.offerClass.id
        );

        const filteredCourses = response.filter((course) => {
          // Check if matches search text
          const matchesSearch =
            !searchText ||
            (course.name &&
              course.name.toLowerCase().includes(searchText.toLowerCase())) ||
            (course.fullname &&
              course.fullname.toLowerCase().includes(searchText.toLowerCase()));

          const notAlreadySelected = !this.selectedCoursesPreview.some(
            (c) => parseInt(c.value) === parseInt(course.id)
          );

          const belongsToCategory =
            parseInt(course.categoryid) === parseInt(categoryId);

          const notCurrentCourse =
            parseInt(course.id) !== parseInt(this.parentCourse.courseId);

          return (
            belongsToCategory &&
            notCurrentCourse &&
            matchesSearch &&
            notAlreadySelected
          );
        });

        // Map courses to options format
        const courseOptions = filteredCourses
          .map((course) => {
            return {
              value: course.id,
              label:
                course.name ||
                course.fullname ||
                course.coursename ||
                `Curso ${course.id}`,
              categoryid: course.categoryid,
              category_name: course.category_name,
            };
          })
          .filter((option) => option !== null);

        // Update course options
        if (append) {
          this.targetCourseOptions = [
            ...this.targetCourseOptions,
            ...courseOptions,
          ];
        } else {
          this.targetCourseOptions = courseOptions;
        }

        // Update pagination state
        this.hasMoreCourses = courseOptions.length >= this.coursesPerPage;

        if (page > this.coursesPage) {
          this.coursesPage = page;
        }
      } catch (error) {
        this.handleError("Erro ao carregar cursos da categoria:", error);
        if (!append) {
          this.targetCourseOptions = [];
        }
      } finally {
        // Clear loading state
        if (page === 1) {
          this.loadingCourses = false;
        } else {
          this.loadingMoreCourses = false;
        }
      }
    },

    /**
     * Loads more courses for the next page if available
     * @returns {Promise<void>}
     */
    async loadMoreCourses() {
      if (
        this.hasMoreCourses &&
        !this.loadingMoreCourses &&
        !this.loadingCourses
      ) {
        const nextPage = this.coursesPage + 1;
        await this.getCoursesForCategory(
          this.selectedCategory.value,
          nextPage,
          true
        );
      }
    },

    /**
     * Handles course search by resetting pagination and loading filtered results
     * @param {string} searchText - The search term
     * @returns {Promise<void>}
     */
    async handleCourseSearch(searchText) {
      if (!this.selectedCategory) return;

      this.resetCoursePagination();
      await this.getCoursesForCategory(
        this.selectedCategory.value,
        1,
        false,
        searchText || ""
      );
    },

    /**
     * Handles course selection from autocomplete
     * @param {Object} course - The selected course object
     */
    handleCourseSelect(course) {
      if (
        course &&
        !this.selectedCoursesPreview.some((c) => c.value === course.value)
      ) {
        // Add course to preview list
        this.selectedCoursesPreview.push({
          value: course.value,
          label: course.label,
          categoryid: course.categoryid,
          category_name: course.category_name,
        });

        // Remove course from available options
        this.targetCourseOptions = this.targetCourseOptions.filter(
          (c) => c.value !== course.value
        );

        this.selectedCourse = null;
      }
    },

    /**
     * Removes a course from the preview list and adds it back to available options
     * @param {Object} course - Course to remove
     */
    removeCourse(course) {
      const courseIndex = this.selectedCoursesPreview.findIndex(
        (c) => c.value === course.value
      );
      if (courseIndex !== -1) {
        const removedCourse = this.selectedCoursesPreview.splice(
          courseIndex,
          1
        )[0];
        this.targetCourseOptions.push(removedCourse);
      }
    },

    /**
     * Handles table sorting configuration
     * @param {Object} sortConfig - Sort configuration object
     * @param {string} sortConfig.sortBy - Field to sort by
     * @param {boolean} sortConfig.sortDesc - Whether to sort in descending order
     */
    handleTableSort({ sortBy, sortDesc }) {
      this.sortBy = sortBy;
      this.sortDesc = sortDesc;
    },

    /**
     * Handles error logging and user notification
     * @param {string} message - Error message for logging
     * @param {Error} error - The error object
     */
    handleError(message, error) {
      this.$emit(
        "error",
        "Erro ao carregar dados. Por favor, tente novamente."
      );
    },

    /**
     * Handles the confirmation and execution of the duplication process
     * @returns {Promise<void>}
     */
    async handleConfirm() {
      if (!this.offerClass || this.selectedCoursesPreview.length === 0) return;

      try {
        this.loading = true;
        this.duplicatingCourses = true;
        this.totalToDuplicate = this.selectedCoursesPreview.length;
        this.duplicatedCount = 0;
        this.$emit("loading", true);

        const offerClassName = this.offerClass.name;
        const offerClassId = parseInt(this.offerClass.id, 10);

        const duplicatedResults = [];

        for (const course of this.selectedCoursesPreview) {
          const destinationOfferCourseId = parseInt(course.value, 10);

          try {
            const result = await duplicateClass(
              offerClassId,
              destinationOfferCourseId
            );

            duplicatedResults.push({
              offerClassName,
              targetCourseName: course.label,
              offerClassId,
              targetCourseId: destinationOfferCourseId,
              result,
            });

            this.duplicatedCount++;
          } catch (courseError) {
            this.$emit(
              "error",
              `Erro ao duplicar para o curso ${course.label}: ${courseError.message}`
            );
          }
        }

        if (duplicatedResults.length === 0) {
          throw new Error("Nenhuma turma foi duplicada com sucesso.");
        }

        // Handle success
        this.$emit("success", {
          offerClassName,
          totalSelected: this.totalToDuplicate,
          totalDuplicates: duplicatedResults.length,
          duplicates: duplicatedResults,
        });

        this.resetForm();
        this.$emit("close");
      } catch (error) {
        this.$emit("error", error.message || "Erro ao duplicar turmas.");
      } finally {
        this.duplicatingCourses = false;
        this.loading = false;
        this.$emit("loading", false);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-container {
  background-color: #212529;
  border-radius: 6px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  width: 100%;
  max-width: 800px; // Aumentado para acomodar a tabela
  border: 1px solid #373b3e;
  z-index: 10001;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #343a40;
}

.modal-title {
  margin: 0;
  font-weight: bold;
  font-size: 1.25rem;
  color: #fff;
}

.close-button {
  background: transparent;
  border: none;
  color: #6c757d;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;

  &:hover {
    color: #fff;
  }
}

.modal-body {
  padding: 1rem;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-message {
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.modal-info {
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #adb5bd;
}

.section-title {
  color: var(--primary);
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.search-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.search-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.table-container {
  margin-bottom: 1rem;

  .empty-preview-message {
    background-color: #343a40;
    padding: 2rem;
    text-align: center;
    border-radius: 4px;

    p {
      color: #adb5bd;
      font-style: italic;
      margin: 0;
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.btn-action {
  background-color: transparent;
  border: none;
  color: #fff;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;

  &.btn-delete {
    color: #dc3545;

    &:hover {
      background-color: rgba(220, 53, 69, 0.1);
    }
  }

  i {
    font-size: 1.25rem;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #343a40;
}

.btn-primary {
  background-color: var(--primary);
  color: #fff;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;

  &:disabled {
    opacity: 0.65;
    cursor: not-allowed;
  }
}

.btn-secondary {
  background-color: #6c757d;
  color: #fff;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: #5c636a;
  }
}

.alert-info {
  background-color: #032830;
  border: 1px solid #087990;
  color: #6edff6;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  i {
    font-size: 1rem;
  }
}

.mt-3 {
  margin-top: 1rem;
}

.form-label {
  font-weight: 500;
  color: #e9ecef;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  display: block;
}
</style>
