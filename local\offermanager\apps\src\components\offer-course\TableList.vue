<template>
  <div>
    <FilterRow inline class="courses-filter-row">
      <div class="filters-left-group">
        <FilterGroup>
          <Autocomplete
            v-model="inputFilters.category"
            :items="categoryOptions"
            placeholder="Pesquisar..."
            label="Categoria"
            :input-max-width="218"
            :has-search-icon="true"
            :auto-open="false"
            :show-filter-tags="false"
            :show-selected-in-input="true"
            :no-results-text="
              categoryOptions.length === 0
                ? 'Nenhuma categoria disponível'
                : 'Nenhuma categoria encontrada'
            "
          />
        </FilterGroup>

        <FilterGroup>
          <Autocomplete
            v-model="inputFilters.course"
            :items="courseOptions"
            placeholder="Pesquisar..."
            label="Curso"
            :input-max-width="218"
            :has-search-icon="true"
            :auto-open="true"
            :loading="loadingCourses || loadingMoreCourses"
            :no-results-text="filterCourseNoResultsText"
            @load-more="loadMoreCourses"
            @search="(search) => getCourseOptions(search)"
            ref="courseAutocomplete"
          />
        </FilterGroup>

        <FilterGroup :isCheckbox="true" class="checkbox-filter-group">
          <CustomCheckbox
            v-model="inputFilters.onlyActive"
            id="onlyActive"
            label="Não exibir inativos"
            @change="handleOnlyActiveChange"
          />
        </FilterGroup>

        <!-- Tags de filtro -->
        <FilterTags v-if="inputFilters.course" class="mt-3">
          <FilterTag @remove="removeFilter('course')">
            Curso: {{ inputFilters.course.label }}
          </FilterTag>
        </FilterTags>
      </div>

      <div class="filters-right-group">
        <button
          class="btn btn-primary"
          @click="showAddCourseModalVisible = true"
        >
          Adicionar Curso
        </button>
      </div>
    </FilterRow>

    <!-- Tabela de Cursos -->
    <CollapsibleTable
      :headers="courseTableHeaders"
      :items="offerCourses"
      :sort-by="sortBy"
      :sort-desc="sortDesc"
      @sort="handleTableSort"
      :expandable="true"
    >
      <template #empty-state>
        <div class="empty-state">
          <span class="no-results">{{
            loading ? "Carregando registros..." : "Não existem registros"
          }}</span>
        </div>
      </template>

      <template #item-name="{ item }">
        <span :title="item.name">
          {{
            item.name.length > 50 ? item.name.slice(0, 50) + "..." : item.name
          }}
        </span>
      </template>

      <template #item-status="{ item }">
        <span v-if="item.status">
          <svg
            class="mr-1"
            width="22"
            height="23"
            viewBox="0 0 22 23"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              x="1"
              y="1.39999"
              width="20"
              height="20"
              rx="10"
              fill="white"
            />
            <rect
              x="1"
              y="1.39999"
              width="20"
              height="20"
              rx="10"
              stroke="var(--success)"
              stroke-width="2"
            />
            <path
              d="M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM15.3589 7.34055C15.2329 7.34314 15.1086 7.37093 14.9937 7.42258C14.8788 7.47425 14.7755 7.54884 14.6899 7.64133L10.3491 13.1726L7.73291 10.5544C7.55519 10.3888 7.31954 10.2992 7.07666 10.3034C6.83383 10.3078 6.60191 10.4061 6.43018 10.5779C6.25849 10.7496 6.16005 10.9815 6.15576 11.2243C6.15152 11.4672 6.24215 11.7019 6.40771 11.8796L9.71533 15.1882C9.80438 15.2771 9.91016 15.3472 10.0269 15.3943C10.1436 15.4413 10.2691 15.4649 10.395 15.4626C10.5206 15.4602 10.6446 15.4327 10.7593 15.3816C10.8742 15.3302 10.9782 15.256 11.064 15.1638L16.0532 8.92648C16.2233 8.74961 16.3183 8.51269 16.3159 8.2673C16.3136 8.02207 16.2147 7.78755 16.0415 7.61398H16.0396C15.9503 7.52501 15.844 7.45488 15.7271 7.40793C15.6101 7.36102 15.4849 7.33798 15.3589 7.34055Z"
              fill="var(--success)"
            />
          </svg>
          Ativo
        </span>
        <span v-else>
          <svg
            class="mr-1"
            width="22"
            height="23"
            viewBox="0 0 22 23"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_572_6021)">
              <rect
                x="1"
                y="1.39999"
                width="20"
                height="20"
                rx="10"
                fill="white"
              />
              <path
                d="M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM14.7524 7.02512C14.6703 7.02512 14.5891 7.04157 14.5132 7.07297C14.4373 7.10442 14.3682 7.1506 14.3101 7.20871L11.0024 10.5173L7.69482 7.20871C7.57747 7.09135 7.41841 7.02512 7.25244 7.02512C7.08647 7.02512 6.92742 7.09135 6.81006 7.20871C6.6927 7.32607 6.62646 7.48512 6.62646 7.65109C6.62646 7.81706 6.6927 7.97612 6.81006 8.09348L10.1187 11.4011L6.81006 14.7087C6.75195 14.7668 6.70577 14.8359 6.67432 14.9118C6.64292 14.9877 6.62646 15.069 6.62646 15.1511C6.62646 15.2332 6.64292 15.3145 6.67432 15.3904C6.70577 15.4663 6.75195 15.5354 6.81006 15.5935C6.92742 15.7108 7.08647 15.7771 7.25244 15.7771C7.33456 15.7771 7.41583 15.7606 7.4917 15.7292C7.56762 15.6978 7.63671 15.6516 7.69482 15.5935L11.0024 12.2849L14.3101 15.5935C14.3682 15.6516 14.4373 15.6978 14.5132 15.7292C14.5891 15.7606 14.6703 15.7771 14.7524 15.7771C14.8346 15.7771 14.9158 15.7606 14.9917 15.7292C15.0676 15.6978 15.1367 15.6516 15.1948 15.5935C15.2529 15.5354 15.2991 15.4663 15.3306 15.3904C15.362 15.3145 15.3784 15.2332 15.3784 15.1511C15.3784 15.069 15.362 14.9877 15.3306 14.9118C15.2991 14.8359 15.2529 14.7668 15.1948 14.7087L11.8862 11.4011L15.1948 8.09348C15.2529 8.03537 15.2991 7.96627 15.3306 7.89035C15.362 7.81448 15.3784 7.73321 15.3784 7.65109C15.3784 7.56898 15.362 7.48771 15.3306 7.41183C15.2991 7.33591 15.2529 7.26682 15.1948 7.20871C15.1367 7.1506 15.0676 7.10442 14.9917 7.07297C14.9158 7.04157 14.8346 7.02512 14.7524 7.02512Z"
                fill="var(--danger)"
              />
            </g>
            <rect
              x="1"
              y="1.39999"
              width="20"
              height="20"
              rx="10"
              stroke="var(--danger)"
              stroke-width="2"
            />
            <defs>
              <clipPath id="clip0_572_6021">
                <rect
                  x="1"
                  y="1.39999"
                  width="20"
                  height="20"
                  rx="10"
                  fill="white"
                />
              </clipPath>
            </defs>
          </svg>
          Inativo
        </span>
      </template>

      <template #item-actions="{ item }">
        <div class="action-buttons">
          <button
            class="btn-action btn-add"
            @click="addOfferClass(item)"
            title="Adicionar turma"
          >
            <img :src="icons.plus" alt="Adicionar turma" />
          </button>
          <button
            class="btn-action"
            :class="item.status ? 'btn-deactivate' : 'btn-activate'"
            @click="requestToggleOfferCourseStatus(item)"
            :disabled="(!item.status && !item.canActivate) || !item.canActivate"
            :title="getStatusButtonTitle(item)"
          >
            <i :class="item.status ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
          </button>
          <button
            class="btn-action btn-delete"
            @click="requestDeleteOfferCourse(item)"
            :disabled="!item.canDelete"
            :title="
              item.canDelete ? 'Excluir' : 'Não é possível excluir este curso'
            "
          >
            <i class="fa fa-trash fa-fw"></i>
          </button>
        </div>
      </template>

      <template #expanded-content="{ item }">
        <div class="course-class-container">
          <div class="course-class-header">
            <div class="course-class-col">NOME DA TURMA</div>
            <div class="course-class-col">TIPO DE INSCRIÇÃO</div>
            <div class="course-class-col">Nº DE VAGAS</div>
            <div class="course-class-col">Nº DE INSCRITOS</div>
            <div class="course-class-col">DATA INÍCIO</div>
            <div class="course-class-col">DATA FIM</div>
            <div class="course-class-col">STATUS</div>
            <div class="course-class-col">AÇÕES</div>
          </div>

          <div class="course-class-content">
            <div v-if="item?.offerClasses?.length > 0">
              <div
                class="course-class-row"
                v-for="(courseClass, index) in item.offerClasses"
                :key="index"
              >
                <div class="course-class-col">
                  <span :title="courseClass.name">
                    {{
                      courseClass.name.length > 20
                        ? courseClass.name.slice(0, 20) + "..."
                        : courseClass.name
                    }}
                  </span>
                </div>
                <div class="course-class-col">
                  {{ courseClass.enrolName }}
                </div>
                <div class="course-class-col">{{ courseClass.vacancies }}</div>
                <div class="course-class-col">
                  {{ courseClass.totalEnrolled }}
                </div>
                <div class="course-class-col">
                  {{ courseClass.startDate }}
                </div>
                <div class="course-class-col">
                  {{ courseClass.endDate }}
                </div>
                <div class="course-class-col operational-cycle">
                  <span
                    :class="[
                      'badge',
                      getOperationalCycleClassName(
                        courseClass.operational_cycle
                      ),
                    ]"
                    v-if="courseClass.operational_cycle === 0"
                  >
                    {{ courseClass.operational_cycle_name }}
                  </span>
                </div>
                <div class="course-class-col">
                  <div class="action-buttons">
                    <button
                      class="btn-action btn-edit"
                      @click="editOffer(item)"
                      title="Visualizar"
                    >
                      <svg
                        width="38"
                        height="39"
                        viewBox="0 0 38 39"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18.1875 25.5897C20.04 25.5897 21.5417 24.0629 21.5417 22.1795C21.5417 20.296 20.04 18.7692 18.1875 18.7692C16.3351 18.7692 14.8334 20.296 14.8334 22.1795C14.8334 24.0629 16.3351 25.5897 18.1875 25.5897Z"
                          stroke="white"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M11 29.4872L15.7917 24.6154M11 22.6667V11.9487C11 11.4319 11.2019 10.9362 11.5614 10.5708C11.9208 10.2053 12.4083 10 12.9167 10H20.5833L26.3333 15.8462V27.5385C26.3333 28.0553 26.1314 28.551 25.772 28.9164C25.4125 29.2819 24.925 29.4872 24.4167 29.4872H17.7083"
                          stroke="white"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </button>
                    <button
                      class="btn-action btn-users"
                      @click="goToEnrollments(courseClass)"
                      title="Usuários Matriculados"
                    >
                      <img :src="icons.users" alt="Usuários Matriculados" />
                    </button>
                    <button
                      class="btn-action btn-edit"
                      @click="goToeditOfferClass(courseClass)"
                      title="Editar"
                    >
                      <i class="fas fa-pencil-alt"></i>
                    </button>
                    <button
                      class="btn-action btn-duplicate"
                      @click="duplicateOfferClass(courseClass, item)"
                      title="Duplicar Turma"
                    >
                      <i class="fas fa-copy"></i>
                    </button>
                    <button
                      class="btn-action"
                      :class="
                        courseClass.status ? 'btn-deactivate' : 'btn-activate'
                      "
                      :title="courseClass.status ? 'Inativar' : 'Ativar'"
                      @click="requestToggleOfferClassStatus(courseClass)"
                    >
                      <i
                        :class="
                          courseClass.status ? 'fas fa-eye' : 'fas fa-eye-slash'
                        "
                      ></i>
                    </button>
                    <button
                      class="btn-action btn-delete"
                      @click="removeOfferClass(item, index)"
                      :disabled="!courseClass.canDelete"
                      :title="
                        courseClass.canDelete
                          ? 'Excluir'
                          : 'Não é possível excluir esta turma'
                      "
                    >
                      <i class="fa fa-trash fa-fw"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="empty-course-class">
              <span>Nenhuma turma encontrada para este curso</span>
            </div>
          </div>
        </div>
      </template>
    </CollapsibleTable>

    <Pagination
      ref="pagination"
      v-model:current-page="currentPage"
      v-model:per-page="perPage"
      :total="totalItems"
      @update:current-page="handlePageChange"
    />

    <AddOfferCourseModal
      v-if="offerId"
      v-model="showAddCourseModalVisible"
      :offerId="offerId"
      @confirm="handleAddCourseConfirm"
    />

    <!-- Modal de Confirmação de Inativação de Curso -->
    <ConfirmationModal
      size="md"
      :show="showCourseStatusModal"
      :title="
        selectedOfferCourse?.showOfferClassStatusModal
          ? 'Ao inativar este curso da oferta, o curso e as turmas associadas serão tratados da seguinte forma:'
          : 'Confirmar Ativação'
      "
      :message="
        selectedOfferCourse?.status
          ? ''
          : 'Tem certeza que deseja ativar este curso?'
      "
      :list-items="
        selectedOfferCourse?.status
          ? [
              'O curso não será mais disponibilizados na oferta, mas as turmas e matrículas permanecerão ativas.',
              'Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.',
              'Novos alunos não poderão ser inscritos através da oferta.',
            ]
          : []
      "
      :confirm-button-text="
        selectedOfferCourse?.status ? 'Inativar curso' : 'Ativar'
      "
      cancel-button-text="Cancelar"
      :icon="selectedOfferCourse?.status ? 'warning' : 'question'"
      @close="showCourseStatusModal = false"
      @confirm="toggleOfferCourseStatus"
    />

    <!-- Modal de Confirmação de Exclusão de Curso -->
    <ConfirmationModal
      size="md"
      :show="showDeleteOfferCourseModal"
      title="A exclusão deste curso da instância de oferta é uma ação irreversível"
      message="Ele será desassociado e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?"
      confirm-button-text="Excluir curso"
      cancel-button-text="Cancelar"
      icon="warning"
      @close="showDeleteOfferCourseModal = false"
      @confirm="deleteOfferCourse"
    />

    <!-- Modal de Confirmação de Exclusão de Turma -->
    <ConfirmationModal
      size="md"
      :show="showDeleteOfferClassModal"
      title="A exclusão desta turma é uma ação irreversível"
      message="Todas as configurações realizadas serão excluídas e a turma será removida do curso. Tem certeza de que deseja continuar?"
      confirm-button-text="Excluir Turma"
      cancel-button-text="Cancelar"
      icon="warning"
      @close="showDeleteOfferClassModal = false"
      @confirm="deleteOfferClass"
    />

    <!-- Modal de Confirmação de Inativação de Turma -->
    <ConfirmationModal
      :show="showOfferClassStatusModal"
      size="md"
      :title="
        selectedOfferClass?.status
          ? 'Ao inativar esta turma, as matrículas e o curso associados serão tratados da seguinte forma:'
          : 'Confirmar Ativação'
      "
      :message="
        selectedOfferClass?.status
          ? ''
          : 'Tem certeza que deseja ativar esta turma?'
      "
      :list-items="
        selectedOfferClass?.status
          ? [
              'Se o curso não possuir outra turma disponível, ele não será mais disponibilizado para novos usuários da oferta. No entanto, matrículas já realizadas permanecerão ativas.',
              'Usuários já matriculados manterão o acesso ao curso normalmente até o encerramento da turma ou da sua matrícula.',
              'Novos alunos não poderão ser matriculados através da oferta.',
            ]
          : []
      "
      :confirm-button-text="
        selectedOfferClass?.status ? 'Inativar Turma' : 'Ativar'
      "
      cancel-button-text="Cancelar"
      :icon="selectedOfferClass?.status ? 'warning' : 'question'"
      @close="showOfferClassStatusModal = false"
      @confirm="toggleOfferClassStatus"
    />

    <!-- Modal de Duplicação de Turma -->
    <DuplicateOfferClassModal
      v-if="showDuplicateOfferClassModal"
      :offerClass="offerClassToDuplicate"
      :parentCourse="classToDuplicateParentOfferCourse"
      :offerId="offerId"
      @close="showDuplicateOfferClassModal = false"
      @success="handleDuplicateSuccess"
      @error="showErrorMessage"
    />

    <!-- Modal de Seleção de Tipo de Inscrição -->
    <EnrolTypeModal
      v-if="selectedOfferCourseForClass"
      :show="showEnrolTypeModal"
      :offerCourseId="selectedOfferCourseForClass?.id"
      :offerId="offerId || '0'"
      @close="showEnrolTypeModal = false"
      @confirm="handleEnrolTypeConfirm"
    />

    <LFLoading :is-loading="loading" />

    <Toast
      :show="showToast"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </div>
</template>

<script>
import usersSvg from "@/assets/img/users.svg";
import plusSvg from "@/assets/img/plus.svg";
import ToastMessages from "@/mixins/toastMessages";
import OfferForm from "@/components/offer/Form.vue";
import CustomTable from "@/components/CustomTable.vue";
import CustomSelect from "@/components/CustomSelect.vue";
import CustomInput from "@/components/CustomInput.vue";
import CustomButton from "@/components/CustomButton.vue";
import Pagination from "@/components/Pagination.vue";
import CollapsibleTable from "@/components/CollapsibleTable.vue";
import PageHeader from "@/components/PageHeader.vue";
import BackButton from "@/components/BackButton.vue";
import Autocomplete from "@/components/Autocomplete.vue";
import TextEditor from "@/components/TextEditor.vue";
import CustomCheckbox from "@/components/CustomCheckbox.vue";
import FilterRow from "@/components/FilterRow.vue";
import FilterGroup from "@/components/FilterGroup.vue";
import FilterTag from "@/components/FilterTag.vue";
import FilterTags from "@/components/FilterTags.vue";
import AddOfferCourseModal from "@/components/offer-course/AddOfferCourseModal.vue";
import ConfirmationModal from "@/components/ConfirmationModal.vue";
import DuplicateOfferClassModal from "@/components/offer-class/DuplicateOfferClassModal.vue";
import EnrolTypeModal from "@/components/EnrolTypeModal.vue";
import Toast from "@/components/Toast.vue";
import LFLoading from "@/components/LFLoading.vue";

import {
  toggleCourseStatus,
  toggleClassStatus,
  removeCourseFromOffer,
  getCurrentCourses,
  getCategories,
  searchCurrentCoursesByCategory,
  getClasses,
  deleteClass,
} from "@/services/offer";

export default {
  name: "TableList",

  mixins: [ToastMessages],

  components: {
    OfferForm,
    CustomTable,
    CustomSelect,
    CustomInput,
    CustomButton,
    Pagination,
    CollapsibleTable,
    PageHeader,
    BackButton,
    Autocomplete,
    TextEditor,
    CustomCheckbox,
    FilterRow,
    FilterGroup,
    FilterTag,
    FilterTags,
    AddOfferCourseModal,
    ConfirmationModal,
    Toast,
    DuplicateOfferClassModal,
    EnrolTypeModal,
    LFLoading,
  },

  props: {
    offerId: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {
      icons: {
        users: usersSvg,
        plus: plusSvg,
      },
      showAddCourseModalVisible: false,
      showCourseStatusModal: false,
      showDeleteOfferCourseModal: false,
      offerCourseToDelete: null,
      showDeleteOfferClassModal: false,
      offerClassToDelete: null,
      classParentCourse: null,
      showOfferClassStatusModal: false,
      showDuplicateOfferClassModal: false,
      showEnrolTypeModal: false,
      selectedOfferClass: null,
      offerClassToDuplicate: null,
      classToDuplicateParentOfferCourse: null,
      selectedOfferCourseForClass: null,
      // Opções para os Autocomplete de categoria e curso
      categoryOptions: [],
      courseOptions: [],
      selectedOfferCourse: null,

      loading: false,

      // Filtros
      inputFilters: {
        course: null,
        category: null,
        onlyActive: false,
      },

      offerCourses: [],

      // Paginação
      currentPage: 1,
      perPage: 5,
      totalItems: 0,

      // Ordenação
      sortBy: "id",
      sortDesc: false,

      // Paginação de cursos potenciais
      filterCoursesPage: 1,
      filterCoursesPerPage: 20,
      coursesTotalPages: 1,
      hasMoreCourses: false,
      loadingCourses: false,
      loadingMoreCourses: false,
      filterCourseNoResultsText: "Nenhum curso encontrado",

      // Tabela de cursos
      courseTableHeaders: [
        { text: "NOME DO CURSO", value: "name", sortable: true },
        { text: "CATEGORIA", value: "category", sortable: true },
        { text: "NÚMERO DE TURMAS", value: "courseClassCount", sortable: true },
        { text: "STATUS DO CURSO", value: "status", sortable: true },
        { text: "AÇÕES", value: "actions", sortable: false },
      ],
    };
  },

  watch: {
    async "inputFilters.course"(newValue, oldValue) {
      this.currentPage = 1;

      if (newValue !== null) {
        this.inputFilters.category = null;
      }

      await this.getCourses();
    },

    async "inputFilters.category"(newValue, oldValue) {
      if (newValue === "") {
        this.inputFilters.category = null;
      }

      if (newValue !== null) {
        this.inputFilters.course = null;
      }

      this.currentPage = 1;

      await this.getCourses();
      await this.getCourseOptions();
    },

    async "inputFilters.onlyActive"(newValue, oldValue) {
      this.currentPage = 1;

      this.inputFilters.course = null;

      await this.getCourses();
      await this.getCourseOptions();
    },

    currentPage() {
      this.getCourses();
    },

    perPage() {
      this.currentPage = 1;

      this.getCourses();
    },
  },

  async created() {
    await this.getCourses();
    await this.getCategoryOptions();
    await this.getCourseOptions();
  },

  methods: {
    /**
     * Fetches the courses for the given offer
     *
     * @returns {Promise<void>}
     */
    async getCourses() {
      if (!this.offerId) return;

      try {
        this.loading = true;

        const options = {
          onlyActive: this.inputFilters.onlyActive,
          page: this.currentPage,
          perPage: this.perPage,
          sortBy: this.sortBy,
          sortDesc: this.sortDesc,
        };

        if (this.inputFilters.course) {
          options.courseIds = [this.inputFilters.course.value];
        }

        if (this.inputFilters.category) {
          options.categorySearch = this.inputFilters.category.label;
        }

        const response = await getCurrentCourses(this.offerId, options);

        const {
          page,
          total_pages,
          total_items,
          courses: offerCourses,
        } = response;

        this.currentPage = page;

        const processedCourses = [];

        for (const offerCourse of offerCourses) {
          try {
            const offerClassesResponse = await getClasses(offerCourse.id);

            const offerClasses = offerClassesResponse.map((offerClass) => {
              return {
                ...offerClass,
                enrol: offerClass.enrol || "-",
                enrolName: offerClass.enrol_name || "-",
                vacancies: offerClass.max_users
                  ? offerClass.max_users
                  : "Ilimitado",
                totalEnrolled: offerClass.enrolled_users || 0,
                startDate: this.formatDate(offerClass.startdate),
                endDate: this.formatDate(offerClass.enddate),
                status: !!parseInt(offerClass.status),
                statusName: !!parseInt(offerClass.status) ? "Ativa" : "Inativo",
                canActivate: offerClass.can_activate,
                canDelete: offerClass.can_delete,
              };
            });

            processedCourses.push({
              id: offerCourse.id,
              courseId: offerCourse.courseid,
              name: offerCourse.fullname,
              category: offerCourse.category_name || "-",
              courseClassCount: offerClasses.length,
              status: !!parseInt(offerCourse.status),
              statusName: !!parseInt(offerCourse.status) ? "Ativo" : "Inativo",
              canDelete: offerCourse.can_delete,
              canActivate: offerCourse.can_activate,
              offerClasses,
            });
          } catch (error) {
            console.log(error);
          }
        }

        this.offerCourses = processedCourses;

        this.totalItems = total_items;
      } catch (error) {
        // this.showErrorMessage(error.message);
        this.offerCourses = [];
        this.totalItems = 0;
      } finally {
        this.loading = false;
      }
    },

    /**
     * Gets the category options from the API
     *
     * @returns {Promise<void>}
     */
    async getCategoryOptions() {
      if (!this.offerId) return;

      try {
        this.loading = true;

        const response = await getCategories("", this.offerId);

        this.categoryOptions = response.map((category) => ({
          value: category.id,
          label: category.name,
        }));
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * Gets the course options from the API
     *
     * @param {String}  search - Search term
     * @param {boolean} resetPagination - Reset pagination
     * @returns {Promise<void>}
     */
    async getCourseOptions(search = "", resetPagination = true) {
      if (!this.offerId) return;

      this.loading = true;

      try {
        if (resetPagination) {
          this.filterCoursesPage = 1;
          this.coursesTotalPages = 1;
          this.hasMoreCourses = false;
          this.loadingCourses = true;
          this.courseOptions = [];
        } else {
          this.loadingMoreCourses = true;
        }

        const response = await searchCurrentCoursesByCategory(
          this.offerId,
          this.inputFilters.category?.value,
          search,
          this.inputFilters.course?.value
            ? [this.inputFilters.course.value]
            : [],
          this.inputFilters.onlyActive
        );

        const courseOptions = response.map((course) => ({
          value: course.id || course.courseid,
          label: course.fullname,
        }));

        if (resetPagination) {
          this.courseOptions = courseOptions;
        } else {
          this.courseOptions = [...this.courseOptions, ...courseOptions];
        }

        this.hasMoreCourses = false;

        if (this.courseOptions.length === 0) {
          this.filterCourseNoResultsText =
            "Nenhum curso disponível nesta categoria";
        }
      } catch (error) {
        this.showErrorMessage("Erro ao carregar cursos da categoria.");

        if (resetPagination) {
          this.courseOptions = [];
        }

        this.hasMoreCourses = false;
      } finally {
        if (resetPagination) {
          this.loadingCourses = false;
        } else {
          this.loadingMoreCourses = false;
        }

        this.loading = false;
      }
    },

    /**
     * Returns the CSS class name associated with the specified operational cycle.
     *
     * @param {number} operational_cycle - The operational cycle identifier (e.g., 0, 1, 2).
     * @returns {string} The corresponding CSS class name ("secondary", "primary", "success")
     *                   or an empty string if the cycle is unrecognized.
     */
    getOperationalCycleClassName(operational_cycle) {
      switch (operational_cycle) {
        case 0:
          return "badge-secondary";
        case 1:
          return "badge-primary";
        case 2:
          return "badge-success";
        default:
          return "";
      }
    },

    /**
     * Formata uma data timestamp para o formato DD/MM/YYYY
     * @param {number} timestamp - Timestamp em segundos
     * @returns {string} Data formatada ou '-' se não houver data
     */
    formatDate(timestamp) {
      if (!timestamp) return "-";

      const date = new Date(timestamp * 1000);
      return date.toLocaleDateString("pt-BR");
    },

    clearFilters() {
      this.inputFilters = {
        course: null,
        category: null,
        onlyActive: false,
      };

      this.getCourses();

      this.getCourseOptions();
    },

    async removeFilter(filter) {
      this.inputFilters[filter] = null;
    },

    /**
     * Carrega mais cursos (próxima página) para o scroll infinito
     */
    async loadMoreCourses() {
      if (
        this.hasMoreCourses &&
        !this.loadingMoreCourses &&
        !this.loadingCourses
      ) {
        this.filterCoursesPage += 1;

        if (this.inputFilters.category?.value) {
          await this.getCourseOptions("", false);
        }
      }
    },

    handleOnlyActiveChange() {
      this.currentPage = 1;

      this.getCourses();
    },

    async handleAddCourseConfirm(offerCourses) {
      try {
        this.loading = true;

        await this.getCourses();

        this.showSuccessMessage("Curso(s) adicionado(s) com sucesso à oferta.");
      } catch (error) {
        this.showErrorMessage(
          error.message || "Ocorreu um erro ao adicionar os cursos."
        );
      } finally {
        this.loading = false;
      }
    },

    handleTableSort({ sortBy, sortDesc }) {
      this.sortBy = sortBy;
      this.sortDesc = sortDesc;
      this.getCourses();
    },

    /**
     * Manipula a mudança de página na paginação
     * @param {number} page - Número da página selecionada
     */
    handlePageChange(page) {
      this.currentPage = page;

      this.getCourses();
    },

    addOfferClass(course) {
      this.selectedOfferCourseForClass = course;
      this.showEnrolTypeModal = true;
    },

    handleEnrolTypeConfirm(data) {
      this.showEnrolTypeModal = false;

      this.$router.push({
        name: "NewClass",
        params: {
          offerCourseId: data.offerCourseId,
          offerid: this.offerId,
        },
        query: {
          enrol_type: data.enrolType,
        },
      });
    },

    /**
     * Redirect to edit class page
     * @param {Object} offerClass - Turma a ser editada
     */
    goToeditOfferClass(offerClass) {
      const parentOfferCourse = this.offerCourses.find((OfferCourse) =>
        OfferCourse.offerClasses.some((t) => t.id === offerClass.id)
      );

      this.$router.push({
        name: "offer.class.edit",
        params: {
          classId: offerClass.id,
        },
      });

      // if (parentOfferCourse) {
      //   this.$router.push({
      //     name: "offer.class.edit",
      //     params: {
      //       classId: offerClass.id,
      //     },
      //   });
      // } else {
      //   this.showErrorMessage(
      //     "Não foi possível editar a turma. Curso pai não encontrado."
      //   );
      // }
    },

    requestToggleOfferClassStatus(offerClass) {
      this.selectedOfferClass = {
        ...offerClass,
      };

      this.showOfferClassStatusModal = true;
    },

    /**
     * Confirms and updates the status of the selected offer class.
     */
    async toggleOfferClassStatus() {
      if (!this.selectedOfferClass) return;

      try {
        this.loading = true;
        const courseClassNome = this.selectedOfferClass.name;
        const newStatus = this.selectedOfferClass.status ? false : true;

        // Chamar a API para alterar o status da turma
        await toggleClassStatus(this.selectedOfferClass.id, newStatus);

        // Atualizar o status da turma na lista local
        const courseIndex = this.offerCourses.findIndex((c) =>
          c.offerClasses.some((t) => t.id === this.selectedOfferClass.id)
        );

        if (courseIndex !== -1) {
          const course = this.offerCourses[courseIndex];
          const courseClassIndex = course.offerClasses.findIndex(
            (t) => t.id === this.selectedOfferClass.id
          );

          if (courseClassIndex !== -1) {
            const courseClass = course.offerClasses[courseClassIndex];
            courseClass.status = newStatus;
            courseClass.statusName = newStatus ? "Ativo" : "Inativo";
          }
        }

        await this.getCourses();

        this.showSuccessMessage(
          newStatus
            ? `Turma "${courseClassNome}" ativada com sucesso.`
            : `Turma "${courseClassNome}" inativada com sucesso.`
        );

        this.selectedOfferClass = null;
        this.showOfferClassStatusModal = false;
      } catch (error) {
        //
      } finally {
        this.loading = false;
      }
    },

    removeOfferClass(course, offerClassIndex) {
      const offerClass = course.offerClasses[offerClassIndex];

      if (!offerClass.canDelete) {
        return;
      }

      this.offerClassToDelete = offerClass;
      this.classParentCourse = course;
      this.showDeleteOfferClassModal = true;
    },

    /**
     * Navigates to the enrollments page of the given offer class.
     * @param {Object} offerClass - The offer class to navigate to.
     */
    goToEnrollments(offerClass) {
      this.$router.push({
        name: "Enrollments",
        params: {
          classId: parseInt(offerClass.id),
        },
      });
    },

    // Método para tratar o sucesso da duplicação de turma
    async handleDuplicateSuccess(data) {
      await this.getCourses();

      if (data.totalDuplicates) {
        this.showSuccessMessage(
          `Turma "${data.offerClassName}" duplicada com sucesso para ${data.totalDuplicates} curso(s).`
        );
      }
    },

    duplicateOfferClass(offerClass, parentCourse) {
      this.offerClassToDuplicate = offerClass;
      this.classToDuplicateParentOfferCourse = parentCourse;

      this.showDuplicateOfferClassModal = true;
    },

    /**
     * Confirms and processes the deletion of a offer class.
     *
     */
    async deleteOfferClass() {
      if (!this.offerClassToDelete || !this.classParentCourse) return;

      this.loading = true;

      try {
        const offerClassName = this.offerClassToDelete.name;

        await deleteClass(this.offerClassToDelete.id);

        const turmaIndex = this.classParentCourse.offerClasses.findIndex(
          (t) => t.id === this.offerClassToDelete.id
        );

        if (turmaIndex !== -1) {
          this.classParentCourse.offerClasses.splice(turmaIndex, 1);
          this.classParentCourse.courseClassCount =
            this.classParentCourse.offerClasses.length;
        }

        this.showSuccessMessage(
          `Turma ${offerClassName} excluída com sucesso.`
        );

        this.offerClassToDelete = null;
        this.classParentCourse = null;
        this.showDeleteOfferClassModal = false;
      } catch (error) {
        this.showErrorMessage(error.message || "Erro ao excluir turma.");
      } finally {
        this.loading = false;
      }
    },

    requestToggleOfferCourseStatus(course) {
      if (!course.canActivate) {
        return;
      }
      this.selectedOfferCourse = course;
      this.showCourseStatusModal = true;
    },

    getStatusButtonTitle(item) {
      if (item.status) {
        return item.canActivate
          ? "Inativar"
          : "Não é possível inativar este curso";
      } else {
        return item.canActivate ? "Ativar" : "Não é possível ativar este curso";
      }
    },

    /**
     * Toggles the status of a course in the offer.
     *
     * @param {Object} selectedOfferCourse - The course to toggle the status of.
     */
    async toggleOfferCourseStatus() {
      if (this.selectedOfferCourse) {
        try {
          this.loading = true;
          const newStatus = this.selectedOfferCourse.status ? false : true;
          const courseNome = this.selectedOfferCourse.name;

          const courseId = this.selectedOfferCourse.id;

          await toggleCourseStatus(this.offerId, courseId, newStatus);

          const courseIndex = this.offerCourses.findIndex(
            (c) => c.id === this.selectedOfferCourse.id
          );
          if (courseIndex !== -1) {
            const course = this.offerCourses[courseIndex];
            course.status = newStatus;
            course.statusName = newStatus ? "Ativo" : "Inativo";
          }

          this.showCourseStatusModal = false;
          this.selectedOfferCourse = null;

          await this.getCourses();

          this.showSuccessMessage(
            newStatus
              ? `Curso "${courseNome}" ativado com sucesso.`
              : `Curso "${courseNome}" inativado com sucesso.`
          );
        } catch (error) {
          //
        } finally {
          this.loading = false;
        }
      }
    },

    /**
     * Initiates the process to delete a course from the offer.
     *
     * @param {Object} course - The course to be deleted.
     *
     */
    requestDeleteOfferCourse(course) {
      if (!course.canDelete) {
        return;
      }

      this.offerCourseToDelete = course;
      this.showDeleteOfferCourseModal = true;
    },

    /**
     * Confirms and processes the deletion of a course from the offer.
     */
    async deleteOfferCourse() {
      if (this.offerCourseToDelete) {
        try {
          this.loading = true;

          const courseNome = this.offerCourseToDelete.name;
          const courseId = this.offerCourseToDelete.id;

          await removeCourseFromOffer(this.offerId, courseId);

          this.offerCourses = this.offerCourses.filter(
            (c) => c.id !== this.offerCourseToDelete.id
          );

          this.offerCourseToDelete = null;
          this.showDeleteOfferCourseModal = false;

          await this.getCourses();

          const message = `Curso "${courseNome}" excluído com sucesso.`;
          this.showSuccessMessage(message);
        } catch (error) {
          this.showErrorMessage(error.message || "Erro ao remover curso.");
        } finally {
          this.loading = false;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.empty-state {
  padding: 2rem;
  text-align: center;

  .no-results {
    font-size: 0.875rem;
    color: #6c757d;
  }
}

.course-class-container {
  background-color: #343a40;
}

.course-class-header,
.course-class-row {
  display: flex;
  border-bottom: 1px solid #495057;

  &:last-child {
    border-bottom: none;
  }
}

.course-class-header {
  background-color: #212529;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
}

.course-class-content {
  background-color: #343a40;
}

.course-class-row {
  height: 50px;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.05);

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.empty-course-class {
  padding: 1rem;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

.course-class-col {
  flex: 1;
  padding: 0.75rem 0.5rem;
  font-size: 14px;
  text-align: center;

  &.operational-cycle > .badge {
    border-radius: 3px;
    font-size: 100%;
    text-transform: uppercase;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;

  /* Estilos específicos que sobrescrevem o global.scss */
  .btn-action {
    font-size: 1.25rem;
    color: #fff;

    &.btn-add {
      img {
        width: 20px;
        height: 20px;
      }
    }

    &:disabled {
      background-color: transparent !important;

      &:hover {
        background-color: transparent !important;
      }
    }
  }
}

.btn-activate {
  color: #6c757d !important;

  &:hover {
    background-color: rgba(108, 117, 125, 0.1) !important;
  }

  i {
    opacity: 0.7;
  }
}

.btn-deactivate {
  color: #fff !important;
}

.courses-filter-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 1rem;
}
.filters-left-group {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
  flex-wrap: wrap;

  .mt-3 {
    margin-top: 1rem;
    width: 100%;
  }
}

.filters-right-group {
  display: flex;
  align-items: flex-end;
}
</style>
