<template>
  <div class="autocomplete-container">
    <label
      v-if="label"
      class="filter-label"
      :class="{ required }"
      :id="`${uniqueId}-label`"
      >{{ label }}</label
    >

    <div class="autocomplete-wrapper">
      <div class="input-container" :style="{ maxWidth: inputMaxWidthStyle }">
        <div
          class="input-wrapper"
          :class="{
            'has-search-icon': hasSearchIcon,
            'has-selected-item':
              showSelectedInInput &&
              !Array.isArray(modelValue) &&
              modelValue &&
              !isOpen &&
              !searchQuery,
          }"
        >
          <input
            type="text"
            class="form-control"
            :placeholder="placeholder"
            v-model="searchQuery"
            :disabled="disabled"
            :aria-expanded="isOpen"
            :aria-owns="`${uniqueId}-listbox`"
            :aria-labelledby="label ? `${uniqueId}-label` : undefined"
            :aria-autocomplete="'list'"
            :aria-controls="`${uniqueId}-listbox`"
            role="combobox"
            tabindex="0"
            @keydown="handleKeydown"
            @focus="!disabled && handleFocus"
            @input="handleInput"
            @click="!disabled && openDropdown()"
            @blur="handleBlur"
            ref="inputElement"
          />
          <!-- Exibe o item selecionado no input quando não é um array e não está aberto -->
          <div
            v-if="
              showSelectedInInput &&
              !Array.isArray(modelValue) &&
              modelValue &&
              !isOpen &&
              !searchQuery
            "
            class="selected-item"
          >
            <span class="selected-text" :title="getSelectedItemLabel">{{
              truncateLabel(getSelectedItemLabel)
            }}</span>
            <i
              class="fas fa-times remove-selected"
              @click.stop="removeSelectedItem"
            ></i>
          </div>
          <i
            v-if="
              hasSearchIcon &&
              !(
                showSelectedInInput &&
                !Array.isArray(modelValue) &&
                modelValue &&
                !isOpen &&
                !searchQuery
              )
            "
            class="search-icon"
            :class="{
              'fas fa-search': !loading,
              'spinner-border spinner-border-sm': loading,
            }"
          ></i>
        </div>

        <div
          v-if="isOpen"
          class="dropdown-menu show"
          :id="`${uniqueId}-listbox`"
          role="listbox"
          tabindex="-1"
          ref="dropdownMenu"
          @scroll="handleScroll"
        >
          <template v-if="displayItems.length > 0">
            <div
              v-for="(item, index) in displayItems"
              :key="item.value === '__ALL__' ? '__ALL__' : item.value"
              class="dropdown-item"
              :id="`${uniqueId}-option-${index}`"
              role="option"
              :data-index="index"
              :aria-selected="selectedIndex === index"
              :tabindex="selectedIndex === index ? 0 : -1"
              :class="{
                active: selectedIndex === index,
                selected:
                  item.value !== '__ALL__' &&
                  (Array.isArray(selectedItems)
                    ? selectedItems.some((i) => i.value === item.value)
                    : selectedItems === item.value),
              }"
              @click="selectItem(item)"
              @keydown="handleOptionKeydown($event, item, index)"
              ref="optionElements"
              :title="item.label"
            >
              <span class="item-label">{{ truncateLabel(item.label) }}</span>
              <i
                v-if="
                  item.value !== '__ALL__' &&
                  Array.isArray(selectedItems) &&
                  selectedItems.some((i) => i.value === item.value)
                "
                class="fas fa-check"
              ></i>
            </div>
            <div v-if="loading" class="dropdown-item loading-item">
              <span>Carregando mais itens...</span>
            </div>
          </template>
          <div v-else class="dropdown-item no-results">
            {{ noResultsText || "Nenhum item disponível" }}
          </div>
        </div>
      </div>

      <div
        class="tags-container"
        v-if="
          showFilterTags && Array.isArray(modelValue) && modelValue.length > 0
        "
      >
        <FilterTags>
          <FilterTag
            v-for="item in selectedItems"
            :key="item.value"
            @remove="removeItem(item)"
          >
            {{ item.label }}
          </FilterTag>
        </FilterTags>
      </div>
    </div>
  </div>
</template>

<script>
import { debounce } from "lodash";
import FilterTag from "./FilterTag.vue";
import FilterTags from "./FilterTags.vue";

export default {
  name: "Autocomplete",

  components: {
    FilterTag,
    FilterTags,
  },

  props: {
    modelValue: {
      type: [Array, Object, String, Number],
      default: () => [],
    },
    items: {
      type: Array,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: "",
    },
    label: {
      type: String,
      default: "",
    },
    width: {
      type: [Number, String],
      default: "auto",
    },
    required: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    minChars: {
      type: Number,
      default: 3,
    },
    showAllOption: {
      // Nova prop
      type: Boolean,
      default: false,
    },
    inputMaxWidth: {
      type: [String, Number],
      default: null,
    },
    autoOpen: {
      type: Boolean,
      default: true,
    },
    noResultsText: {
      type: String,
      default: "Nenhum item disponível",
    },
    hasSearchIcon: {
      type: Boolean,
      default: false,
    },
    showFilterTags: {
      type: Boolean,
      default: true,
    },
    showSelectedInInput: {
      type: Boolean,
      default: false,
    },
    maxLabelLength: {
      type: Number,
      default: 30,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    keepOpenOnSelect: {
      type: Boolean,
      default: false,
    },
  },

  emits: ["update:modelValue", "select", "select-all", "load-more", "search"], // Adicionado 'search' para busca por texto

  data() {
    return {
      searchQuery: "",
      isOpen: false,
      selectedIndex: -1,
      internalItems: [],
      uniqueId: `autocomplete-${Math.random().toString(36).substring(2, 9)}`,
      focusedOptionIndex: -1,
      blurTimeout: null,
      debouncedSearch: null, // Nova propriedade para armazenar a função debounce
    };
  },

  computed: {
    // Itens a serem exibidos no dropdown (filtrados + opção "Todos")
    displayItems() {
      let itemsToShow = this.internalItems;

      // Filtrar localmente se houver query
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        itemsToShow = this.internalItems.filter((item) =>
          item.label.toLowerCase().includes(query)
        );
      }

      // Adicionar opção "Todos" no início se habilitada e se for seleção múltipla
      if (this.showAllOption && Array.isArray(this.modelValue)) {
        return [{ label: "Todos", value: "__ALL__" }, ...itemsToShow];
      }

      return itemsToShow;
    },
    // Calcula o valor formatado para o estilo max-width do input
    inputMaxWidthStyle() {
      if (!this.inputMaxWidth) {
        return null; // Não aplica max-width se a prop não for passada
      }
      return typeof this.inputMaxWidth === "number"
        ? `${this.inputMaxWidth}px`
        : this.inputMaxWidth;
    },
    // Retorna o label do item selecionado para exibição no input
    getSelectedItemLabel() {
      if (!this.modelValue) return "";

      return this.modelValue.label;
    },

    selectedItems() {
      if (!Array.isArray(this.modelValue)) return [];

      return this.modelValue.map((item) => {
        if (item.value && item.label !== "") {
          return item;
        }

        const currentItem = this.items.find(
          (i) => i.value === (item.value || item)
        );

        const label = currentItem?.label || "";

        return { value: item.value || item, label: label };
      });
    },
  },

  created() {
    // Criar a função debounce uma única vez
    this.debouncedSearch = debounce((value) => {
      this.$emit("search", value);
    }, 300);
  },

  watch: {
    items: {
      handler(newItems) {
        this.internalItems = Array.isArray(newItems) ? [...newItems] : [];

        // Se tiver itens e autoOpen estiver habilitado, abre o dropdown
        if (
          this.autoOpen &&
          this.keepOpenOnSelect &&
          !this.disabled &&
          this.internalItems.length > 0 &&
          this.$refs.inputElement === document.activeElement
        ) {
          this.isOpen = true;
        }
      },
      immediate: true,
      deep: true,
    },

    searchQuery(newValue) {
      // Mantém o dropdown aberto independentemente do texto digitado
      // Apenas filtra os resultados conforme o usuário digita
      this.isOpen = true;
      this.selectedIndex = -1; // Reseta o índice ao digitar

      if (newValue.length === 0 || newValue.length >= this.minChars) {
        // Chama a função debounce armazenada
        this.debouncedSearch(newValue);
      }
    },

    // Observa os itens internos para atualizar a visibilidade (pode ser removido se displayItems for suficiente)
    // internalItems: {
    //   handler(newItems) {
    //     // A lógica de abertura agora está em handleFocus e searchQuery watcher
    //   },
    //   deep: true
    // }
  },

  methods: {
    handleFocus() {
      // Abre o dropdown ao focar, mostrando todos os itens
      if (this.autoOpen && !this.disabled) {
        this.isOpen = true;
        this.selectedIndex = -1; // Reseta o índice ao focar

        // Limpa a busca quando o campo recebe foco
        if (this.searchQuery) {
          this.searchQuery = "";
          // Emite evento de busca vazia para recarregar a lista inicial
          this.$emit("search", "");
        }
      }

      // Limpa qualquer timeout de blur pendente
      if (this.blurTimeout) {
        clearTimeout(this.blurTimeout);
        this.blurTimeout = null;
      }
    },

    // Método para abrir o dropdown programaticamente
    openDropdown() {
      if (!this.disabled) {
        this.isOpen = true;
      }
    },

    handleBlur() {
      // Usa um timeout para permitir que cliques nos itens do dropdown sejam processados
      // antes de fechar o dropdown
      this.blurTimeout = setTimeout(() => {
        // Verifica se o foco não foi para um dos itens do dropdown
        if (!this.$el.contains(document.activeElement)) {
          this.isOpen = false;
          this.selectedIndex = -1;
        }
      }, 150);
    },

    handleInput() {
      // Chamado no @input para garantir que o dropdown abra/filtre enquanto digita
      if (!this.disabled) {
        this.isOpen = true;
      }
    },

    selectItem(item) {
      // Lida com a seleção da opção "Todos"
      if (item.value === "__ALL__") {
        if (Array.isArray(this.modelValue)) {
          if (this.modelValue.length === this.items.length) {
            this.$emit("update:modelValue", []);
            return;
          }

          this.$emit("update:modelValue", this.items);
          this.$emit("select-all");
        }

        this.searchQuery = "";
        this.isOpen = false;
        this.selectedIndex = -1;

        // Retorna o foco ao input
        this.$nextTick(() => {
          this.focusInput();
        });

        return; // Interrompe a execução aqui para "Todos"
      }

      if (Array.isArray(this.modelValue)) {
        const newValue = [...this.modelValue];
        const index = newValue.findIndex((i) => i.value === item.value);

        if (index === -1) {
          newValue.push(item);
        } else {
          newValue.splice(index, 1);
        }

        this.$emit("update:modelValue", newValue);
      } else {
        this.$emit("update:modelValue", item);
        this.$emit("select", item);
      }

      // Limpa a busca, fecha o dropdown e reseta o índice
      this.searchQuery = "";
      this.isOpen = this.keepOpenOnSelect ? true : false;
      this.selectedIndex = -1;

      // Retorna o foco ao input
      this.$nextTick(() => {
        if (this.autoOpen) {
          this.focusInput();
        }
      });
    },

    removeItem(item) {
      if (Array.isArray(this.modelValue)) {
        const newValue = this.modelValue.filter((i) => i.value !== item.value);
        this.$emit("update:modelValue", newValue);
      } else {
        this.$emit("update:modelValue", "");
      }

      // Limpa a busca e fecha para seleção múltipla
      if (Array.isArray(this.modelValue)) {
        this.searchQuery = "";
        this.isOpen = false;
        this.selectedIndex = -1;
      }
      // Para seleção única, fecha no nextTick para evitar reabertura pelo watcher
      else {
        this.selectedIndex = -1; // Reseta índice
        this.$nextTick(() => {
          this.isOpen = false;
        });
      }

      // Retorna o foco ao input
      this.$nextTick(() => {
        this.focusInput();
      });
    },

    // Método para remover o item selecionado quando não é um array
    removeSelectedItem() {
      this.$emit("update:modelValue", "");
      this.searchQuery = "";
      this.selectedIndex = -1;

      // Retorna o foco ao input
      this.$nextTick(() => {
        this.focusInput();
      });
    },

    handleKeydown(event) {
      if (!this.isOpen && event.key !== "Tab") {
        // Se o dropdown não estiver aberto, abre-o para qualquer tecla exceto Tab
        this.isOpen = true;
        return;
      }

      switch (event.key) {
        case "ArrowDown":
          event.preventDefault();
          event.stopPropagation();
          this.selectedIndex = Math.min(
            this.selectedIndex + 1,
            this.displayItems.length - 1
          );
          this.focusOption(this.selectedIndex);
          break;

        case "ArrowUp":
          event.preventDefault();
          event.stopPropagation();
          this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
          if (this.selectedIndex === -1) {
            this.focusInput();
          } else {
            this.focusOption(this.selectedIndex);
          }
          break;

        case "Enter":
          event.preventDefault();
          if (this.selectedIndex >= 0) {
            this.selectItem(this.displayItems[this.selectedIndex]);
          } else if (
            this.searchQuery &&
            this.searchQuery.length >= this.minChars
          ) {
            // Se o usuário pressionar Enter com texto digitado, mas sem seleção,
            // emite evento de busca para buscar no backend
            this.$emit("search", this.searchQuery);
          }
          break;

        case "Escape":
          event.preventDefault();
          this.isOpen = false;
          this.selectedIndex = -1;
          break;

        case "Tab":
          // Se Tab for pressionado e o dropdown estiver aberto, move o foco para o primeiro item
          if (this.isOpen && !event.shiftKey && this.displayItems.length > 0) {
            event.preventDefault();
            event.stopPropagation();
            this.selectedIndex = 0;
            this.focusOption(0);
          }
          break;
      }
    },

    handleOptionKeydown(event, item, index) {
      switch (event.key) {
        case "ArrowDown":
          event.preventDefault();
          event.stopPropagation();
          if (index < this.displayItems.length - 1) {
            this.selectedIndex = index + 1;
            this.focusOption(this.selectedIndex);
          }
          break;

        case "ArrowUp":
          event.preventDefault();
          event.stopPropagation();
          if (index > 0) {
            this.selectedIndex = index - 1;
            this.focusOption(this.selectedIndex);
          } else {
            // Se estiver no primeiro item, volta para o input
            this.selectedIndex = -1;
            this.focusInput();
          }
          break;

        case "Enter":
        case " ": // Espaço
          event.preventDefault();
          this.selectItem(item);
          break;

        case "Escape":
          event.preventDefault();
          this.isOpen = false;
          this.selectedIndex = -1;
          this.focusInput();
          break;

        case "Tab":
          if (event.shiftKey) {
            // Shift+Tab: move para o item anterior ou volta para o input
            event.preventDefault();
            event.stopPropagation();
            if (index > 0) {
              this.selectedIndex = index - 1;
              this.focusOption(this.selectedIndex);
            } else {
              this.selectedIndex = -1;
              this.focusInput();
            }
          } else {
            // Tab normal: move para o próximo item ou volta para o primeiro
            event.preventDefault();
            event.stopPropagation();
            if (index < this.displayItems.length - 1) {
              this.selectedIndex = index + 1;
              this.focusOption(this.selectedIndex);
            } else {
              // Se for o último item, volta para o primeiro item
              this.selectedIndex = 0;
              this.focusOption(0);
            }
          }
          break;
      }
    },

    focusInput() {
      if (this.$refs.inputElement) {
        this.$refs.inputElement.focus();
      }
    },

    focusOption(index) {
      requestAnimationFrame(() => {
        const el = this.$refs.optionElements?.[index];
        if (el) el.focus();
      });
    },

    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.isOpen = false;
        this.selectedIndex = -1;
      }
    },

    // Método para truncar o texto do label quando exceder o limite de caracteres
    truncateLabel(label) {
      if (!label) return "";
      if (label.length <= this.maxLabelLength) return label;
      return label.substring(0, this.maxLabelLength) + "...";
    },

    // Método para detectar quando o usuário rolou até o final da lista
    handleScroll(event) {
      if (!event || !event.target) return;

      const target = event.target;
      // Verifica se o usuário rolou até próximo do final da lista (dentro de 50px)
      if (
        target.scrollHeight &&
        target.scrollTop !== undefined &&
        target.clientHeight &&
        target.scrollHeight - target.scrollTop - target.clientHeight < 50
      ) {
        // Emite evento para carregar mais itens
        this.$emit("load-more");
      }
    },
  },

  mounted() {
    document.addEventListener("click", this.handleClickOutside);

    // Se autoOpen estiver habilitado, abre o dropdown automaticamente após a montagem
    if (this.autoOpen && !this.disabled && this.internalItems.length > 0) {
      this.$nextTick(() => {
        this.isOpen = true;
      });
    }
  },

  beforeUnmount() {
    document.removeEventListener("click", this.handleClickOutside);
  },
};
</script>

<style lang="scss" scoped>
.autocomplete-container {
  position: relative;
  width: 100%;
}

.filter-label {
  font-size: 14px;
  color: #fff;
  margin-bottom: 8px;
  display: block;

  &.required::after {
    content: "*";
    color: #dc3545;
    margin-left: 4px;
  }
}

.autocomplete-wrapper {
  position: relative;
  width: 100%;
  min-width: 250px; /* Garante a largura mínima consistente */

  .dropdown-item[aria-selected="true"]:before {
    content: unset !important;
  }
}

.input-container {
  position: relative;
  width: 100%;
  min-width: 250px; /* Garante a largura mínima consistente */
  box-sizing: border-box; /* Garante que padding não afete a largura total */
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;

  &.has-search-icon {
    .form-control {
      padding-right: 30px;
    }

    .search-icon {
      position: absolute;
      right: 12px;
      color: #51585e;
      font-size: 16px;
      pointer-events: none;
    }
  }

  &.has-selected-item {
    .form-control {
      opacity: 0;
      position: absolute;
      z-index: -1;
      width: 100%; /* Garante que o input mantenha a largura */
    }

    .selected-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 10px 12px;
      font-size: 15px;
      line-height: 1.5;
      height: 36px;
      color: #fff;
      background-color: #2c3237;
      border: 1px solid #51585e;
      border-radius: 4px;
      min-width: 250px; /* Garante a largura mínima igual ao input original */
      box-sizing: border-box; /* Garante que padding não afete a largura total */

      .selected-text {
        white-space: nowrap; /* Evita quebra de linha */
        overflow: hidden; /* Esconde o texto que não cabe */
        text-overflow: ellipsis; /* Mostra reticências para texto que não cabe */
        flex-grow: 1; /* Permite que o texto ocupe todo o espaço disponível */
        padding-right: 8px; /* Espaço para o ícone de remoção */
      }

      .remove-selected {
        cursor: pointer;
        margin-left: 8px;
        color: #6c757d;
        flex-shrink: 0; /* Impede que o ícone encolha */
        position: relative;
        right: 0;

        &:hover {
          color: #fff;
        }
      }
    }
  }
}

.form-control {
  // width: 100%;
  // padding: 10px 12px;
  // font-size: 15px;
  // line-height: 1.5;
  // color: #fff;
  // background-color: #2c3237 !important;
  // border: 1px solid #51585e;
  // border-radius: 4px;
  // transition: border-color 0.15s ease-in-out;
  // opacity: 0.8;

  &:focus {
    outline: none;
    border-color: #51585e;
    box-shadow: none;
  }

  &::placeholder {
    color: #51585e;
    opacity: 0.7;
  }

  &:disabled {
    background-color: #343a40;
    border-color: #495057;
    opacity: 0.75;
    cursor: not-allowed;
  }
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 9999;
  margin-top: 4px;
  padding: 0.5rem 0;
  background-color: #2c3237;
  border: 1px solid #51585e;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden; /* Impede scroll horizontal */
  min-width: 100%; /* Garante que o dropdown seja pelo menos tão largo quanto o input */
  width: auto; /* Permite que o dropdown seja mais largo que o input se necessário */
  max-width: 350px; /* Aumentado para acomodar mensagens mais longas */

  &.show {
    display: block;
  }
}

.dropdown-item {
  padding: 0.375rem 0.75rem;
  color: #fff;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  outline: none;
  width: 100%;
  box-sizing: border-box;

  .item-label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
    padding-right: 8px;
  }

  &:hover {
    background-color: #343a40;
  }

  &:focus {
    background-color: var(--primary);
    outline: none;
  }

  &.active {
    background-color: var(--primary);
  }

  &.selected {
    background-color: #343a40;

    &:focus {
      background-color: var(--primary);
    }
  }

  &.no-results {
    color: #adb5bd;
    font-style: italic;
    cursor: default;
    white-space: normal;
    word-wrap: break-word;
    text-align: center;
    padding: 10px;

    &:hover {
      background-color: transparent;
    }
  }

  &.loading-item {
    color: #adb5bd;
    font-style: italic;
    text-align: center;
    cursor: default;

    &:hover {
      background-color: transparent;
    }
  }

  i {
    margin-left: 8px;
    font-size: 0.875rem;
    color: var(--primary);
    flex-shrink: 0;
  }
}

.tags-container {
  margin-top: 8px;
  width: 100%;

  :deep(.filter-tags) {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}
</style>
