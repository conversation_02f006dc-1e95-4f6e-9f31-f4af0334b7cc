<template>
  <div>
    <div class="form-row mb-3">
      <div class="form-group">
        <div class="label-container">
          <div class="label-with-help">
            <label class="form-label">Nome da Oferta</label>
            <i
              class="icon fa fa-exclamation-circle text-danger fa-fw"
              title="Obrigatório"
              role="img"
              aria-label="Obrigatório"
            ></i>
          </div>
        </div>
        <div class="input-container">
          <CustomInput
            v-model="localOffer.name"
            placeholder="Oferta 0001"
            :width="280"
            required
            :has-error="formErrors.name.hasError"
            :error-message="formErrors.name.message"
            @validate="validateForm()"
          />
        </div>
      </div>

      <div class="form-group" v-if="typeEnabled">
        <div class="label-container">
          <div class="label-with-help">
            <label class="form-label">Tipo da oferta</label>
          </div>
        </div>
        <div class="input-container">
          <CustomSelect
            v-model="localOffer.type"
            :options="typeOptions"
            :width="280"
          />
        </div>
      </div>

      <div class="form-group" v-if="isEditing">
        <div class="input-container pt-4">
          <CustomCheckbox
            v-model="localOffer.status"
            id="status"
            label="Ativar oferta"
            :confirmBeforeChange="true"
            @request-change="handleStatusChange"
          />
        </div>
      </div>
    </div>

    <div class="form-row mb-3">
      <div class="form-group">
        <div class="label-container">
          <div class="label-with-help">
            <label class="form-label">Público-alvo</label>
            <i
              class="icon fa fa-exclamation-circle text-danger fa-fw"
              title="Obrigatório"
              role="img"
              aria-label="Obrigatório"
            ></i>
            <HelpIcon
              title="Ajuda com público-alvo"
              text="Para atribuir o público-alvo é necessário que o mesmo seja previamente criado no 'Gerenciador de Público-Alvo'.<br><br>
                      Após a exclusão do ‘atributo’ de um público-alvo, os usuários que já estiverem inscritos em um curso permanecerão matriculados, 
                      mantendo acesso ao conteúdo normalmente. <br><br>
                      A exclusão impactará apenas a exibição do curso para novos usuários dentro desse público-alvo."
            />
          </div>
        </div>
        <div class="input-container">
          <Autocomplete
            class="autocomplete-audiences"
            v-model="localOffer.audiences"
            :items="audienceOptions"
            placeholder="Pesquisar público-alvo..."
            :input-max-width="218"
            :required="true"
            :show-all-option="true"
            :has-error="formErrors.audiences.hasError"
            :error-message="formErrors.audiences.message"
            @update:modelValue="validateForm()"
          />
        </div>
      </div>
    </div>

    <div class="form-group text-editor-container">
      <div class="label-container">
        <div class="label-with-help">
          <label class="form-label">Descrição da oferta</label>
        </div>
      </div>
      <div class="limited-width-editor">
        <TextEditor
          v-model="localOffer.description"
          placeholder="Digite a descrição da oferta aqui..."
          :rows="5"
          @update:modelValue="validateForm()"
        />
      </div>
    </div>

    <ConfirmationModal
      :show="showOfferStatusModal"
      size="md"
      title="Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma: "
      list-title="Comportamento para os cursos, turmas e matrículas:"
      :list-items="[
        'Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.',
        'Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.',
        'Novos alunos não poderão ser inscritos através da oferta.',
      ]"
      confirm-button-text="Inativar oferta"
      cancel-button-text="Cancelar"
      icon="warning"
      @close="showOfferStatusModal = false"
      @confirm="confirmInactivateStatus"
    />

    <Toast
      :show="showToast"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </div>
</template>

<script>
import { searchAudiences } from "@/services/offer";

import ToastMessages from "@/mixins/toastMessages";

import Toast from "@/components/Toast.vue";
import HelpIcon from "@/components/HelpIcon.vue";
import TextEditor from "@/components/TextEditor.vue";
import CustomInput from "@/components/CustomInput.vue";
import CustomSelect from "@/components/CustomSelect.vue";
import Autocomplete from "@/components/Autocomplete.vue";
import CustomCheckbox from "@/components/CustomCheckbox.vue";
import ConfirmationModal from "@/components/ConfirmationModal.vue";

export default {
  name: "OfferForm",
  mixins: [ToastMessages],

  components: {
    Toast,
    HelpIcon,
    TextEditor,
    CustomInput,
    CustomSelect,
    Autocomplete,
    CustomCheckbox,
    ConfirmationModal,
  },

  props: {
    offer: {
      type: Object,
      required: true,
    },
    isEditing: {
      type: Boolean,
      required: true,
    },
  },

  emits: ["update:offer", "validate"],

  data() {
    return {
      localOffer: { ...this.offer },

      showOfferStatusModal: false,

      typeOptions: [],
      typeEnabled: false,

      audienceOptions: [],

      formErrors: {
        name: {
          hasError: false,
          message: "Nome da oferta é obrigatório",
        },
        audiences: {
          hasError: false,
          message: "Selecione pelo menos um público-alvo",
        },
      },
    };
  },

  /**
   * Lifecycle hook.
   * Fetches the offer types if the type selection is enabled.
   */
  async created() {
    if (this.typeEnabled) {
      await this.getTypes();
    }

    await this.getAudiences();
  },

  watch: {
    offer: {
      /**
       * Watches for changes in the offer prop and updates the localOffer if necessary.
       * This is needed because the audience prop is an array of objects, and we
       * need to transform it into an array of values.
       * @param {Object} newVal - The new value of offer
       */
      handler(newVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(this.localOffer)) {
          const audiences = newVal?.audiences?.map((audience) => {
            return {
              value: audience,
              label: "",
            };
          });

          this.localOffer = { ...newVal, audiences };
        }
      },
      deep: true,
      immediate: true,
    },

    localOffer: {
      /**
       * When the localOffer changes, it emits an updated offer to the parent component
       * with the audiences transformed into an array of values.
       * @param {Object} newVal - The new value of localOffer
       */
      handler(newVal) {
        const audiences = newVal?.audiences?.map((audience) => audience.value);

        newVal = { ...newVal, audiences };

        if (JSON.stringify(newVal) !== JSON.stringify(this.offer)) {
          this.$emit("update:offer", { ...newVal, audiences });
        }
      },

      deep: true,
    },
  },

  methods: {
    /**
     * Fetches the offer types from the server and loads them into the component.
     */
    async getTypes() {
      try {
        const response = await getTypeOptions();

        const { enabled, types, default: defaultType } = response;

        this.typeEnabled = !!enabled;

        if (enabled && Array.isArray(types)) {
          this.typeOptions = types.map((type) => ({
            value: type,
            label: type.charAt(0).toUpperCase() + type.slice(1),
          }));
        }
      } catch (error) {
        this.showErrorMessage(
          error.message || "Erro ao carregar opções de tipos."
        );
      }
    },

    /**
     * Fetches the audience list from the server and updates the component state.
     */
    async getAudiences() {
      this.loading = true;

      try {
        const response = await searchAudiences("");

        this.audienceOptions = response.map((audience) => ({
          value: audience.id,
          label: audience.name.toUpperCase(),
        }));
      } catch (error) {
        console.log(error);
        this.showErrorMessage("Erro ao carregar públicos-alvo.");
      } finally {
        this.loading = false;
      }
    },

    /**
     * Handles the change in the offer's status.
     *
     * @param {boolean} value - The new status value for the offer.
     */
    handleStatusChange(value) {
      console.log(value);
      if (!value) {
        this.showOfferStatusModal = true;
        return;
      }

      this.localOffer.status = true;
      this.validateForm();
    },

    /**
     * Hides the confirmation modal and sets the offer status to false.
     */
    confirmInactivateStatus() {
      this.showOfferStatusModal = false;
      this.localOffer.status = false;
      this.validateForm();
    },

    /**
     * Validates all form fields and emits a validation event.
     * @returns {boolean} If the form is valid.
     */
    validateForm() {
      let isValid = true;

      Object.keys(this.formErrors).forEach((field) => {
        if (!this.isValidField(field)) {
          isValid = false;
        }
      });

      this.$emit("validate", isValid);

      return isValid;
    },

    /**
     * Validates a specific form field and emits a validation event.
     *
     * @param {string} fieldName - The name of the field to be validated.
     */
    isValidField(fieldName) {
      switch (fieldName) {
        case "name":
          this.formErrors.name.hasError = !this.localOffer.name;
          break;
        case "audiences":
          this.formErrors.audiences.hasError =
            !this.localOffer?.audiences ||
            this.localOffer.audiences.length === 0;
          break;
      }

      return !this.formErrors[fieldName].hasError;
    },
  },
};
</script>

<style lang="scss" scoped>
.label-with-help {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  gap: 2px;
}

.form-label {
  font-size: 14px;
  color: #fff !important;
  margin-bottom: 0;
  margin-right: 2px;
}

.fa-exclamation-circle {
  margin-right: 0;
}

.required-fields-message {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.form-info {
  background-color: #212529;
  color: #fff;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.limited-width-input {
  max-width: 280px;
}

.limited-width-editor {
  max-width: 700px;
}

.label-container {
  margin-bottom: 0.5rem;
}

.input-container {
  width: 100%;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1rem;
  min-width: 280px;
}

.text-editor-container {
  margin-bottom: 1rem;
}
</style>
