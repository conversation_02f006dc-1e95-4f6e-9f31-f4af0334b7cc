import Ajax from "tool_lfxp/ajax";
import Notification from "core/notification";

const ajax = async (method, args) => {
  const request = {
    methodname: method,
    args: Object.assign({}, args),
  };

  try {
    return await Ajax.call([request])[0];
  } catch (e) {
    Notification.exception(e);
    throw e;
  }
};

const requestWithFiles = async (request, files) => {
  try {
    return await Ajax.callWithFiles(request, files);
  } catch (e) {
    Notification.exception(e);
    throw e;
  }
};

export { ajax, requestWithFiles };
