import * as Config from "core/config";
import { createRouter, createWebHistory } from "vue-router";
import OfferList from "../views/offers/List.vue";
import Enrollments from "../views/Enrollments.vue";
import OfferCreate from "../views/offers/Create.vue";
import OfferEdit from "../views/offers/Edit.vue";
import NewClassView from "../views/NewClassView.vue";
import CreateEdit from "../views/offer-classes/CreateEdit.vue";

const basePath = "/local/offermanager/";

const dynamicPath = (() => {
  const host = window.location.host;
  const currentPath = window.location.pathname;
  const relativePath = Config.wwwroot
    .replace(/^https?\:\/\//i, "")
    .replace(host, "")
    .concat(basePath);
  return currentPath.includes("index.php")
    ? relativePath + "index.php"
    : relativePath;
})();

const routes = [
  {
    path: "/",
    name: "offer.index",
    component: OfferList,
    meta: {
      title: "Gerenciar Ofertas",
    },
  },
  {
    path: "/offers/create",
    name: "offer.create",
    component: OfferCreate,
    meta: {
      title: "Nova Oferta",
    },
  },
  {
    path: "/offers/:id/edit",
    name: "offer.edit",
    component: OfferEdit,
    props: true,
    meta: {
      title: "Editar Oferta",
    },
  },
  {
    path: "/offers/classes/create",
    name: "offer.class.create",
    component: CreateEdit,
    props: true,
    meta: {
      title: "Nova Turma",
    },
  },
  {
    path: "/offers/classes/:classId/edit",
    name: "offer.class.edit",
    component: CreateEdit,
    props: true,
    meta: {
      title: "Editar Turma",
    },
  },
  {
    path: "/enrollments/:classId",
    name: "Enrollments",
    component: Enrollments,
    props: true,
    meta: {
      title: "Usuários matriculados",
    },
  },
  {
    path: "/:pathMatch(.*)*",
    redirect: "/",
  },
];

const router = createRouter({
  history: createWebHistory(dynamicPath),
  routes,
  // Configuração para rolar para o topo da página quando uma nova rota é carregada
  scrollBehavior() {
    // Sempre rola para o topo
    return { top: 0 };
  },
});

// Atualiza o título da página quando a rota muda
router.beforeEach((to, from, next) => {
  document.title = to.meta.title || "Gerenciar Ofertas";
  next();
});

// Tratamento de erros de navegação
router.onError((error) => {
  console.error("Erro de navegação:", error);

  // Se o erro for relacionado a uma rota não encontrada, redirecionar para a página inicial
  if (
    error.name === "NavigationDuplicated" ||
    error.message.includes("No match") ||
    error.message.includes("missing required param")
  ) {
    router.push("/");
  }
});

export default router;
