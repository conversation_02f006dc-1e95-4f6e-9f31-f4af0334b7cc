export default {
  data() {
    return {
      showToast: false,
      toastMessage: "",
      toastType: "success",
      toastTimeout: null,
    };
  },
  methods: {
    /**
     * Exibe uma mensagem de toast com tipo e mensagem especificados
     * @param {string} message Mensagem a ser exibida
     * @param {string} type Tipo de mensagem: 'success' ou 'error'
     */
    showToastMessage(message, type = "success") {
      // Limpa timeout anterior se existir
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      this.showToast = false;

      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = type;
        this.showToast = true;

        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },

    /**
     * Atalho para mensagem de sucesso
     * @param {string} message
     */
    showSuccessMessage(message) {
      this.showToastMessage(message, "success");
    },

    /**
     * Atalho para mensagem de erro
     * @param {string} message
     */
    showErrorMessage(message) {
      this.showToastMessage(message, "error");
    },
  },
};
