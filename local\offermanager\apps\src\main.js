import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'

// Importar estilos SCSS
import './assets/scss/main.scss'

// Importar Font Awesome
const loadFontAwesome = () => {
  const link = document.createElement('link')
  link.rel = 'stylesheet'
  link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css'
  document.head.appendChild(link)
}

const init = (selector, options = {}) => {
    // Carregar Font Awesome
    loadFontAwesome()

    const app = createApp(App)
    app.use(createPinia())
    app.use(router)

    // Se uma rota foi passada do PHP, navegar para ela
    // if (options && options.route) {
    //     // Mapear as rotas do PHP para as rotas do Vue Router
    //     const routeMap = {
    //         'new-offer': '/new-offer',
    //         'edit-offer': '/edit-offer',
    //         'new-subscribed-users': '/new-subscribed-users',
    //         'new-class': '/new-class',
    //         'edit-class': '/edit-class'
    //         // Adicionar mais mapeamentos conforme necessário
    //     }

    //     let targetRoute = routeMap[options.route] || '/'

    //     // Se temos um ID de oferta (para edição), adicionar ao caminho
    //     if (options.route === 'edit-offer' && options.offerId) {
    //         targetRoute = `/edit-offer/${options.offerId}`
    //     }

    //     if (options.route === 'new-subscribed-users' && options.subscribeId) {
    //         targetRoute = `/new-subscribed-users/${options.subscribeId}`
    //     }

    //     if (options.route === 'new-class' && options.offercourseid) {
    //         targetRoute = `/new-class/${options.offercourseid}`
    //     }

    //     if (options.route === 'edit-class' && options.offercourseid && options.classid) {
    //         targetRoute = `/edit-class/${options.offercourseid}/${options.classid}`
    //     }

    //     router.replace(targetRoute)
    // }

    app.mount(selector)
    return app
}

export default { init }