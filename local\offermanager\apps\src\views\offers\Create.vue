<template>
  <div id="create-offer-component" class="create-offer">
    <PageHeader title="Adicionar Oferta">
      <template #actions>
        <BackButton @click="goBack" />
      </template>
    </PageHeader>

    <div class="alert alert-primary" v-if="showWarning">
      <i class="fas fa-exclamation-triangle"></i>
      Para que uma instância de oferta seja ativada e disponibilize os cursos
      para os públicos-alvo configurados, é necessário garantir que pelo menos
      um curso, um grupo de público-alvo, e uma turma estejam configurados à
      instância de oferta.
    </div>

    <!-- Configurações Gerais -->
    <div class="section-container mt-3">
      <h2 class="section-title">CONFIGURAÇÕES GERAIS</h2>

      <OfferForm
        v-model:offer="offer"
        :offer="offer"
        :isEditing="isEditing"
        @validate="isValidForm = $event"
      />
    </div>

    <!-- Cursos -->
    <div class="section-container" :class="{ 'no-title-section': !isEditing }">
      <div class="message-container" v-if="!isEditing">
        <div class="lock-message">
          <i class="fas fa-lock lock-icon"></i>
          <span>Salve a oferta primeiro para gerenciar os cursos</span>
        </div>
      </div>
    </div>

    <hr />

    <div class="d-flex justify-content-between align-items-center">
      <div class="required-fields-message">
        <div class="form-info">
          Este formulário contém campos obrigatórios marcados com
          <i class="fa fa-exclamation-circle text-danger"></i>
        </div>
      </div>
      <div class="actions-container offer-actions">
        <CustomButton
          variant="primary"
          label="Salvar"
          @click="saveOffer"
          :disabled="!isValidForm"
        />
        <CustomButton variant="secondary" label="Cancelar" @click="goBack" />
      </div>
    </div>

    <LFLoading :is-loading="loading" />

    <Toast
      :show="showToast"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </div>
</template>

<script>
import Toast from "@/components/Toast.vue";
import LFLoading from "@/components/LFLoading.vue";
import ToastMessages from "@/mixins/toastMessages";
import PageHeader from "@/components/PageHeader.vue";
import BackButton from "@/components/BackButton.vue";
import CustomButton from "@/components/CustomButton.vue";
import OfferForm from "@/components/offer/Form.vue";

import { saveOffer } from "@/services/offer";

export default {
  name: "OfferEdit",

  mixins: [ToastMessages],

  components: {
    Toast,
    LFLoading,
    OfferForm,
    PageHeader,
    BackButton,
    CustomButton,
  },

  data() {
    return {
      loading: false,

      offer: {
        id: null,
        name: "",
        type: "",
        description: "",
        status: false,
        audiences: [],
      },

      showWarning: true,
      isEditing: false,

      isValidForm: false,
    };
  },

  methods: {
    /**
     * Saves an offer by sending the offer data to the server.
     */
    async saveOffer() {
      this.loading = true;

      if (!this.isValidForm) {
        this.loading = false;
        return;
      }

      try {
        const response = await saveOffer(this.offer);

        if (response.id) {
          const offerId = response.id;

          this.showSuccessMessage("Oferta criada com sucesso!");

          this.$router.push({
            name: "offer.edit",
            params: {
              id: offerId,
            },
          });
        }
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * Redirects the user back to the list of offers.
     */
    goBack() {
      this.$router.push({ name: "offer.index" });
    },
  },
};
</script>

<style lang="scss" scoped></style>
