define("local_offermanager/app/app-lazy",["core/config","tool_lfxp/ajax","core/notification"],function(kg,Vg,Rg){"use strict";function Fg(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const s in e)if(s!=="default"){const i=Object.getOwnPropertyDescriptor(e,s);Object.defineProperty(t,s,i.get?i:{enumerable:!0,get:()=>e[s]})}}return t.default=e,Object.freeze(t)}const Lg=Fg(kg);/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function rn(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const tt={}.NODE_ENV!=="production"?Object.freeze({}):{},Or={}.NODE_ENV!=="production"?Object.freeze([]):[],Ot=()=>{},Ug=()=>!1,oo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),li=e=>e.startsWith("onUpdate:"),ht=Object.assign,Ya=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},Bg=Object.prototype.hasOwnProperty,Ze=(e,t)=>Bg.call(e,t),me=Array.isArray,Jn=e=>io(e)==="[object Map]",Sr=e=>io(e)==="[object Set]",Mc=e=>io(e)==="[object Date]",Oe=e=>typeof e=="function",dt=e=>typeof e=="string",Ns=e=>typeof e=="symbol",Je=e=>e!==null&&typeof e=="object",Ja=e=>(Je(e)||Oe(e))&&Oe(e.then)&&Oe(e.catch),Pc=Object.prototype.toString,io=e=>Pc.call(e),Qa=e=>io(e).slice(8,-1),kc=e=>io(e)==="[object Object]",Xa=e=>dt(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ao=rn(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),$g=rn("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),ui=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Hg=/-(\w)/g,jt=ui(e=>e.replace(Hg,(t,s)=>s?s.toUpperCase():"")),qg=/\B([A-Z])/g,Tn=ui(e=>e.replace(qg,"-$1").toLowerCase()),Qn=ui(e=>e.charAt(0).toUpperCase()+e.slice(1)),Xn=ui(e=>e?`on${Qn(e)}`:""),Nn=(e,t)=>!Object.is(e,t),Tr=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},lo=(e,t,s,i=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:i,value:s})},ci=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Wg=e=>{const t=dt(e)?Number(e):NaN;return isNaN(t)?e:t};let Vc;const uo=()=>Vc||(Vc=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function as(e){if(me(e)){const t={};for(let s=0;s<e.length;s++){const i=e[s],r=dt(i)?Kg(i):as(i);if(r)for(const a in r)t[a]=r[a]}return t}else if(dt(e)||Je(e))return e}const jg=/;(?![^(]*\))/g,zg=/:([^]+)/,Gg=/\/\*[^]*?\*\//g;function Kg(e){const t={};return e.replace(Gg,"").split(jg).forEach(s=>{if(s){const i=s.split(zg);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function fe(e){let t="";if(dt(e))t=e;else if(me(e))for(let s=0;s<e.length;s++){const i=fe(e[s]);i&&(t+=i+" ")}else if(Je(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Zg="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",Yg="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",Jg="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",Qg=rn(Zg),Xg=rn(Yg),e1=rn(Jg),t1=rn("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Rc(e){return!!e||e===""}function s1(e,t){if(e.length!==t.length)return!1;let s=!0;for(let i=0;s&&i<e.length;i++)s=co(e[i],t[i]);return s}function co(e,t){if(e===t)return!0;let s=Mc(e),i=Mc(t);if(s||i)return s&&i?e.getTime()===t.getTime():!1;if(s=Ns(e),i=Ns(t),s||i)return e===t;if(s=me(e),i=me(t),s||i)return s&&i?s1(e,t):!1;if(s=Je(e),i=Je(t),s||i){if(!s||!i)return!1;const r=Object.keys(e).length,a=Object.keys(t).length;if(r!==a)return!1;for(const u in e){const d=e.hasOwnProperty(u),h=t.hasOwnProperty(u);if(d&&!h||!d&&h||!co(e[u],t[u]))return!1}}return String(e)===String(t)}function el(e,t){return e.findIndex(s=>co(s,t))}const Fc=e=>!!(e&&e.__v_isRef===!0),W=e=>dt(e)?e:e==null?"":me(e)||Je(e)&&(e.toString===Pc||!Oe(e.toString))?Fc(e)?W(e.value):JSON.stringify(e,Lc,2):String(e),Lc=(e,t)=>Fc(t)?Lc(e,t.value):Jn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[i,r],a)=>(s[tl(i,a)+" =>"]=r,s),{})}:Sr(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>tl(s))}:Ns(t)?tl(t):Je(t)&&!me(t)&&!kc(t)?String(t):t,tl=(e,t="")=>{var s;return Ns(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function qs(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let ts;class Uc{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ts,!t&&ts&&(this.index=(ts.scopes||(ts.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=ts;try{return ts=this,t()}finally{ts=s}}else({}).NODE_ENV!=="production"&&qs("cannot run an inactive effect scope.")}on(){++this._on===1&&(this.prevScope=ts,ts=this)}off(){this._on>0&&--this._on===0&&(ts=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,i;for(s=0,i=this.effects.length;s<i;s++)this.effects[s].stop();for(this.effects.length=0,s=0,i=this.cleanups.length;s<i;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,i=this.scopes.length;s<i;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function n1(e){return new Uc(e)}function r1(){return ts}let st;const sl=new WeakSet;class Bc{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ts&&ts.active&&ts.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,sl.has(this)&&(sl.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Hc(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Gc(this),qc(this);const t=st,s=Is;st=this,Is=!0;try{return this.fn()}finally{({}).NODE_ENV!=="production"&&st!==this&&qs("Active effect was not restored correctly - this is likely a Vue internal bug."),Wc(this),st=t,Is=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)il(t);this.deps=this.depsTail=void 0,Gc(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?sl.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ol(this)&&this.run()}get dirty(){return ol(this)}}let $c=0,fo,ho;function Hc(e,t=!1){if(e.flags|=8,t){e.next=ho,ho=e;return}e.next=fo,fo=e}function nl(){$c++}function rl(){if(--$c>0)return;if(ho){let t=ho;for(ho=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;fo;){let t=fo;for(fo=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(i){e||(e=i)}t=s}}if(e)throw e}function qc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Wc(e){let t,s=e.depsTail,i=s;for(;i;){const r=i.prevDep;i.version===-1?(i===s&&(s=r),il(i),o1(i)):t=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=r}e.deps=t,e.depsTail=s}function ol(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(jc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function jc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===po)||(e.globalVersion=po,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!ol(e))))return;e.flags|=2;const t=e.dep,s=st,i=Is;st=e,Is=!0;try{qc(e);const r=e.fn(e._value);(t.version===0||Nn(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{st=s,Is=i,Wc(e),e.flags&=-3}}function il(e,t=!1){const{dep:s,prevSub:i,nextSub:r}=e;if(i&&(i.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=i,e.nextSub=void 0),{}.NODE_ENV!=="production"&&s.subsHead===e&&(s.subsHead=r),s.subs===e&&(s.subs=i,!i&&s.computed)){s.computed.flags&=-5;for(let a=s.computed.deps;a;a=a.nextDep)il(a,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function o1(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Is=!0;const zc=[];function As(){zc.push(Is),Is=!1}function Ms(){const e=zc.pop();Is=e===void 0?!0:e}function Gc(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=st;st=void 0;try{t()}finally{st=s}}}let po=0;class i1{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class al{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0,{}.NODE_ENV!=="production"&&(this.subsHead=void 0)}track(t){if(!st||!Is||st===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==st)s=this.activeLink=new i1(st,this),st.deps?(s.prevDep=st.depsTail,st.depsTail.nextDep=s,st.depsTail=s):st.deps=st.depsTail=s,Kc(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const i=s.nextDep;i.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=i),s.prevDep=st.depsTail,s.nextDep=void 0,st.depsTail.nextDep=s,st.depsTail=s,st.deps===s&&(st.deps=i)}return{}.NODE_ENV!=="production"&&st.onTrack&&st.onTrack(ht({effect:st},t)),s}trigger(t){this.version++,po++,this.notify(t)}notify(t){nl();try{if({}.NODE_ENV!=="production")for(let s=this.subsHead;s;s=s.nextSub)s.sub.onTrigger&&!(s.sub.flags&8)&&s.sub.onTrigger(ht({effect:s.sub},t));for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{rl()}}}function Kc(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let i=t.deps;i;i=i.nextDep)Kc(i)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),{}.NODE_ENV!=="production"&&e.dep.subsHead===void 0&&(e.dep.subsHead=e),e.dep.subs=e}}const ll=new WeakMap,er=Symbol({}.NODE_ENV!=="production"?"Object iterate":""),ul=Symbol({}.NODE_ENV!=="production"?"Map keys iterate":""),mo=Symbol({}.NODE_ENV!=="production"?"Array iterate":"");function St(e,t,s){if(Is&&st){let i=ll.get(e);i||ll.set(e,i=new Map);let r=i.get(s);r||(i.set(s,r=new al),r.map=i,r.key=s),{}.NODE_ENV!=="production"?r.track({target:e,type:t,key:s}):r.track()}}function Ws(e,t,s,i,r,a){const u=ll.get(e);if(!u){po++;return}const d=h=>{h&&({}.NODE_ENV!=="production"?h.trigger({target:e,type:t,key:s,newValue:i,oldValue:r,oldTarget:a}):h.trigger())};if(nl(),t==="clear")u.forEach(d);else{const h=me(e),_=h&&Xa(s);if(h&&s==="length"){const p=Number(i);u.forEach((g,w)=>{(w==="length"||w===mo||!Ns(w)&&w>=p)&&d(g)})}else switch((s!==void 0||u.has(void 0))&&d(u.get(s)),_&&d(u.get(mo)),t){case"add":h?_&&d(u.get("length")):(d(u.get(er)),Jn(e)&&d(u.get(ul)));break;case"delete":h||(d(u.get(er)),Jn(e)&&d(u.get(ul)));break;case"set":Jn(e)&&d(u.get(er));break}}rl()}function Nr(e){const t=Me(e);return t===e?t:(St(t,"iterate",mo),zt(e)?t:t.map(Ft))}function di(e){return St(e=Me(e),"iterate",mo),e}const a1={__proto__:null,[Symbol.iterator](){return cl(this,Symbol.iterator,Ft)},concat(...e){return Nr(this).concat(...e.map(t=>me(t)?Nr(t):t))},entries(){return cl(this,"entries",e=>(e[1]=Ft(e[1]),e))},every(e,t){return on(this,"every",e,t,void 0,arguments)},filter(e,t){return on(this,"filter",e,t,s=>s.map(Ft),arguments)},find(e,t){return on(this,"find",e,t,Ft,arguments)},findIndex(e,t){return on(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return on(this,"findLast",e,t,Ft,arguments)},findLastIndex(e,t){return on(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return on(this,"forEach",e,t,void 0,arguments)},includes(...e){return dl(this,"includes",e)},indexOf(...e){return dl(this,"indexOf",e)},join(e){return Nr(this).join(e)},lastIndexOf(...e){return dl(this,"lastIndexOf",e)},map(e,t){return on(this,"map",e,t,void 0,arguments)},pop(){return go(this,"pop")},push(...e){return go(this,"push",e)},reduce(e,...t){return Zc(this,"reduce",e,t)},reduceRight(e,...t){return Zc(this,"reduceRight",e,t)},shift(){return go(this,"shift")},some(e,t){return on(this,"some",e,t,void 0,arguments)},splice(...e){return go(this,"splice",e)},toReversed(){return Nr(this).toReversed()},toSorted(e){return Nr(this).toSorted(e)},toSpliced(...e){return Nr(this).toSpliced(...e)},unshift(...e){return go(this,"unshift",e)},values(){return cl(this,"values",Ft)}};function cl(e,t,s){const i=di(e),r=i[t]();return i!==e&&!zt(e)&&(r._next=r.next,r.next=()=>{const a=r._next();return a.value&&(a.value=s(a.value)),a}),r}const l1=Array.prototype;function on(e,t,s,i,r,a){const u=di(e),d=u!==e&&!zt(e),h=u[t];if(h!==l1[t]){const g=h.apply(e,a);return d?Ft(g):g}let _=s;u!==e&&(d?_=function(g,w){return s.call(this,Ft(g),w,e)}:s.length>2&&(_=function(g,w){return s.call(this,g,w,e)}));const p=h.call(u,_,i);return d&&r?r(p):p}function Zc(e,t,s,i){const r=di(e);let a=s;return r!==e&&(zt(e)?s.length>3&&(a=function(u,d,h){return s.call(this,u,d,h,e)}):a=function(u,d,h){return s.call(this,u,Ft(d),h,e)}),r[t](a,...i)}function dl(e,t,s){const i=Me(e);St(i,"iterate",mo);const r=i[t](...s);return(r===-1||r===!1)&&_i(s[0])?(s[0]=Me(s[0]),i[t](...s)):r}function go(e,t,s=[]){As(),nl();const i=Me(e)[t].apply(e,s);return rl(),Ms(),i}const u1=rn("__proto__,__v_isRef,__isVue"),Yc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ns));function c1(e){Ns(e)||(e=String(e));const t=Me(this);return St(t,"has",e),t.hasOwnProperty(e)}class Jc{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,i){if(s==="__v_skip")return t.__v_skip;const r=this._isReadonly,a=this._isShallow;if(s==="__v_isReactive")return!r;if(s==="__v_isReadonly")return r;if(s==="__v_isShallow")return a;if(s==="__v_raw")return i===(r?a?rd:nd:a?sd:td).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const u=me(t);if(!r){let h;if(u&&(h=a1[s]))return h;if(s==="hasOwnProperty")return c1}const d=Reflect.get(t,s,Ct(t)?t:i);return(Ns(s)?Yc.has(s):u1(s))||(r||St(t,"get",s),a)?d:Ct(d)?u&&Xa(s)?d:d.value:Je(d)?r?id(d):mi(d):d}}class Qc extends Jc{constructor(t=!1){super(!1,t)}set(t,s,i,r){let a=t[s];if(!this._isShallow){const h=zs(a);if(!zt(i)&&!zs(i)&&(a=Me(a),i=Me(i)),!me(t)&&Ct(a)&&!Ct(i))return h?!1:(a.value=i,!0)}const u=me(t)&&Xa(s)?Number(s)<t.length:Ze(t,s),d=Reflect.set(t,s,i,Ct(t)?t:r);return t===Me(r)&&(u?Nn(i,a)&&Ws(t,"set",s,i,a):Ws(t,"add",s,i)),d}deleteProperty(t,s){const i=Ze(t,s),r=t[s],a=Reflect.deleteProperty(t,s);return a&&i&&Ws(t,"delete",s,void 0,r),a}has(t,s){const i=Reflect.has(t,s);return(!Ns(s)||!Yc.has(s))&&St(t,"has",s),i}ownKeys(t){return St(t,"iterate",me(t)?"length":er),Reflect.ownKeys(t)}}class Xc extends Jc{constructor(t=!1){super(!0,t)}set(t,s){return{}.NODE_ENV!=="production"&&qs(`Set operation on key "${String(s)}" failed: target is readonly.`,t),!0}deleteProperty(t,s){return{}.NODE_ENV!=="production"&&qs(`Delete operation on key "${String(s)}" failed: target is readonly.`,t),!0}}const d1=new Qc,f1=new Xc,h1=new Qc(!0),p1=new Xc(!0),fl=e=>e,fi=e=>Reflect.getPrototypeOf(e);function m1(e,t,s){return function(...i){const r=this.__v_raw,a=Me(r),u=Jn(a),d=e==="entries"||e===Symbol.iterator&&u,h=e==="keys"&&u,_=r[e](...i),p=s?fl:t?vi:Ft;return!t&&St(a,"iterate",h?ul:er),{next(){const{value:g,done:w}=_.next();return w?{value:g,done:w}:{value:d?[p(g[0]),p(g[1])]:p(g),done:w}},[Symbol.iterator](){return this}}}}function hi(e){return function(...t){if({}.NODE_ENV!=="production"){const s=t[0]?`on key "${t[0]}" `:"";qs(`${Qn(e)} operation ${s}failed: target is readonly.`,Me(this))}return e==="delete"?!1:e==="clear"?void 0:this}}function g1(e,t){const s={get(r){const a=this.__v_raw,u=Me(a),d=Me(r);e||(Nn(r,d)&&St(u,"get",r),St(u,"get",d));const{has:h}=fi(u),_=t?fl:e?vi:Ft;if(h.call(u,r))return _(a.get(r));if(h.call(u,d))return _(a.get(d));a!==u&&a.get(r)},get size(){const r=this.__v_raw;return!e&&St(Me(r),"iterate",er),Reflect.get(r,"size",r)},has(r){const a=this.__v_raw,u=Me(a),d=Me(r);return e||(Nn(r,d)&&St(u,"has",r),St(u,"has",d)),r===d?a.has(r):a.has(r)||a.has(d)},forEach(r,a){const u=this,d=u.__v_raw,h=Me(d),_=t?fl:e?vi:Ft;return!e&&St(h,"iterate",er),d.forEach((p,g)=>r.call(a,_(p),_(g),u))}};return ht(s,e?{add:hi("add"),set:hi("set"),delete:hi("delete"),clear:hi("clear")}:{add(r){!t&&!zt(r)&&!zs(r)&&(r=Me(r));const a=Me(this);return fi(a).has.call(a,r)||(a.add(r),Ws(a,"add",r,r)),this},set(r,a){!t&&!zt(a)&&!zs(a)&&(a=Me(a));const u=Me(this),{has:d,get:h}=fi(u);let _=d.call(u,r);_?{}.NODE_ENV!=="production"&&ed(u,d,r):(r=Me(r),_=d.call(u,r));const p=h.call(u,r);return u.set(r,a),_?Nn(a,p)&&Ws(u,"set",r,a,p):Ws(u,"add",r,a),this},delete(r){const a=Me(this),{has:u,get:d}=fi(a);let h=u.call(a,r);h?{}.NODE_ENV!=="production"&&ed(a,u,r):(r=Me(r),h=u.call(a,r));const _=d?d.call(a,r):void 0,p=a.delete(r);return h&&Ws(a,"delete",r,void 0,_),p},clear(){const r=Me(this),a=r.size!==0,u={}.NODE_ENV!=="production"?Jn(r)?new Map(r):new Set(r):void 0,d=r.clear();return a&&Ws(r,"clear",void 0,void 0,u),d}}),["keys","values","entries",Symbol.iterator].forEach(r=>{s[r]=m1(r,e,t)}),s}function pi(e,t){const s=g1(e,t);return(i,r,a)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?i:Reflect.get(Ze(s,r)&&r in i?s:i,r,a)}const _1={get:pi(!1,!1)},v1={get:pi(!1,!0)},b1={get:pi(!0,!1)},y1={get:pi(!0,!0)};function ed(e,t,s){const i=Me(s);if(i!==s&&t.call(e,i)){const r=Qa(e);qs(`Reactive ${r} contains both the raw and reactive versions of the same object${r==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const td=new WeakMap,sd=new WeakMap,nd=new WeakMap,rd=new WeakMap;function w1(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function E1(e){return e.__v_skip||!Object.isExtensible(e)?0:w1(Qa(e))}function mi(e){return zs(e)?e:gi(e,!1,d1,_1,td)}function od(e){return gi(e,!1,h1,v1,sd)}function id(e){return gi(e,!0,f1,b1,nd)}function js(e){return gi(e,!0,p1,y1,rd)}function gi(e,t,s,i,r){if(!Je(e))return{}.NODE_ENV!=="production"&&qs(`value cannot be made ${t?"readonly":"reactive"}: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const a=E1(e);if(a===0)return e;const u=r.get(e);if(u)return u;const d=new Proxy(e,a===2?i:s);return r.set(e,d),d}function tr(e){return zs(e)?tr(e.__v_raw):!!(e&&e.__v_isReactive)}function zs(e){return!!(e&&e.__v_isReadonly)}function zt(e){return!!(e&&e.__v_isShallow)}function _i(e){return e?!!e.__v_raw:!1}function Me(e){const t=e&&e.__v_raw;return t?Me(t):e}function hl(e){return!Ze(e,"__v_skip")&&Object.isExtensible(e)&&lo(e,"__v_skip",!0),e}const Ft=e=>Je(e)?mi(e):e,vi=e=>Je(e)?id(e):e;function Ct(e){return e?e.__v_isRef===!0:!1}function ad(e){return ld(e,!1)}function C1(e){return ld(e,!0)}function ld(e,t){return Ct(e)?e:new x1(e,t)}class x1{constructor(t,s){this.dep=new al,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:Me(t),this._value=s?t:Ft(t),this.__v_isShallow=s}get value(){return{}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track(),this._value}set value(t){const s=this._rawValue,i=this.__v_isShallow||zt(t)||zs(t);t=i?t:Me(t),Nn(t,s)&&(this._rawValue=t,this._value=i?t:Ft(t),{}.NODE_ENV!=="production"?this.dep.trigger({target:this,type:"set",key:"value",newValue:t,oldValue:s}):this.dep.trigger())}}function In(e){return Ct(e)?e.value:e}const D1={get:(e,t,s)=>t==="__v_raw"?e:In(Reflect.get(e,t,s)),set:(e,t,s,i)=>{const r=e[t];return Ct(r)&&!Ct(s)?(r.value=s,!0):Reflect.set(e,t,s,i)}};function ud(e){return tr(e)?e:new Proxy(e,D1)}class O1{constructor(t,s,i){this.fn=t,this.setter=s,this._value=void 0,this.dep=new al(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=po-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=i}notify(){if(this.flags|=16,!(this.flags&8)&&st!==this)return Hc(this,!0),!0}get value(){const t={}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track();return jc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter?this.setter(t):{}.NODE_ENV!=="production"&&qs("Write operation failed: computed value is readonly")}}function S1(e,t,s=!1){let i,r;Oe(e)?i=e:(i=e.get,r=e.set);const a=new O1(i,r,s);return{}.NODE_ENV!=="production"&&t&&!s&&(a.onTrack=t.onTrack,a.onTrigger=t.onTrigger),a}const bi={},yi=new WeakMap;let sr;function T1(e,t=!1,s=sr){if(s){let i=yi.get(s);i||yi.set(s,i=[]),i.push(e)}else({}).NODE_ENV!=="production"&&!t&&qs("onWatcherCleanup() was called when there was no active watcher to associate with.")}function N1(e,t,s=tt){const{immediate:i,deep:r,once:a,scheduler:u,augmentJob:d,call:h}=s,_=X=>{(s.onWarn||qs)("Invalid watch source: ",X,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},p=X=>r?X:zt(X)||r===!1||r===0?an(X,1):an(X);let g,w,x,P,L=!1,te=!1;if(Ct(e)?(w=()=>e.value,L=zt(e)):tr(e)?(w=()=>p(e),L=!0):me(e)?(te=!0,L=e.some(X=>tr(X)||zt(X)),w=()=>e.map(X=>{if(Ct(X))return X.value;if(tr(X))return p(X);if(Oe(X))return h?h(X,2):X();({}).NODE_ENV!=="production"&&_(X)})):Oe(e)?t?w=h?()=>h(e,2):e:w=()=>{if(x){As();try{x()}finally{Ms()}}const X=sr;sr=g;try{return h?h(e,3,[P]):e(P)}finally{sr=X}}:(w=Ot,{}.NODE_ENV!=="production"&&_(e)),t&&r){const X=w,pe=r===!0?1/0:r;w=()=>an(X(),pe)}const N=r1(),re=()=>{g.stop(),N&&N.active&&Ya(N.effects,g)};if(a&&t){const X=t;t=(...pe)=>{X(...pe),re()}}let J=te?new Array(e.length).fill(bi):bi;const we=X=>{if(!(!(g.flags&1)||!g.dirty&&!X))if(t){const pe=g.run();if(r||L||(te?pe.some((be,Ae)=>Nn(be,J[Ae])):Nn(pe,J))){x&&x();const be=sr;sr=g;try{const Ae=[pe,J===bi?void 0:te&&J[0]===bi?[]:J,P];J=pe,h?h(t,3,Ae):t(...Ae)}finally{sr=be}}}else g.run()};return d&&d(we),g=new Bc(w),g.scheduler=u?()=>u(we,!1):we,P=X=>T1(X,!1,g),x=g.onStop=()=>{const X=yi.get(g);if(X){if(h)h(X,4);else for(const pe of X)pe();yi.delete(g)}},{}.NODE_ENV!=="production"&&(g.onTrack=s.onTrack,g.onTrigger=s.onTrigger),t?i?we(!0):J=g.run():u?u(we.bind(null,!0),!0):g.run(),re.pause=g.pause.bind(g),re.resume=g.resume.bind(g),re.stop=re,re}function an(e,t=1/0,s){if(t<=0||!Je(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,Ct(e))an(e.value,t,s);else if(me(e))for(let i=0;i<e.length;i++)an(e[i],t,s);else if(Sr(e)||Jn(e))e.forEach(i=>{an(i,t,s)});else if(kc(e)){for(const i in e)an(e[i],t,s);for(const i of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,i)&&an(e[i],t,s)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const nr=[];function wi(e){nr.push(e)}function Ei(){nr.pop()}let pl=!1;function Y(e,...t){if(pl)return;pl=!0,As();const s=nr.length?nr[nr.length-1].component:null,i=s&&s.appContext.config.warnHandler,r=I1();if(i)Ir(i,s,11,[e+t.map(a=>{var u,d;return(d=(u=a.toString)==null?void 0:u.call(a))!=null?d:JSON.stringify(a)}).join(""),s&&s.proxy,r.map(({vnode:a})=>`at <${$i(s,a.type)}>`).join(`
`),r]);else{const a=[`[Vue warn]: ${e}`,...t];r.length&&a.push(`
`,...A1(r)),console.warn(...a)}Ms(),pl=!1}function I1(){let e=nr[nr.length-1];if(!e)return[];const t=[];for(;e;){const s=t[0];s&&s.vnode===e?s.recurseCount++:t.push({vnode:e,recurseCount:0});const i=e.component&&e.component.parent;e=i&&i.vnode}return t}function A1(e){const t=[];return e.forEach((s,i)=>{t.push(...i===0?[]:[`
`],...M1(s))}),t}function M1({vnode:e,recurseCount:t}){const s=t>0?`... (${t} recursive calls)`:"",i=e.component?e.component.parent==null:!1,r=` at <${$i(e.component,e.type,i)}`,a=">"+s;return e.props?[r,...P1(e.props),a]:[r+a]}function P1(e){const t=[],s=Object.keys(e);return s.slice(0,3).forEach(i=>{t.push(...cd(i,e[i]))}),s.length>3&&t.push(" ..."),t}function cd(e,t,s){return dt(t)?(t=JSON.stringify(t),s?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?s?t:[`${e}=${t}`]:Ct(t)?(t=cd(e,Me(t.value),!0),s?t:[`${e}=Ref<`,t,">"]):Oe(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=Me(t),s?t:[`${e}=`,t])}function k1(e,t){({}).NODE_ENV!=="production"&&e!==void 0&&(typeof e!="number"?Y(`${t} is not a valid number - got ${JSON.stringify(e)}.`):isNaN(e)&&Y(`${t} is NaN - the duration expression might be incorrect.`))}const ml={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function Ir(e,t,s,i){try{return i?e(...i):e()}catch(r){_o(r,t,s)}}function Ps(e,t,s,i){if(Oe(e)){const r=Ir(e,t,s,i);return r&&Ja(r)&&r.catch(a=>{_o(a,t,s)}),r}if(me(e)){const r=[];for(let a=0;a<e.length;a++)r.push(Ps(e[a],t,s,i));return r}else({}).NODE_ENV!=="production"&&Y(`Invalid value type passed to callWithAsyncErrorHandling(): ${typeof e}`)}function _o(e,t,s,i=!0){const r=t?t.vnode:null,{errorHandler:a,throwUnhandledErrorInProduction:u}=t&&t.appContext.config||tt;if(t){let d=t.parent;const h=t.proxy,_={}.NODE_ENV!=="production"?ml[s]:`https://vuejs.org/error-reference/#runtime-${s}`;for(;d;){const p=d.ec;if(p){for(let g=0;g<p.length;g++)if(p[g](e,h,_)===!1)return}d=d.parent}if(a){As(),Ir(a,null,10,[e,h,_]),Ms();return}}V1(e,s,r,i,u)}function V1(e,t,s,i=!0,r=!1){if({}.NODE_ENV!=="production"){const a=ml[t];if(s&&wi(s),Y(`Unhandled error${a?` during execution of ${a}`:""}`),s&&Ei(),i)throw e;console.error(e)}else{if(r)throw e;console.error(e)}}const Gt=[];let Gs=-1;const Ar=[];let An=null,Mr=0;const dd=Promise.resolve();let Ci=null;const R1=100;function gl(e){const t=Ci||dd;return e?t.then(this?e.bind(this):e):t}function F1(e){let t=Gs+1,s=Gt.length;for(;t<s;){const i=t+s>>>1,r=Gt[i],a=vo(r);a<e||a===e&&r.flags&2?t=i+1:s=i}return t}function xi(e){if(!(e.flags&1)){const t=vo(e),s=Gt[Gt.length-1];!s||!(e.flags&2)&&t>=vo(s)?Gt.push(e):Gt.splice(F1(t),0,e),e.flags|=1,fd()}}function fd(){Ci||(Ci=dd.then(gd))}function hd(e){me(e)?Ar.push(...e):An&&e.id===-1?An.splice(Mr+1,0,e):e.flags&1||(Ar.push(e),e.flags|=1),fd()}function pd(e,t,s=Gs+1){for({}.NODE_ENV!=="production"&&(t=t||new Map);s<Gt.length;s++){const i=Gt[s];if(i&&i.flags&2){if(e&&i.id!==e.uid||{}.NODE_ENV!=="production"&&_l(t,i))continue;Gt.splice(s,1),s--,i.flags&4&&(i.flags&=-2),i(),i.flags&4||(i.flags&=-2)}}}function md(e){if(Ar.length){const t=[...new Set(Ar)].sort((s,i)=>vo(s)-vo(i));if(Ar.length=0,An){An.push(...t);return}for(An=t,{}.NODE_ENV!=="production"&&(e=e||new Map),Mr=0;Mr<An.length;Mr++){const s=An[Mr];({}).NODE_ENV!=="production"&&_l(e,s)||(s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2)}An=null,Mr=0}}const vo=e=>e.id==null?e.flags&2?-1:1/0:e.id;function gd(e){({}).NODE_ENV!=="production"&&(e=e||new Map);const t={}.NODE_ENV!=="production"?s=>_l(e,s):Ot;try{for(Gs=0;Gs<Gt.length;Gs++){const s=Gt[Gs];if(s&&!(s.flags&8)){if({}.NODE_ENV!=="production"&&t(s))continue;s.flags&4&&(s.flags&=-2),Ir(s,s.i,s.i?15:14),s.flags&4||(s.flags&=-2)}}}finally{for(;Gs<Gt.length;Gs++){const s=Gt[Gs];s&&(s.flags&=-2)}Gs=-1,Gt.length=0,md(e),Ci=null,(Gt.length||Ar.length)&&gd(e)}}function _l(e,t){const s=e.get(t)||0;if(s>R1){const i=t.i,r=i&&Wl(i.type);return _o(`Maximum recursive updates exceeded${r?` in component <${r}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}return e.set(t,s+1),!1}let ks=!1;const Di=new Map;({}).NODE_ENV!=="production"&&(uo().__VUE_HMR_RUNTIME__={createRecord:vl(_d),rerender:vl(B1),reload:vl($1)});const rr=new Map;function L1(e){const t=e.type.__hmrId;let s=rr.get(t);s||(_d(t,e.type),s=rr.get(t)),s.instances.add(e)}function U1(e){rr.get(e.type.__hmrId).instances.delete(e)}function _d(e,t){return rr.has(e)?!1:(rr.set(e,{initialDef:Oi(t),instances:new Set}),!0)}function Oi(e){return Nf(e)?e.__vccOpts:e}function B1(e,t){const s=rr.get(e);s&&(s.initialDef.render=t,[...s.instances].forEach(i=>{t&&(i.render=t,Oi(i.type).render=t),i.renderCache=[],ks=!0,i.update(),ks=!1}))}function $1(e,t){const s=rr.get(e);if(!s)return;t=Oi(t),vd(s.initialDef,t);const i=[...s.instances];for(let r=0;r<i.length;r++){const a=i[r],u=Oi(a.type);let d=Di.get(u);d||(u!==s.initialDef&&vd(u,t),Di.set(u,d=new Set)),d.add(a),a.appContext.propsCache.delete(a.type),a.appContext.emitsCache.delete(a.type),a.appContext.optionsCache.delete(a.type),a.ceReload?(d.add(a),a.ceReload(t.styles),d.delete(a)):a.parent?xi(()=>{ks=!0,a.parent.update(),ks=!1,d.delete(a)}):a.appContext.reload?a.appContext.reload():typeof window<"u"?window.location.reload():console.warn("[HMR] Root or manually mounted instance modified. Full reload required."),a.root.ce&&a!==a.root&&a.root.ce._removeChildStyle(u)}hd(()=>{Di.clear()})}function vd(e,t){ht(e,t);for(const s in e)s!=="__file"&&!(s in t)&&delete e[s]}function vl(e){return(t,s)=>{try{return e(t,s)}catch(i){console.error(i),console.warn("[HMR] Something went wrong during Vue component hot-reload. Full reload required.")}}}let Ks,bo=[],bl=!1;function yo(e,...t){Ks?Ks.emit(e,...t):bl||bo.push({event:e,args:t})}function bd(e,t){var s,i;Ks=e,Ks?(Ks.enabled=!0,bo.forEach(({event:r,args:a})=>Ks.emit(r,...a)),bo=[]):typeof window<"u"&&window.HTMLElement&&!((i=(s=window.navigator)==null?void 0:s.userAgent)!=null&&i.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(a=>{bd(a,t)}),setTimeout(()=>{Ks||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,bl=!0,bo=[])},3e3)):(bl=!0,bo=[])}function H1(e,t){yo("app:init",e,t,{Fragment:Ie,Text:So,Comment:yt,Static:To})}function q1(e){yo("app:unmount",e)}const W1=yl("component:added"),yd=yl("component:updated"),j1=yl("component:removed"),z1=e=>{Ks&&typeof Ks.cleanupBuffer=="function"&&!Ks.cleanupBuffer(e)&&j1(e)};/*! #__NO_SIDE_EFFECTS__ */function yl(e){return t=>{yo(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}const G1=wd("perf:start"),K1=wd("perf:end");function wd(e){return(t,s,i)=>{yo(e,t.appContext.app,t.uid,t,s,i)}}function Z1(e,t,s){yo("component:emit",e.appContext.app,e,t,s)}let bt=null,Ed=null;function Si(e){const t=bt;return bt=e,Ed=e&&e.type.__scopeId||null,t}function Se(e,t=bt,s){if(!t||e._n)return e;const i=(...r)=>{i._d&&bf(-1);const a=Si(t);let u;try{u=e(...r)}finally{Si(a),i._d&&bf(1)}return{}.NODE_ENV!=="production"&&yd(t),u};return i._n=!0,i._c=!0,i._d=!0,i}function Cd(e){$g(e)&&Y("Do not use built-in directive ids as custom directive id: "+e)}function at(e,t){if(bt===null)return{}.NODE_ENV!=="production"&&Y("withDirectives can only be used inside render functions."),e;const s=Bi(bt),i=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[a,u,d,h=tt]=t[r];a&&(Oe(a)&&(a={mounted:a,updated:a}),a.deep&&an(u),i.push({dir:a,instance:s,value:u,oldValue:void 0,arg:d,modifiers:h}))}return e}function or(e,t,s,i){const r=e.dirs,a=t&&t.dirs;for(let u=0;u<r.length;u++){const d=r[u];a&&(d.oldValue=a[u].value);let h=d.dir[i];h&&(As(),Ps(h,s,8,[e.el,d,e,t]),Ms())}}const xd=Symbol("_vte"),Dd=e=>e.__isTeleport,ir=e=>e&&(e.disabled||e.disabled===""),Od=e=>e&&(e.defer||e.defer===""),Sd=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Td=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,wl=(e,t)=>{const s=e&&e.to;if(dt(s))if(t){const i=t(s);return{}.NODE_ENV!=="production"&&!i&&!ir(e)&&Y(`Failed to locate Teleport target with selector "${s}". Note the target element must exist before the component is mounted - i.e. the target cannot be rendered by the component itself, and ideally should be outside of the entire Vue component tree.`),i}else return{}.NODE_ENV!=="production"&&Y("Current renderer does not support string target for Teleports. (missing querySelector renderer option)"),null;else return{}.NODE_ENV!=="production"&&!s&&!ir(e)&&Y(`Invalid Teleport target: ${s}`),s},Nd={name:"Teleport",__isTeleport:!0,process(e,t,s,i,r,a,u,d,h,_){const{mc:p,pc:g,pbc:w,o:{insert:x,querySelector:P,createText:L,createComment:te}}=_,N=ir(t.props);let{shapeFlag:re,children:J,dynamicChildren:we}=t;if({}.NODE_ENV!=="production"&&ks&&(h=!1,we=null),e==null){const X=t.el={}.NODE_ENV!=="production"?te("teleport start"):L(""),pe=t.anchor={}.NODE_ENV!=="production"?te("teleport end"):L("");x(X,s,i),x(pe,s,i);const be=(ae,A)=>{re&16&&(r&&r.isCE&&(r.ce._teleportTarget=ae),p(J,ae,A,r,a,u,d,h))},Ae=()=>{const ae=t.target=wl(t.props,P),A=Id(ae,t,L,x);ae?(u!=="svg"&&Sd(ae)?u="svg":u!=="mathml"&&Td(ae)&&(u="mathml"),N||(be(ae,A),Ni(t,!1))):{}.NODE_ENV!=="production"&&!N&&Y("Invalid Teleport target on mount:",ae,`(${typeof ae})`)};N&&(be(s,pe),Ni(t,!0)),Od(t.props)?(t.el.__isMounted=!1,Zt(()=>{Ae(),delete t.el.__isMounted},a)):Ae()}else{if(Od(t.props)&&e.el.__isMounted===!1){Zt(()=>{Nd.process(e,t,s,i,r,a,u,d,h,_)},a);return}t.el=e.el,t.targetStart=e.targetStart;const X=t.anchor=e.anchor,pe=t.target=e.target,be=t.targetAnchor=e.targetAnchor,Ae=ir(e.props),ae=Ae?s:pe,A=Ae?X:be;if(u==="svg"||Sd(pe)?u="svg":(u==="mathml"||Td(pe))&&(u="mathml"),we?(w(e.dynamicChildren,we,ae,r,a,u,d),Oo(e,t,{}.NODE_ENV==="production")):h||g(e,t,ae,A,r,a,u,d,!1),N)Ae?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Ti(t,s,X,_,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const Ee=t.target=wl(t.props,P);Ee?Ti(t,Ee,null,_,0):{}.NODE_ENV!=="production"&&Y("Invalid Teleport target on update:",pe,`(${typeof pe})`)}else Ae&&Ti(t,pe,be,_,1);Ni(t,N)}},remove(e,t,s,{um:i,o:{remove:r}},a){const{shapeFlag:u,children:d,anchor:h,targetStart:_,targetAnchor:p,target:g,props:w}=e;if(g&&(r(_),r(p)),a&&r(h),u&16){const x=a||!ir(w);for(let P=0;P<d.length;P++){const L=d[P];i(L,t,s,x,!!L.dynamicChildren)}}},move:Ti,hydrate:Y1};function Ti(e,t,s,{o:{insert:i},m:r},a=2){a===0&&i(e.targetAnchor,t,s);const{el:u,anchor:d,shapeFlag:h,children:_,props:p}=e,g=a===2;if(g&&i(u,t,s),(!g||ir(p))&&h&16)for(let w=0;w<_.length;w++)r(_[w],t,s,2);g&&i(d,t,s)}function Y1(e,t,s,i,r,a,{o:{nextSibling:u,parentNode:d,querySelector:h,insert:_,createText:p}},g){const w=t.target=wl(t.props,h);if(w){const x=ir(t.props),P=w._lpa||w.firstChild;if(t.shapeFlag&16)if(x)t.anchor=g(u(e),t,d(e),s,i,r,a),t.targetStart=P,t.targetAnchor=P&&u(P);else{t.anchor=u(e);let L=P;for(;L;){if(L&&L.nodeType===8){if(L.data==="teleport start anchor")t.targetStart=L;else if(L.data==="teleport anchor"){t.targetAnchor=L,w._lpa=t.targetAnchor&&u(t.targetAnchor);break}}L=u(L)}t.targetAnchor||Id(w,t,p,_),g(P&&u(P),t,w,s,i,r,a)}Ni(t,x)}return t.anchor&&u(t.anchor)}const J1=Nd;function Ni(e,t){const s=e.ctx;if(s&&s.ut){let i,r;for(t?(i=e.el,r=e.anchor):(i=e.targetStart,r=e.targetAnchor);i&&i!==r;)i.nodeType===1&&i.setAttribute("data-v-owner",s.uid),i=i.nextSibling;s.ut()}}function Id(e,t,s,i){const r=t.targetStart=s(""),a=t.targetAnchor=s("");return r[xd]=a,e&&(i(r,e),i(a,e)),a}const Mn=Symbol("_leaveCb"),Ii=Symbol("_enterCb");function Q1(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Bd(()=>{e.isMounted=!0}),$d(()=>{e.isUnmounting=!0}),e}const _s=[Function,Array],Ad={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:_s,onEnter:_s,onAfterEnter:_s,onEnterCancelled:_s,onBeforeLeave:_s,onLeave:_s,onAfterLeave:_s,onLeaveCancelled:_s,onBeforeAppear:_s,onAppear:_s,onAfterAppear:_s,onAppearCancelled:_s},Md=e=>{const t=e.subTree;return t.component?Md(t.component):t},X1={name:"BaseTransition",props:Ad,setup(e,{slots:t}){const s=Li(),i=Q1();return()=>{const r=t.default&&Rd(t.default(),!0);if(!r||!r.length)return;const a=Pd(r),u=Me(e),{mode:d}=u;if({}.NODE_ENV!=="production"&&d&&d!=="in-out"&&d!=="out-in"&&d!=="default"&&Y(`invalid <transition> mode: ${d}`),i.isLeaving)return Cl(a);const h=Vd(a);if(!h)return Cl(a);let _=El(h,u,i,s,g=>_=g);h.type!==yt&&wo(h,_);let p=s.subTree&&Vd(s.subTree);if(p&&p.type!==yt&&!cr(h,p)&&Md(s).type!==yt){let g=El(p,u,i,s);if(wo(p,g),d==="out-in"&&h.type!==yt)return i.isLeaving=!0,g.afterLeave=()=>{i.isLeaving=!1,s.job.flags&8||s.update(),delete g.afterLeave,p=void 0},Cl(a);d==="in-out"&&h.type!==yt?g.delayLeave=(w,x,P)=>{const L=kd(i,p);L[String(p.key)]=p,w[Mn]=()=>{x(),w[Mn]=void 0,delete _.delayedLeave,p=void 0},_.delayedLeave=()=>{P(),delete _.delayedLeave,p=void 0}}:p=void 0}else p&&(p=void 0);return a}}};function Pd(e){let t=e[0];if(e.length>1){let s=!1;for(const i of e)if(i.type!==yt){if({}.NODE_ENV!=="production"&&s){Y("<transition> can only be used on a single element or component. Use <transition-group> for lists.");break}if(t=i,s=!0,{}.NODE_ENV==="production")break}}return t}const e_=X1;function kd(e,t){const{leavingVNodes:s}=e;let i=s.get(t.type);return i||(i=Object.create(null),s.set(t.type,i)),i}function El(e,t,s,i,r){const{appear:a,mode:u,persisted:d=!1,onBeforeEnter:h,onEnter:_,onAfterEnter:p,onEnterCancelled:g,onBeforeLeave:w,onLeave:x,onAfterLeave:P,onLeaveCancelled:L,onBeforeAppear:te,onAppear:N,onAfterAppear:re,onAppearCancelled:J}=t,we=String(e.key),X=kd(s,e),pe=(ae,A)=>{ae&&Ps(ae,i,9,A)},be=(ae,A)=>{const Ee=A[1];pe(ae,A),me(ae)?ae.every(ue=>ue.length<=1)&&Ee():ae.length<=1&&Ee()},Ae={mode:u,persisted:d,beforeEnter(ae){let A=h;if(!s.isMounted)if(a)A=te||h;else return;ae[Mn]&&ae[Mn](!0);const Ee=X[we];Ee&&cr(e,Ee)&&Ee.el[Mn]&&Ee.el[Mn](),pe(A,[ae])},enter(ae){let A=_,Ee=p,ue=g;if(!s.isMounted)if(a)A=N||_,Ee=re||p,ue=J||g;else return;let Ge=!1;const mt=ae[Ii]=ce=>{Ge||(Ge=!0,ce?pe(ue,[ae]):pe(Ee,[ae]),Ae.delayedLeave&&Ae.delayedLeave(),ae[Ii]=void 0)};A?be(A,[ae,mt]):mt()},leave(ae,A){const Ee=String(e.key);if(ae[Ii]&&ae[Ii](!0),s.isUnmounting)return A();pe(w,[ae]);let ue=!1;const Ge=ae[Mn]=mt=>{ue||(ue=!0,A(),mt?pe(L,[ae]):pe(P,[ae]),ae[Mn]=void 0,X[Ee]===e&&delete X[Ee])};X[Ee]=e,x?be(x,[ae,Ge]):Ge()},clone(ae){const A=El(ae,t,s,i,r);return r&&r(A),A}};return Ae}function Cl(e){if(Co(e))return e=Ys(e),e.children=null,e}function Vd(e){if(!Co(e))return Dd(e.type)&&e.children?Pd(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:s}=e;if(s){if(t&16)return s[0];if(t&32&&Oe(s.default))return s.default()}}function wo(e,t){e.shapeFlag&6&&e.component?(e.transition=t,wo(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Rd(e,t=!1,s){let i=[],r=0;for(let a=0;a<e.length;a++){let u=e[a];const d=s==null?u.key:String(s)+String(u.key!=null?u.key:a);u.type===Ie?(u.patchFlag&128&&r++,i=i.concat(Rd(u.children,t,d))):(t||u.type!==yt)&&i.push(d!=null?Ys(u,{key:d}):u)}if(r>1)for(let a=0;a<i.length;a++)i[a].patchFlag=-2;return i}/*! #__NO_SIDE_EFFECTS__ */function Fd(e,t){return Oe(e)?(()=>ht({name:e.name},t,{setup:e}))():e}function Ld(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}const t_=new WeakSet;function Eo(e,t,s,i,r=!1){if(me(e)){e.forEach((P,L)=>Eo(P,t&&(me(t)?t[L]:t),s,i,r));return}if(Pr(i)&&!r){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&Eo(e,t,s,i.component.subTree);return}const a=i.shapeFlag&4?Bi(i.component):i.el,u=r?null:a,{i:d,r:h}=e;if({}.NODE_ENV!=="production"&&!d){Y("Missing ref owner context. ref cannot be used on hoisted vnodes. A vnode with ref must be created inside the render function.");return}const _=t&&t.r,p=d.refs===tt?d.refs={}:d.refs,g=d.setupState,w=Me(g),x=g===tt?()=>!1:P=>({}).NODE_ENV!=="production"&&(Ze(w,P)&&!Ct(w[P])&&Y(`Template ref "${P}" used on a non-ref value. It will not work in the production build.`),t_.has(w[P]))?!1:Ze(w,P);if(_!=null&&_!==h&&(dt(_)?(p[_]=null,x(_)&&(g[_]=null)):Ct(_)&&(_.value=null)),Oe(h))Ir(h,d,12,[u,p]);else{const P=dt(h),L=Ct(h);if(P||L){const te=()=>{if(e.f){const N=P?x(h)?g[h]:p[h]:h.value;r?me(N)&&Ya(N,a):me(N)?N.includes(a)||N.push(a):P?(p[h]=[a],x(h)&&(g[h]=p[h])):(h.value=[a],e.k&&(p[e.k]=h.value))}else P?(p[h]=u,x(h)&&(g[h]=u)):L?(h.value=u,e.k&&(p[e.k]=u)):{}.NODE_ENV!=="production"&&Y("Invalid template ref type:",h,`(${typeof h})`)};u?(te.id=-1,Zt(te,s)):te()}else({}).NODE_ENV!=="production"&&Y("Invalid template ref type:",h,`(${typeof h})`)}}uo().requestIdleCallback,uo().cancelIdleCallback;const Pr=e=>!!e.type.__asyncLoader,Co=e=>e.type.__isKeepAlive;function s_(e,t){Ud(e,"a",t)}function n_(e,t){Ud(e,"da",t)}function Ud(e,t,s=Tt){const i=e.__wdc||(e.__wdc=()=>{let r=s;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Ai(t,i,s),s){let r=s.parent;for(;r&&r.parent;)Co(r.parent.vnode)&&r_(i,t,s,r),r=r.parent}}function r_(e,t,s,i){const r=Ai(t,e,i,!0);Hd(()=>{Ya(i[t],r)},s)}function Ai(e,t,s=Tt,i=!1){if(s){const r=s[e]||(s[e]=[]),a=t.__weh||(t.__weh=(...u)=>{As();const d=Ao(s),h=Ps(t,s,e,u);return d(),Ms(),h});return i?r.unshift(a):r.push(a),a}else if({}.NODE_ENV!=="production"){const r=Xn(ml[e].replace(/ hook$/,""));Y(`${r} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`)}}const ln=e=>(t,s=Tt)=>{(!Mo||e==="sp")&&Ai(e,(...i)=>t(...i),s)},o_=ln("bm"),Bd=ln("m"),i_=ln("bu"),a_=ln("u"),$d=ln("bum"),Hd=ln("um"),l_=ln("sp"),u_=ln("rtg"),c_=ln("rtc");function d_(e,t=Tt){Ai("ec",e,t)}const xl="components",f_="directives";function G(e,t){return qd(xl,e,!0,t)||e}const h_=Symbol.for("v-ndc");function p_(e){return qd(f_,e)}function qd(e,t,s=!0,i=!1){const r=bt||Tt;if(r){const a=r.type;if(e===xl){const d=Wl(a,!1);if(d&&(d===t||d===jt(t)||d===Qn(jt(t))))return a}const u=Wd(r[e]||a[e],t)||Wd(r.appContext[e],t);if(!u&&i)return a;if({}.NODE_ENV!=="production"&&s&&!u){const d=e===xl?`
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.`:"";Y(`Failed to resolve ${e.slice(0,-1)}: ${t}${d}`)}return u}else({}).NODE_ENV!=="production"&&Y(`resolve${Qn(e.slice(0,-1))} can only be used in render() or setup().`)}function Wd(e,t){return e&&(e[t]||e[jt(t)]||e[Qn(jt(t))])}function lt(e,t,s,i){let r;const a=s&&s[i],u=me(e);if(u||dt(e)){const d=u&&tr(e);let h=!1,_=!1;d&&(h=!zt(e),_=zs(e),e=di(e)),r=new Array(e.length);for(let p=0,g=e.length;p<g;p++)r[p]=t(h?_?vi(Ft(e[p])):Ft(e[p]):e[p],p,void 0,a&&a[p])}else if(typeof e=="number"){({}).NODE_ENV!=="production"&&!Number.isInteger(e)&&Y(`The v-for range expect an integer value but got ${e}.`),r=new Array(e);for(let d=0;d<e;d++)r[d]=t(d+1,d,void 0,a&&a[d])}else if(Je(e))if(e[Symbol.iterator])r=Array.from(e,(d,h)=>t(d,h,void 0,a&&a[h]));else{const d=Object.keys(e);r=new Array(d.length);for(let h=0,_=d.length;h<_;h++){const p=d[h];r[h]=t(e[p],p,h,a&&a[h])}}else r=[];return s&&(s[i]=r),r}function Lt(e,t,s={},i,r){if(bt.ce||bt.parent&&Pr(bt.parent)&&bt.parent.ce)return t!=="default"&&(s.name=t),D(),_t(Ie,null,[M("slot",s,i&&i())],64);let a=e[t];({}).NODE_ENV!=="production"&&a&&a.length>1&&(Y("SSR-optimized slot function detected in a non-SSR-optimized render function. You need to mark this component with $dynamic-slots in the parent template."),a=()=>[]),a&&a._c&&(a._d=!1),D();const u=a&&jd(a(s)),d=s.key||u&&u.key,h=_t(Ie,{key:(d&&!Ns(d)?d:`_${t}`)+(!u&&i?"_fb":"")},u||(i?i():[]),u&&e._===1?64:-2);return!r&&h.scopeId&&(h.slotScopeIds=[h.scopeId+"-s"]),a&&a._c&&(a._d=!0),h}function jd(e){return e.some(t=>ur(t)?!(t.type===yt||t.type===Ie&&!jd(t.children)):!0)?e:null}const Dl=e=>e?Df(e)?Bi(e):Dl(e.parent):null,ar=ht(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>({}).NODE_ENV!=="production"?js(e.props):e.props,$attrs:e=>({}).NODE_ENV!=="production"?js(e.attrs):e.attrs,$slots:e=>({}).NODE_ENV!=="production"?js(e.slots):e.slots,$refs:e=>({}).NODE_ENV!=="production"?js(e.refs):e.refs,$parent:e=>Dl(e.parent),$root:e=>Dl(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Nl(e),$forceUpdate:e=>e.f||(e.f=()=>{xi(e.update)}),$nextTick:e=>e.n||(e.n=gl.bind(e.proxy)),$watch:e=>G_.bind(e)}),Ol=e=>e==="_"||e==="$",Sl=(e,t)=>e!==tt&&!e.__isScriptSetup&&Ze(e,t),zd={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:i,data:r,props:a,accessCache:u,type:d,appContext:h}=e;if({}.NODE_ENV!=="production"&&t==="__isVue")return!0;let _;if(t[0]!=="$"){const x=u[t];if(x!==void 0)switch(x){case 1:return i[t];case 2:return r[t];case 4:return s[t];case 3:return a[t]}else{if(Sl(i,t))return u[t]=1,i[t];if(r!==tt&&Ze(r,t))return u[t]=2,r[t];if((_=e.propsOptions[0])&&Ze(_,t))return u[t]=3,a[t];if(s!==tt&&Ze(s,t))return u[t]=4,s[t];Tl&&(u[t]=0)}}const p=ar[t];let g,w;if(p)return t==="$attrs"?(St(e.attrs,"get",""),{}.NODE_ENV!=="production"&&Ri()):{}.NODE_ENV!=="production"&&t==="$slots"&&St(e,"get",t),p(e);if((g=d.__cssModules)&&(g=g[t]))return g;if(s!==tt&&Ze(s,t))return u[t]=4,s[t];if(w=h.config.globalProperties,Ze(w,t))return w[t];({}).NODE_ENV!=="production"&&bt&&(!dt(t)||t.indexOf("__v")!==0)&&(r!==tt&&Ol(t[0])&&Ze(r,t)?Y(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===bt&&Y(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))},set({_:e},t,s){const{data:i,setupState:r,ctx:a}=e;return Sl(r,t)?(r[t]=s,!0):{}.NODE_ENV!=="production"&&r.__isScriptSetup&&Ze(r,t)?(Y(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):i!==tt&&Ze(i,t)?(i[t]=s,!0):Ze(e.props,t)?({}.NODE_ENV!=="production"&&Y(`Attempting to mutate prop "${t}". Props are readonly.`),!1):t[0]==="$"&&t.slice(1)in e?({}.NODE_ENV!=="production"&&Y(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):({}.NODE_ENV!=="production"&&t in e.appContext.config.globalProperties?Object.defineProperty(a,t,{enumerable:!0,configurable:!0,value:s}):a[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:i,appContext:r,propsOptions:a}},u){let d;return!!s[u]||e!==tt&&Ze(e,u)||Sl(t,u)||(d=a[0])&&Ze(d,u)||Ze(i,u)||Ze(ar,u)||Ze(r.config.globalProperties,u)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:Ze(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};({}).NODE_ENV!=="production"&&(zd.ownKeys=e=>(Y("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e)));function m_(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys(ar).forEach(s=>{Object.defineProperty(t,s,{configurable:!0,enumerable:!1,get:()=>ar[s](e),set:Ot})}),t}function g_(e){const{ctx:t,propsOptions:[s]}=e;s&&Object.keys(s).forEach(i=>{Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>e.props[i],set:Ot})})}function __(e){const{ctx:t,setupState:s}=e;Object.keys(Me(s)).forEach(i=>{if(!s.__isScriptSetup){if(Ol(i[0])){Y(`setup() return property ${JSON.stringify(i)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);return}Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>s[i],set:Ot})}})}function Gd(e){return me(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}function v_(){const e=Object.create(null);return(t,s)=>{e[s]?Y(`${t} property "${s}" is already defined in ${e[s]}.`):e[s]=t}}let Tl=!0;function b_(e){const t=Nl(e),s=e.proxy,i=e.ctx;Tl=!1,t.beforeCreate&&Kd(t.beforeCreate,e,"bc");const{data:r,computed:a,methods:u,watch:d,provide:h,inject:_,created:p,beforeMount:g,mounted:w,beforeUpdate:x,updated:P,activated:L,deactivated:te,beforeDestroy:N,beforeUnmount:re,destroyed:J,unmounted:we,render:X,renderTracked:pe,renderTriggered:be,errorCaptured:Ae,serverPrefetch:ae,expose:A,inheritAttrs:Ee,components:ue,directives:Ge,filters:mt}=t,ce={}.NODE_ENV!=="production"?v_():null;if({}.NODE_ENV!=="production"){const[de]=e.propsOptions;if(de)for(const Ce in de)ce("Props",Ce)}if(_&&y_(_,i,ce),u)for(const de in u){const Ce=u[de];Oe(Ce)?({}.NODE_ENV!=="production"?Object.defineProperty(i,de,{value:Ce.bind(s),configurable:!0,enumerable:!0,writable:!0}):i[de]=Ce.bind(s),{}.NODE_ENV!=="production"&&ce("Methods",de)):{}.NODE_ENV!=="production"&&Y(`Method "${de}" has type "${typeof Ce}" in the component definition. Did you reference the function correctly?`)}if(r){({}).NODE_ENV!=="production"&&!Oe(r)&&Y("The data option must be a function. Plain object usage is no longer supported.");const de=r.call(s,s);if({}.NODE_ENV!=="production"&&Ja(de)&&Y("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),!Je(de))({}).NODE_ENV!=="production"&&Y("data() should return an object.");else if(e.data=mi(de),{}.NODE_ENV!=="production")for(const Ce in de)ce("Data",Ce),Ol(Ce[0])||Object.defineProperty(i,Ce,{configurable:!0,enumerable:!0,get:()=>de[Ce],set:Ot})}if(Tl=!0,a)for(const de in a){const Ce=a[de],xt=Oe(Ce)?Ce.bind(s,s):Oe(Ce.get)?Ce.get.bind(s,s):Ot;({}).NODE_ENV!=="production"&&xt===Ot&&Y(`Computed property "${de}" has no getter.`);const vs=!Oe(Ce)&&Oe(Ce.set)?Ce.set.bind(s):{}.NODE_ENV!=="production"?()=>{Y(`Write operation failed: computed property "${de}" is readonly.`)}:Ot,Nt=Rs({get:xt,set:vs});Object.defineProperty(i,de,{enumerable:!0,configurable:!0,get:()=>Nt.value,set:bs=>Nt.value=bs}),{}.NODE_ENV!=="production"&&ce("Computed",de)}if(d)for(const de in d)Zd(d[de],i,s,de);if(h){const de=Oe(h)?h.call(s):h;Reflect.ownKeys(de).forEach(Ce=>{Pi(Ce,de[Ce])})}p&&Kd(p,e,"c");function it(de,Ce){me(Ce)?Ce.forEach(xt=>de(xt.bind(s))):Ce&&de(Ce.bind(s))}if(it(o_,g),it(Bd,w),it(i_,x),it(a_,P),it(s_,L),it(n_,te),it(d_,Ae),it(c_,pe),it(u_,be),it($d,re),it(Hd,we),it(l_,ae),me(A))if(A.length){const de=e.exposed||(e.exposed={});A.forEach(Ce=>{Object.defineProperty(de,Ce,{get:()=>s[Ce],set:xt=>s[Ce]=xt})})}else e.exposed||(e.exposed={});X&&e.render===Ot&&(e.render=X),Ee!=null&&(e.inheritAttrs=Ee),ue&&(e.components=ue),Ge&&(e.directives=Ge),ae&&Ld(e)}function y_(e,t,s=Ot){me(e)&&(e=Il(e));for(const i in e){const r=e[i];let a;Je(r)?"default"in r?a=Zs(r.from||i,r.default,!0):a=Zs(r.from||i):a=Zs(r),Ct(a)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>a.value,set:u=>a.value=u}):t[i]=a,{}.NODE_ENV!=="production"&&s("Inject",i)}}function Kd(e,t,s){Ps(me(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,s)}function Zd(e,t,s,i){let r=i.includes(".")?hf(s,i):()=>s[i];if(dt(e)){const a=t[e];Oe(a)?Vr(r,a):{}.NODE_ENV!=="production"&&Y(`Invalid watch handler specified by key "${e}"`,a)}else if(Oe(e))Vr(r,e.bind(s));else if(Je(e))if(me(e))e.forEach(a=>Zd(a,t,s,i));else{const a=Oe(e.handler)?e.handler.bind(s):t[e.handler];Oe(a)?Vr(r,a,e):{}.NODE_ENV!=="production"&&Y(`Invalid watch handler specified by key "${e.handler}"`,a)}else({}).NODE_ENV!=="production"&&Y(`Invalid watch option: "${i}"`,e)}function Nl(e){const t=e.type,{mixins:s,extends:i}=t,{mixins:r,optionsCache:a,config:{optionMergeStrategies:u}}=e.appContext,d=a.get(t);let h;return d?h=d:!r.length&&!s&&!i?h=t:(h={},r.length&&r.forEach(_=>Mi(h,_,u,!0)),Mi(h,t,u)),Je(t)&&a.set(t,h),h}function Mi(e,t,s,i=!1){const{mixins:r,extends:a}=t;a&&Mi(e,a,s,!0),r&&r.forEach(u=>Mi(e,u,s,!0));for(const u in t)if(i&&u==="expose")({}).NODE_ENV!=="production"&&Y('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const d=w_[u]||s&&s[u];e[u]=d?d(e[u],t[u]):t[u]}return e}const w_={data:Yd,props:Jd,emits:Jd,methods:xo,computed:xo,beforeCreate:Kt,created:Kt,beforeMount:Kt,mounted:Kt,beforeUpdate:Kt,updated:Kt,beforeDestroy:Kt,beforeUnmount:Kt,destroyed:Kt,unmounted:Kt,activated:Kt,deactivated:Kt,errorCaptured:Kt,serverPrefetch:Kt,components:xo,directives:xo,watch:C_,provide:Yd,inject:E_};function Yd(e,t){return t?e?function(){return ht(Oe(e)?e.call(this,this):e,Oe(t)?t.call(this,this):t)}:t:e}function E_(e,t){return xo(Il(e),Il(t))}function Il(e){if(me(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Kt(e,t){return e?[...new Set([].concat(e,t))]:t}function xo(e,t){return e?ht(Object.create(null),e,t):t}function Jd(e,t){return e?me(e)&&me(t)?[...new Set([...e,...t])]:ht(Object.create(null),Gd(e),Gd(t??{})):t}function C_(e,t){if(!e)return t;if(!t)return e;const s=ht(Object.create(null),e);for(const i in t)s[i]=Kt(e[i],t[i]);return s}function Qd(){return{app:null,config:{isNativeTag:Ug,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let x_=0;function D_(e,t){return function(i,r=null){Oe(i)||(i=ht({},i)),r!=null&&!Je(r)&&({}.NODE_ENV!=="production"&&Y("root props passed to app.mount() must be an object."),r=null);const a=Qd(),u=new WeakSet,d=[];let h=!1;const _=a.app={_uid:x_++,_component:i,_props:r,_container:null,_context:a,_instance:null,version:If,get config(){return a.config},set config(p){({}).NODE_ENV!=="production"&&Y("app.config cannot be replaced. Modify individual options instead.")},use(p,...g){return u.has(p)?{}.NODE_ENV!=="production"&&Y("Plugin has already been applied to target app."):p&&Oe(p.install)?(u.add(p),p.install(_,...g)):Oe(p)?(u.add(p),p(_,...g)):{}.NODE_ENV!=="production"&&Y('A plugin must either be a function or an object with an "install" function.'),_},mixin(p){return a.mixins.includes(p)?{}.NODE_ENV!=="production"&&Y("Mixin has already been applied to target app"+(p.name?`: ${p.name}`:"")):a.mixins.push(p),_},component(p,g){return{}.NODE_ENV!=="production"&&Hl(p,a.config),g?({}.NODE_ENV!=="production"&&a.components[p]&&Y(`Component "${p}" has already been registered in target app.`),a.components[p]=g,_):a.components[p]},directive(p,g){return{}.NODE_ENV!=="production"&&Cd(p),g?({}.NODE_ENV!=="production"&&a.directives[p]&&Y(`Directive "${p}" has already been registered in target app.`),a.directives[p]=g,_):a.directives[p]},mount(p,g,w){if(h)({}).NODE_ENV!=="production"&&Y("App has already been mounted.\nIf you want to remount the same app, move your app creation logic into a factory function and create fresh app instances for each mount - e.g. `const createMyApp = () => createApp(App)`");else{({}).NODE_ENV!=="production"&&p.__vue_app__&&Y("There is already an app instance mounted on the host container.\n If you want to mount another app on the same host container, you need to unmount the previous app by calling `app.unmount()` first.");const x=_._ceVNode||M(i,r);return x.appContext=a,w===!0?w="svg":w===!1&&(w=void 0),{}.NODE_ENV!=="production"&&(a.reload=()=>{const P=Ys(x);P.el=null,e(P,p,w)}),g&&t?t(x,p):e(x,p,w),h=!0,_._container=p,p.__vue_app__=_,{}.NODE_ENV!=="production"&&(_._instance=x.component,H1(_,If)),Bi(x.component)}},onUnmount(p){({}).NODE_ENV!=="production"&&typeof p!="function"&&Y(`Expected function as first argument to app.onUnmount(), but got ${typeof p}`),d.push(p)},unmount(){h?(Ps(d,_._instance,16),e(null,_._container),{}.NODE_ENV!=="production"&&(_._instance=null,q1(_)),delete _._container.__vue_app__):{}.NODE_ENV!=="production"&&Y("Cannot unmount an app that is not mounted.")},provide(p,g){return{}.NODE_ENV!=="production"&&p in a.provides&&(Ze(a.provides,p)?Y(`App already provides property with key "${String(p)}". It will be overwritten with the new value.`):Y(`App already provides property with key "${String(p)}" inherited from its parent element. It will be overwritten with the new value.`)),a.provides[p]=g,_},runWithContext(p){const g=kr;kr=_;try{return p()}finally{kr=g}}};return _}}let kr=null;function Pi(e,t){if(!Tt)({}).NODE_ENV!=="production"&&Y("provide() can only be used inside setup().");else{let s=Tt.provides;const i=Tt.parent&&Tt.parent.provides;i===s&&(s=Tt.provides=Object.create(i)),s[e]=t}}function Zs(e,t,s=!1){const i=Tt||bt;if(i||kr){let r=kr?kr._context.provides:i?i.parent==null||i.ce?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return s&&Oe(t)?t.call(i&&i.proxy):t;({}).NODE_ENV!=="production"&&Y(`injection "${String(e)}" not found.`)}else({}).NODE_ENV!=="production"&&Y("inject() can only be used inside setup() or functional components.")}const Xd={},ef=()=>Object.create(Xd),tf=e=>Object.getPrototypeOf(e)===Xd;function O_(e,t,s,i=!1){const r={},a=ef();e.propsDefaults=Object.create(null),sf(e,t,r,a);for(const u in e.propsOptions[0])u in r||(r[u]=void 0);({}).NODE_ENV!=="production"&&of(t||{},r,e),s?e.props=i?r:od(r):e.type.props?e.props=r:e.props=a,e.attrs=a}function S_(e){for(;e;){if(e.type.__hmrId)return!0;e=e.parent}}function T_(e,t,s,i){const{props:r,attrs:a,vnode:{patchFlag:u}}=e,d=Me(r),[h]=e.propsOptions;let _=!1;if(!({}.NODE_ENV!=="production"&&S_(e))&&(i||u>0)&&!(u&16)){if(u&8){const p=e.vnode.dynamicProps;for(let g=0;g<p.length;g++){let w=p[g];if(Vi(e.emitsOptions,w))continue;const x=t[w];if(h)if(Ze(a,w))x!==a[w]&&(a[w]=x,_=!0);else{const P=jt(w);r[P]=Al(h,d,P,x,e,!1)}else x!==a[w]&&(a[w]=x,_=!0)}}}else{sf(e,t,r,a)&&(_=!0);let p;for(const g in d)(!t||!Ze(t,g)&&((p=Tn(g))===g||!Ze(t,p)))&&(h?s&&(s[g]!==void 0||s[p]!==void 0)&&(r[g]=Al(h,d,g,void 0,e,!0)):delete r[g]);if(a!==d)for(const g in a)(!t||!Ze(t,g))&&(delete a[g],_=!0)}_&&Ws(e.attrs,"set",""),{}.NODE_ENV!=="production"&&of(t||{},r,e)}function sf(e,t,s,i){const[r,a]=e.propsOptions;let u=!1,d;if(t)for(let h in t){if(ao(h))continue;const _=t[h];let p;r&&Ze(r,p=jt(h))?!a||!a.includes(p)?s[p]=_:(d||(d={}))[p]=_:Vi(e.emitsOptions,h)||(!(h in i)||_!==i[h])&&(i[h]=_,u=!0)}if(a){const h=Me(s),_=d||tt;for(let p=0;p<a.length;p++){const g=a[p];s[g]=Al(r,h,g,_[g],e,!Ze(_,g))}}return u}function Al(e,t,s,i,r,a){const u=e[s];if(u!=null){const d=Ze(u,"default");if(d&&i===void 0){const h=u.default;if(u.type!==Function&&!u.skipFactory&&Oe(h)){const{propsDefaults:_}=r;if(s in _)i=_[s];else{const p=Ao(r);i=_[s]=h.call(null,t),p()}}else i=h;r.ce&&r.ce._setProp(s,i)}u[0]&&(a&&!d?i=!1:u[1]&&(i===""||i===Tn(s))&&(i=!0))}return i}const N_=new WeakMap;function nf(e,t,s=!1){const i=s?N_:t.propsCache,r=i.get(e);if(r)return r;const a=e.props,u={},d=[];let h=!1;if(!Oe(e)){const p=g=>{h=!0;const[w,x]=nf(g,t,!0);ht(u,w),x&&d.push(...x)};!s&&t.mixins.length&&t.mixins.forEach(p),e.extends&&p(e.extends),e.mixins&&e.mixins.forEach(p)}if(!a&&!h)return Je(e)&&i.set(e,Or),Or;if(me(a))for(let p=0;p<a.length;p++){({}).NODE_ENV!=="production"&&!dt(a[p])&&Y("props must be strings when using array syntax.",a[p]);const g=jt(a[p]);rf(g)&&(u[g]=tt)}else if(a){({}).NODE_ENV!=="production"&&!Je(a)&&Y("invalid props options",a);for(const p in a){const g=jt(p);if(rf(g)){const w=a[p],x=u[g]=me(w)||Oe(w)?{type:w}:ht({},w),P=x.type;let L=!1,te=!0;if(me(P))for(let N=0;N<P.length;++N){const re=P[N],J=Oe(re)&&re.name;if(J==="Boolean"){L=!0;break}else J==="String"&&(te=!1)}else L=Oe(P)&&P.name==="Boolean";x[0]=L,x[1]=te,(L||Ze(x,"default"))&&d.push(g)}}}const _=[u,d];return Je(e)&&i.set(e,_),_}function rf(e){return e[0]!=="$"&&!ao(e)?!0:({}.NODE_ENV!=="production"&&Y(`Invalid prop name: "${e}" is a reserved property.`),!1)}function I_(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function of(e,t,s){const i=Me(t),r=s.propsOptions[0],a=Object.keys(e).map(u=>jt(u));for(const u in r){let d=r[u];d!=null&&A_(u,i[u],d,{}.NODE_ENV!=="production"?js(i):i,!a.includes(u))}}function A_(e,t,s,i,r){const{type:a,required:u,validator:d,skipCheck:h}=s;if(u&&r){Y('Missing required prop: "'+e+'"');return}if(!(t==null&&!u)){if(a!=null&&a!==!0&&!h){let _=!1;const p=me(a)?a:[a],g=[];for(let w=0;w<p.length&&!_;w++){const{valid:x,expectedType:P}=P_(t,p[w]);g.push(P||""),_=x}if(!_){Y(k_(e,t,g));return}}d&&!d(t,i)&&Y('Invalid prop: custom validator check failed for prop "'+e+'".')}}const M_=rn("String,Number,Boolean,Function,Symbol,BigInt");function P_(e,t){let s;const i=I_(t);if(i==="null")s=e===null;else if(M_(i)){const r=typeof e;s=r===i.toLowerCase(),!s&&r==="object"&&(s=e instanceof t)}else i==="Object"?s=Je(e):i==="Array"?s=me(e):s=e instanceof t;return{valid:s,expectedType:i}}function k_(e,t,s){if(s.length===0)return`Prop type [] for prop "${e}" won't match anything. Did you mean to use type Array instead?`;let i=`Invalid prop: type check failed for prop "${e}". Expected ${s.map(Qn).join(" | ")}`;const r=s[0],a=Qa(t),u=af(t,r),d=af(t,a);return s.length===1&&lf(r)&&!V_(r,a)&&(i+=` with value ${u}`),i+=`, got ${a} `,lf(a)&&(i+=`with value ${d}.`),i}function af(e,t){return t==="String"?`"${e}"`:t==="Number"?`${Number(e)}`:`${e}`}function lf(e){return["string","number","boolean"].some(s=>e.toLowerCase()===s)}function V_(...e){return e.some(t=>t.toLowerCase()==="boolean")}const Ml=e=>e[0]==="_"||e==="$stable",Pl=e=>me(e)?e.map(Vs):[Vs(e)],R_=(e,t,s)=>{if(t._n)return t;const i=Se((...r)=>({}.NODE_ENV!=="production"&&Tt&&!(s===null&&bt)&&!(s&&s.root!==Tt.root)&&Y(`Slot "${e}" invoked outside of the render function: this will not track dependencies used in the slot. Invoke the slot function inside the render function instead.`),Pl(t(...r))),s);return i._c=!1,i},uf=(e,t,s)=>{const i=e._ctx;for(const r in e){if(Ml(r))continue;const a=e[r];if(Oe(a))t[r]=R_(r,a,i);else if(a!=null){({}).NODE_ENV!=="production"&&Y(`Non-function value encountered for slot "${r}". Prefer function slots for better performance.`);const u=Pl(a);t[r]=()=>u}}},cf=(e,t)=>{({}).NODE_ENV!=="production"&&!Co(e.vnode)&&Y("Non-function value encountered for default slot. Prefer function slots for better performance.");const s=Pl(t);e.slots.default=()=>s},kl=(e,t,s)=>{for(const i in t)(s||!Ml(i))&&(e[i]=t[i])},F_=(e,t,s)=>{const i=e.slots=ef();if(e.vnode.shapeFlag&32){const r=t.__;r&&lo(i,"__",r,!0);const a=t._;a?(kl(i,t,s),s&&lo(i,"_",a,!0)):uf(t,i)}else t&&cf(e,t)},L_=(e,t,s)=>{const{vnode:i,slots:r}=e;let a=!0,u=tt;if(i.shapeFlag&32){const d=t._;d?{}.NODE_ENV!=="production"&&ks?(kl(r,t,s),Ws(e,"set","$slots")):s&&d===1?a=!1:kl(r,t,s):(a=!t.$stable,uf(t,r)),u=t}else t&&(cf(e,t),u={default:1});if(a)for(const d in r)!Ml(d)&&u[d]==null&&delete r[d]};let Do,Pn;function un(e,t){e.appContext.config.performance&&ki()&&Pn.mark(`vue-${t}-${e.uid}`),{}.NODE_ENV!=="production"&&G1(e,t,ki()?Pn.now():Date.now())}function cn(e,t){if(e.appContext.config.performance&&ki()){const s=`vue-${t}-${e.uid}`,i=s+":end";Pn.mark(i),Pn.measure(`<${$i(e,e.type)}> ${t}`,s,i),Pn.clearMarks(s),Pn.clearMarks(i)}({}).NODE_ENV!=="production"&&K1(e,t,ki()?Pn.now():Date.now())}function ki(){return Do!==void 0||(typeof window<"u"&&window.performance?(Do=!0,Pn=window.performance):Do=!1),Do}function U_(){const e=[];if({}.NODE_ENV!=="production"&&e.length){const t=e.length>1;console.warn(`Feature flag${t?"s":""} ${e.join(", ")} ${t?"are":"is"} not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.`)}}const Zt=ev;function B_(e){return $_(e)}function $_(e,t){U_();const s=uo();s.__VUE__=!0,{}.NODE_ENV!=="production"&&bd(s.__VUE_DEVTOOLS_GLOBAL_HOOK__,s);const{insert:i,remove:r,patchProp:a,createElement:u,createText:d,createComment:h,setText:_,setElementText:p,parentNode:g,nextSibling:w,setScopeId:x=Ot,insertStaticContent:P}=e,L=(y,C,V,U=null,q=null,j=null,se=void 0,Z=null,ee={}.NODE_ENV!=="production"&&ks?!1:!!C.dynamicChildren)=>{if(y===C)return;y&&!cr(y,C)&&(U=ie(y),ss(y,q,j,!0),y=null),C.patchFlag===-2&&(ee=!1,C.dynamicChildren=null);const{type:K,ref:ye,shapeFlag:ne}=C;switch(K){case So:te(y,C,V,U);break;case yt:N(y,C,V,U);break;case To:y==null?re(C,V,U,se):{}.NODE_ENV!=="production"&&J(y,C,V,se);break;case Ie:Ge(y,C,V,U,q,j,se,Z,ee);break;default:ne&1?pe(y,C,V,U,q,j,se,Z,ee):ne&6?mt(y,C,V,U,q,j,se,Z,ee):ne&64||ne&128?K.process(y,C,V,U,q,j,se,Z,ee,ke):{}.NODE_ENV!=="production"&&Y("Invalid VNode type:",K,`(${typeof K})`)}ye!=null&&q?Eo(ye,y&&y.ref,j,C||y,!C):ye==null&&y&&y.ref!=null&&Eo(y.ref,null,j,y,!0)},te=(y,C,V,U)=>{if(y==null)i(C.el=d(C.children),V,U);else{const q=C.el=y.el;C.children!==y.children&&_(q,C.children)}},N=(y,C,V,U)=>{y==null?i(C.el=h(C.children||""),V,U):C.el=y.el},re=(y,C,V,U)=>{[y.el,y.anchor]=P(y.children,C,V,U,y.el,y.anchor)},J=(y,C,V,U)=>{if(C.children!==y.children){const q=w(y.anchor);X(y),[C.el,C.anchor]=P(C.children,V,q,U)}else C.el=y.el,C.anchor=y.anchor},we=({el:y,anchor:C},V,U)=>{let q;for(;y&&y!==C;)q=w(y),i(y,V,U),y=q;i(C,V,U)},X=({el:y,anchor:C})=>{let V;for(;y&&y!==C;)V=w(y),r(y),y=V;r(C)},pe=(y,C,V,U,q,j,se,Z,ee)=>{C.type==="svg"?se="svg":C.type==="math"&&(se="mathml"),y==null?be(C,V,U,q,j,se,Z,ee):A(y,C,q,j,se,Z,ee)},be=(y,C,V,U,q,j,se,Z)=>{let ee,K;const{props:ye,shapeFlag:ne,transition:_e,dirs:xe}=y;if(ee=y.el=u(y.type,j,ye&&ye.is,ye),ne&8?p(ee,y.children):ne&16&&ae(y.children,ee,null,U,q,Vl(y,j),se,Z),xe&&or(y,null,U,"created"),Ae(ee,y,y.scopeId,se,U),ye){for(const Xe in ye)Xe!=="value"&&!ao(Xe)&&a(ee,Xe,null,ye[Xe],j,U);"value"in ye&&a(ee,"value",null,ye.value,j),(K=ye.onVnodeBeforeMount)&&Js(K,U,y)}({}).NODE_ENV!=="production"&&(lo(ee,"__vnode",y,!0),lo(ee,"__vueParentComponent",U,!0)),xe&&or(y,null,U,"beforeMount");const Le=H_(q,_e);Le&&_e.beforeEnter(ee),i(ee,C,V),((K=ye&&ye.onVnodeMounted)||Le||xe)&&Zt(()=>{K&&Js(K,U,y),Le&&_e.enter(ee),xe&&or(y,null,U,"mounted")},q)},Ae=(y,C,V,U,q)=>{if(V&&x(y,V),U)for(let j=0;j<U.length;j++)x(y,U[j]);if(q){let j=q.subTree;if({}.NODE_ENV!=="production"&&j.patchFlag>0&&j.patchFlag&2048&&(j=Ul(j.children)||j),C===j||vf(j.type)&&(j.ssContent===C||j.ssFallback===C)){const se=q.vnode;Ae(y,se,se.scopeId,se.slotScopeIds,q.parent)}}},ae=(y,C,V,U,q,j,se,Z,ee=0)=>{for(let K=ee;K<y.length;K++){const ye=y[K]=Z?kn(y[K]):Vs(y[K]);L(null,ye,C,V,U,q,j,se,Z)}},A=(y,C,V,U,q,j,se)=>{const Z=C.el=y.el;({}).NODE_ENV!=="production"&&(Z.__vnode=C);let{patchFlag:ee,dynamicChildren:K,dirs:ye}=C;ee|=y.patchFlag&16;const ne=y.props||tt,_e=C.props||tt;let xe;if(V&&lr(V,!1),(xe=_e.onVnodeBeforeUpdate)&&Js(xe,V,C,y),ye&&or(C,y,V,"beforeUpdate"),V&&lr(V,!0),{}.NODE_ENV!=="production"&&ks&&(ee=0,se=!1,K=null),(ne.innerHTML&&_e.innerHTML==null||ne.textContent&&_e.textContent==null)&&p(Z,""),K?(Ee(y.dynamicChildren,K,Z,V,U,Vl(C,q),j),{}.NODE_ENV!=="production"&&Oo(y,C)):se||xt(y,C,Z,null,V,U,Vl(C,q),j,!1),ee>0){if(ee&16)ue(Z,ne,_e,V,q);else if(ee&2&&ne.class!==_e.class&&a(Z,"class",null,_e.class,q),ee&4&&a(Z,"style",ne.style,_e.style,q),ee&8){const Le=C.dynamicProps;for(let Xe=0;Xe<Le.length;Xe++){const Ye=Le[Xe],Vt=ne[Ye],Dt=_e[Ye];(Dt!==Vt||Ye==="value")&&a(Z,Ye,Vt,Dt,q,V)}}ee&1&&y.children!==C.children&&p(Z,C.children)}else!se&&K==null&&ue(Z,ne,_e,V,q);((xe=_e.onVnodeUpdated)||ye)&&Zt(()=>{xe&&Js(xe,V,C,y),ye&&or(C,y,V,"updated")},U)},Ee=(y,C,V,U,q,j,se)=>{for(let Z=0;Z<C.length;Z++){const ee=y[Z],K=C[Z],ye=ee.el&&(ee.type===Ie||!cr(ee,K)||ee.shapeFlag&198)?g(ee.el):V;L(ee,K,ye,null,U,q,j,se,!0)}},ue=(y,C,V,U,q)=>{if(C!==V){if(C!==tt)for(const j in C)!ao(j)&&!(j in V)&&a(y,j,C[j],null,q,U);for(const j in V){if(ao(j))continue;const se=V[j],Z=C[j];se!==Z&&j!=="value"&&a(y,j,Z,se,q,U)}"value"in V&&a(y,"value",C.value,V.value,q)}},Ge=(y,C,V,U,q,j,se,Z,ee)=>{const K=C.el=y?y.el:d(""),ye=C.anchor=y?y.anchor:d("");let{patchFlag:ne,dynamicChildren:_e,slotScopeIds:xe}=C;({}).NODE_ENV!=="production"&&(ks||ne&2048)&&(ne=0,ee=!1,_e=null),xe&&(Z=Z?Z.concat(xe):xe),y==null?(i(K,V,U),i(ye,V,U),ae(C.children||[],V,ye,q,j,se,Z,ee)):ne>0&&ne&64&&_e&&y.dynamicChildren?(Ee(y.dynamicChildren,_e,V,q,j,se,Z),{}.NODE_ENV!=="production"?Oo(y,C):(C.key!=null||q&&C===q.subTree)&&Oo(y,C,!0)):xt(y,C,V,ye,q,j,se,Z,ee)},mt=(y,C,V,U,q,j,se,Z,ee)=>{C.slotScopeIds=Z,y==null?C.shapeFlag&512?q.ctx.activate(C,V,U,se,ee):ce(C,V,U,q,j,se,ee):it(y,C,ee)},ce=(y,C,V,U,q,j,se)=>{const Z=y.component=lv(y,U,q);if({}.NODE_ENV!=="production"&&Z.type.__hmrId&&L1(Z),{}.NODE_ENV!=="production"&&(wi(y),un(Z,"mount")),Co(y)&&(Z.ctx.renderer=ke),{}.NODE_ENV!=="production"&&un(Z,"init"),cv(Z,!1,se),{}.NODE_ENV!=="production"&&cn(Z,"init"),{}.NODE_ENV!=="production"&&ks&&(y.el=null),Z.asyncDep){if(q&&q.registerDep(Z,de,se),!y.el){const ee=Z.subTree=M(yt);N(null,ee,C,V)}}else de(Z,y,C,V,q,j,se);({}).NODE_ENV!=="production"&&(Ei(),cn(Z,"mount"))},it=(y,C,V)=>{const U=C.component=y.component;if(Q_(y,C,V))if(U.asyncDep&&!U.asyncResolved){({}).NODE_ENV!=="production"&&wi(C),Ce(U,C,V),{}.NODE_ENV!=="production"&&Ei();return}else U.next=C,U.update();else C.el=y.el,U.vnode=C},de=(y,C,V,U,q,j,se)=>{const Z=()=>{if(y.isMounted){let{next:ne,bu:_e,u:xe,parent:Le,vnode:Xe}=y;{const Ut=df(y);if(Ut){ne&&(ne.el=Xe.el,Ce(y,ne,se)),Ut.asyncDep.then(()=>{y.isUnmounted||Z()});return}}let Ye=ne,Vt;({}).NODE_ENV!=="production"&&wi(ne||y.vnode),lr(y,!1),ne?(ne.el=Xe.el,Ce(y,ne,se)):ne=Xe,_e&&Tr(_e),(Vt=ne.props&&ne.props.onVnodeBeforeUpdate)&&Js(Vt,Le,ne,Xe),lr(y,!0),{}.NODE_ENV!=="production"&&un(y,"render");const Dt=Ll(y);({}).NODE_ENV!=="production"&&cn(y,"render");const Jt=y.subTree;y.subTree=Dt,{}.NODE_ENV!=="production"&&un(y,"patch"),L(Jt,Dt,g(Jt.el),ie(Jt),y,q,j),{}.NODE_ENV!=="production"&&cn(y,"patch"),ne.el=Dt.el,Ye===null&&X_(y,Dt.el),xe&&Zt(xe,q),(Vt=ne.props&&ne.props.onVnodeUpdated)&&Zt(()=>Js(Vt,Le,ne,Xe),q),{}.NODE_ENV!=="production"&&yd(y),{}.NODE_ENV!=="production"&&Ei()}else{let ne;const{el:_e,props:xe}=C,{bm:Le,m:Xe,parent:Ye,root:Vt,type:Dt}=y,Jt=Pr(C);if(lr(y,!1),Le&&Tr(Le),!Jt&&(ne=xe&&xe.onVnodeBeforeMount)&&Js(ne,Ye,C),lr(y,!0),_e&&Ve){const Ut=()=>{({}).NODE_ENV!=="production"&&un(y,"render"),y.subTree=Ll(y),{}.NODE_ENV!=="production"&&cn(y,"render"),{}.NODE_ENV!=="production"&&un(y,"hydrate"),Ve(_e,y.subTree,y,q,null),{}.NODE_ENV!=="production"&&cn(y,"hydrate")};Jt&&Dt.__asyncHydrate?Dt.__asyncHydrate(_e,y,Ut):Ut()}else{Vt.ce&&Vt.ce._def.shadowRoot!==!1&&Vt.ce._injectChildStyle(Dt),{}.NODE_ENV!=="production"&&un(y,"render");const Ut=y.subTree=Ll(y);({}).NODE_ENV!=="production"&&cn(y,"render"),{}.NODE_ENV!=="production"&&un(y,"patch"),L(null,Ut,V,U,y,q,j),{}.NODE_ENV!=="production"&&cn(y,"patch"),C.el=Ut.el}if(Xe&&Zt(Xe,q),!Jt&&(ne=xe&&xe.onVnodeMounted)){const Ut=C;Zt(()=>Js(ne,Ye,Ut),q)}(C.shapeFlag&256||Ye&&Pr(Ye.vnode)&&Ye.vnode.shapeFlag&256)&&y.a&&Zt(y.a,q),y.isMounted=!0,{}.NODE_ENV!=="production"&&W1(y),C=V=U=null}};y.scope.on();const ee=y.effect=new Bc(Z);y.scope.off();const K=y.update=ee.run.bind(ee),ye=y.job=ee.runIfDirty.bind(ee);ye.i=y,ye.id=y.uid,ee.scheduler=()=>xi(ye),lr(y,!0),{}.NODE_ENV!=="production"&&(ee.onTrack=y.rtc?ne=>Tr(y.rtc,ne):void 0,ee.onTrigger=y.rtg?ne=>Tr(y.rtg,ne):void 0),K()},Ce=(y,C,V)=>{C.component=y;const U=y.vnode.props;y.vnode=C,y.next=null,T_(y,C.props,U,V),L_(y,C.children,V),As(),pd(y),Ms()},xt=(y,C,V,U,q,j,se,Z,ee=!1)=>{const K=y&&y.children,ye=y?y.shapeFlag:0,ne=C.children,{patchFlag:_e,shapeFlag:xe}=C;if(_e>0){if(_e&128){Nt(K,ne,V,U,q,j,se,Z,ee);return}else if(_e&256){vs(K,ne,V,U,q,j,se,Z,ee);return}}xe&8?(ye&16&&R(K,q,j),ne!==K&&p(V,ne)):ye&16?xe&16?Nt(K,ne,V,U,q,j,se,Z,ee):R(K,q,j,!0):(ye&8&&p(V,""),xe&16&&ae(ne,V,U,q,j,se,Z,ee))},vs=(y,C,V,U,q,j,se,Z,ee)=>{y=y||Or,C=C||Or;const K=y.length,ye=C.length,ne=Math.min(K,ye);let _e;for(_e=0;_e<ne;_e++){const xe=C[_e]=ee?kn(C[_e]):Vs(C[_e]);L(y[_e],xe,V,null,q,j,se,Z,ee)}K>ye?R(y,q,j,!0,!1,ne):ae(C,V,U,q,j,se,Z,ee,ne)},Nt=(y,C,V,U,q,j,se,Z,ee)=>{let K=0;const ye=C.length;let ne=y.length-1,_e=ye-1;for(;K<=ne&&K<=_e;){const xe=y[K],Le=C[K]=ee?kn(C[K]):Vs(C[K]);if(cr(xe,Le))L(xe,Le,V,null,q,j,se,Z,ee);else break;K++}for(;K<=ne&&K<=_e;){const xe=y[ne],Le=C[_e]=ee?kn(C[_e]):Vs(C[_e]);if(cr(xe,Le))L(xe,Le,V,null,q,j,se,Z,ee);else break;ne--,_e--}if(K>ne){if(K<=_e){const xe=_e+1,Le=xe<ye?C[xe].el:U;for(;K<=_e;)L(null,C[K]=ee?kn(C[K]):Vs(C[K]),V,Le,q,j,se,Z,ee),K++}}else if(K>_e)for(;K<=ne;)ss(y[K],q,j,!0),K++;else{const xe=K,Le=K,Xe=new Map;for(K=Le;K<=_e;K++){const It=C[K]=ee?kn(C[K]):Vs(C[K]);It.key!=null&&({}.NODE_ENV!=="production"&&Xe.has(It.key)&&Y("Duplicate keys found during update:",JSON.stringify(It.key),"Make sure keys are unique."),Xe.set(It.key,K))}let Ye,Vt=0;const Dt=_e-Le+1;let Jt=!1,Ut=0;const vn=new Array(Dt);for(K=0;K<Dt;K++)vn[K]=0;for(K=xe;K<=ne;K++){const It=y[K];if(Vt>=Dt){ss(It,q,j,!0);continue}let ys;if(It.key!=null)ys=Xe.get(It.key);else for(Ye=Le;Ye<=_e;Ye++)if(vn[Ye-Le]===0&&cr(It,C[Ye])){ys=Ye;break}ys===void 0?ss(It,q,j,!0):(vn[ys-Le]=K+1,ys>=Ut?Ut=ys:Jt=!0,L(It,C[ys],V,null,q,j,se,Z,ee),Vt++)}const Wr=Jt?q_(vn):Or;for(Ye=Wr.length-1,K=Dt-1;K>=0;K--){const It=Le+K,ys=C[It],aa=It+1<ye?C[It+1].el:U;vn[K]===0?L(null,ys,V,aa,q,j,se,Z,ee):Jt&&(Ye<0||K!==Wr[Ye]?bs(ys,V,aa,2):Ye--)}}},bs=(y,C,V,U,q=null)=>{const{el:j,type:se,transition:Z,children:ee,shapeFlag:K}=y;if(K&6){bs(y.component.subTree,C,V,U);return}if(K&128){y.suspense.move(C,V,U);return}if(K&64){se.move(y,C,V,ke);return}if(se===Ie){i(j,C,V);for(let ne=0;ne<ee.length;ne++)bs(ee[ne],C,V,U);i(y.anchor,C,V);return}if(se===To){we(y,C,V);return}if(U!==2&&K&1&&Z)if(U===0)Z.beforeEnter(j),i(j,C,V),Zt(()=>Z.enter(j),q);else{const{leave:ne,delayLeave:_e,afterLeave:xe}=Z,Le=()=>{y.ctx.isUnmounted?r(j):i(j,C,V)},Xe=()=>{ne(j,()=>{Le(),xe&&xe()})};_e?_e(j,Le,Xe):Xe()}else i(j,C,V)},ss=(y,C,V,U=!1,q=!1)=>{const{type:j,props:se,ref:Z,children:ee,dynamicChildren:K,shapeFlag:ye,patchFlag:ne,dirs:_e,cacheIndex:xe}=y;if(ne===-2&&(q=!1),Z!=null&&(As(),Eo(Z,null,V,y,!0),Ms()),xe!=null&&(C.renderCache[xe]=void 0),ye&256){C.ctx.deactivate(y);return}const Le=ye&1&&_e,Xe=!Pr(y);let Ye;if(Xe&&(Ye=se&&se.onVnodeBeforeUnmount)&&Js(Ye,C,y),ye&6)cs(y.component,V,U);else{if(ye&128){y.suspense.unmount(V,U);return}Le&&or(y,null,C,"beforeUnmount"),ye&64?y.type.remove(y,C,V,ke,U):K&&!K.hasOnce&&(j!==Ie||ne>0&&ne&64)?R(K,C,V,!1,!0):(j===Ie&&ne&384||!q&&ye&16)&&R(ee,C,V),U&&en(y)}(Xe&&(Ye=se&&se.onVnodeUnmounted)||Le)&&Zt(()=>{Ye&&Js(Ye,C,y),Le&&or(y,null,C,"unmounted")},V)},en=y=>{const{type:C,el:V,anchor:U,transition:q}=y;if(C===Ie){({}).NODE_ENV!=="production"&&y.patchFlag>0&&y.patchFlag&2048&&q&&!q.persisted?y.children.forEach(se=>{se.type===yt?r(se.el):en(se)}):Ls(V,U);return}if(C===To){X(y);return}const j=()=>{r(V),q&&!q.persisted&&q.afterLeave&&q.afterLeave()};if(y.shapeFlag&1&&q&&!q.persisted){const{leave:se,delayLeave:Z}=q,ee=()=>se(V,j);Z?Z(y.el,j,ee):ee()}else j()},Ls=(y,C)=>{let V;for(;y!==C;)V=w(y),r(y),y=V;r(C)},cs=(y,C,V)=>{({}).NODE_ENV!=="production"&&y.type.__hmrId&&U1(y);const{bum:U,scope:q,job:j,subTree:se,um:Z,m:ee,a:K,parent:ye,slots:{__:ne}}=y;ff(ee),ff(K),U&&Tr(U),ye&&me(ne)&&ne.forEach(_e=>{ye.renderCache[_e]=void 0}),q.stop(),j&&(j.flags|=8,ss(se,y,C,V)),Z&&Zt(Z,C),Zt(()=>{y.isUnmounted=!0},C),C&&C.pendingBranch&&!C.isUnmounted&&y.asyncDep&&!y.asyncResolved&&y.suspenseId===C.pendingId&&(C.deps--,C.deps===0&&C.resolve()),{}.NODE_ENV!=="production"&&z1(y)},R=(y,C,V,U=!1,q=!1,j=0)=>{for(let se=j;se<y.length;se++)ss(y[se],C,V,U,q)},ie=y=>{if(y.shapeFlag&6)return ie(y.component.subTree);if(y.shapeFlag&128)return y.suspense.next();const C=w(y.anchor||y.el),V=C&&C[xd];return V?w(V):C};let oe=!1;const ge=(y,C,V)=>{y==null?C._vnode&&ss(C._vnode,null,null,!0):L(C._vnode||null,y,C,null,null,null,V),C._vnode=y,oe||(oe=!0,pd(),md(),oe=!1)},ke={p:L,um:ss,m:bs,r:en,mt:ce,mc:ae,pc:xt,pbc:Ee,n:ie,o:e};let rt,Ve;return t&&([rt,Ve]=t(ke)),{render:ge,hydrate:rt,createApp:D_(ge,rt)}}function Vl({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function lr({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function H_(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Oo(e,t,s=!1){const i=e.children,r=t.children;if(me(i)&&me(r))for(let a=0;a<i.length;a++){const u=i[a];let d=r[a];d.shapeFlag&1&&!d.dynamicChildren&&((d.patchFlag<=0||d.patchFlag===32)&&(d=r[a]=kn(r[a]),d.el=u.el),!s&&d.patchFlag!==-2&&Oo(u,d)),d.type===So&&(d.el=u.el),d.type===yt&&!d.el&&(d.el=u.el),{}.NODE_ENV!=="production"&&d.el&&(d.el.__vnode=d)}}function q_(e){const t=e.slice(),s=[0];let i,r,a,u,d;const h=e.length;for(i=0;i<h;i++){const _=e[i];if(_!==0){if(r=s[s.length-1],e[r]<_){t[i]=r,s.push(i);continue}for(a=0,u=s.length-1;a<u;)d=a+u>>1,e[s[d]]<_?a=d+1:u=d;_<e[s[a]]&&(a>0&&(t[i]=s[a-1]),s[a]=i)}}for(a=s.length,u=s[a-1];a-- >0;)s[a]=u,u=t[u];return s}function df(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:df(t)}function ff(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const W_=Symbol.for("v-scx"),j_=()=>{{const e=Zs(W_);return e||{}.NODE_ENV!=="production"&&Y("Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build."),e}};function z_(e,t){return Rl(e,null,t)}function Vr(e,t,s){return{}.NODE_ENV!=="production"&&!Oe(t)&&Y("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),Rl(e,t,s)}function Rl(e,t,s=tt){const{immediate:i,deep:r,flush:a,once:u}=s;({}).NODE_ENV!=="production"&&!t&&(i!==void 0&&Y('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),r!==void 0&&Y('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),u!==void 0&&Y('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const d=ht({},s);({}).NODE_ENV!=="production"&&(d.onWarn=Y);const h=t&&i||!t&&a!=="post";let _;if(Mo){if(a==="sync"){const x=j_();_=x.__watcherHandles||(x.__watcherHandles=[])}else if(!h){const x=()=>{};return x.stop=Ot,x.resume=Ot,x.pause=Ot,x}}const p=Tt;d.call=(x,P,L)=>Ps(x,p,P,L);let g=!1;a==="post"?d.scheduler=x=>{Zt(x,p&&p.suspense)}:a!=="sync"&&(g=!0,d.scheduler=(x,P)=>{P?x():xi(x)}),d.augmentJob=x=>{t&&(x.flags|=4),g&&(x.flags|=2,p&&(x.id=p.uid,x.i=p))};const w=N1(e,t,d);return Mo&&(_?_.push(w):h&&w()),w}function G_(e,t,s){const i=this.proxy,r=dt(e)?e.includes(".")?hf(i,e):()=>i[e]:e.bind(i,i);let a;Oe(t)?a=t:(a=t.handler,s=t);const u=Ao(this),d=Rl(r,a.bind(i),s);return u(),d}function hf(e,t){const s=t.split(".");return()=>{let i=e;for(let r=0;r<s.length&&i;r++)i=i[s[r]];return i}}const K_=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${jt(t)}Modifiers`]||e[`${Tn(t)}Modifiers`];function Z_(e,t,...s){if(e.isUnmounted)return;const i=e.vnode.props||tt;if({}.NODE_ENV!=="production"){const{emitsOptions:p,propsOptions:[g]}=e;if(p)if(!(t in p))(!g||!(Xn(jt(t))in g))&&Y(`Component emitted event "${t}" but it is neither declared in the emits option nor as an "${Xn(jt(t))}" prop.`);else{const w=p[t];Oe(w)&&(w(...s)||Y(`Invalid event arguments: event validation failed for event "${t}".`))}}let r=s;const a=t.startsWith("update:"),u=a&&K_(i,t.slice(7));if(u&&(u.trim&&(r=s.map(p=>dt(p)?p.trim():p)),u.number&&(r=s.map(ci))),{}.NODE_ENV!=="production"&&Z1(e,t,r),{}.NODE_ENV!=="production"){const p=t.toLowerCase();p!==t&&i[Xn(p)]&&Y(`Event "${p}" is emitted in component ${$i(e,e.type)} but the handler is registered for "${t}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${Tn(t)}" instead of "${t}".`)}let d,h=i[d=Xn(t)]||i[d=Xn(jt(t))];!h&&a&&(h=i[d=Xn(Tn(t))]),h&&Ps(h,e,6,r);const _=i[d+"Once"];if(_){if(!e.emitted)e.emitted={};else if(e.emitted[d])return;e.emitted[d]=!0,Ps(_,e,6,r)}}function pf(e,t,s=!1){const i=t.emitsCache,r=i.get(e);if(r!==void 0)return r;const a=e.emits;let u={},d=!1;if(!Oe(e)){const h=_=>{const p=pf(_,t,!0);p&&(d=!0,ht(u,p))};!s&&t.mixins.length&&t.mixins.forEach(h),e.extends&&h(e.extends),e.mixins&&e.mixins.forEach(h)}return!a&&!d?(Je(e)&&i.set(e,null),null):(me(a)?a.forEach(h=>u[h]=null):ht(u,a),Je(e)&&i.set(e,u),u)}function Vi(e,t){return!e||!oo(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ze(e,t[0].toLowerCase()+t.slice(1))||Ze(e,Tn(t))||Ze(e,t))}let Fl=!1;function Ri(){Fl=!0}function Ll(e){const{type:t,vnode:s,proxy:i,withProxy:r,propsOptions:[a],slots:u,attrs:d,emit:h,render:_,renderCache:p,props:g,data:w,setupState:x,ctx:P,inheritAttrs:L}=e,te=Si(e);let N,re;({}).NODE_ENV!=="production"&&(Fl=!1);try{if(s.shapeFlag&4){const X=r||i,pe={}.NODE_ENV!=="production"&&x.__isScriptSetup?new Proxy(X,{get(be,Ae,ae){return Y(`Property '${String(Ae)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(be,Ae,ae)}}):X;N=Vs(_.call(pe,X,p,{}.NODE_ENV!=="production"?js(g):g,x,w,P)),re=d}else{const X=t;({}).NODE_ENV!=="production"&&d===g&&Ri(),N=Vs(X.length>1?X({}.NODE_ENV!=="production"?js(g):g,{}.NODE_ENV!=="production"?{get attrs(){return Ri(),js(d)},slots:u,emit:h}:{attrs:d,slots:u,emit:h}):X({}.NODE_ENV!=="production"?js(g):g,null)),re=t.props?d:Y_(d)}}catch(X){No.length=0,_o(X,e,1),N=M(yt)}let J=N,we;if({}.NODE_ENV!=="production"&&N.patchFlag>0&&N.patchFlag&2048&&([J,we]=mf(N)),re&&L!==!1){const X=Object.keys(re),{shapeFlag:pe}=J;if(X.length){if(pe&7)a&&X.some(li)&&(re=J_(re,a)),J=Ys(J,re,!1,!0);else if({}.NODE_ENV!=="production"&&!Fl&&J.type!==yt){const be=Object.keys(d),Ae=[],ae=[];for(let A=0,Ee=be.length;A<Ee;A++){const ue=be[A];oo(ue)?li(ue)||Ae.push(ue[2].toLowerCase()+ue.slice(3)):ae.push(ue)}ae.length&&Y(`Extraneous non-props attributes (${ae.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes.`),Ae.length&&Y(`Extraneous non-emits event listeners (${Ae.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text root nodes. If the listener is intended to be a component custom event listener only, declare it using the "emits" option.`)}}}return s.dirs&&({}.NODE_ENV!=="production"&&!gf(J)&&Y("Runtime directive used on component with non-element root node. The directives will not function as intended."),J=Ys(J,null,!1,!0),J.dirs=J.dirs?J.dirs.concat(s.dirs):s.dirs),s.transition&&({}.NODE_ENV!=="production"&&!gf(J)&&Y("Component inside <Transition> renders non-element root node that cannot be animated."),wo(J,s.transition)),{}.NODE_ENV!=="production"&&we?we(J):N=J,Si(te),N}const mf=e=>{const t=e.children,s=e.dynamicChildren,i=Ul(t,!1);if(i){if({}.NODE_ENV!=="production"&&i.patchFlag>0&&i.patchFlag&2048)return mf(i)}else return[e,void 0];const r=t.indexOf(i),a=s?s.indexOf(i):-1,u=d=>{t[r]=d,s&&(a>-1?s[a]=d:d.patchFlag>0&&(e.dynamicChildren=[...s,d]))};return[Vs(i),u]};function Ul(e,t=!0){let s;for(let i=0;i<e.length;i++){const r=e[i];if(ur(r)){if(r.type!==yt||r.children==="v-if"){if(s)return;if(s=r,{}.NODE_ENV!=="production"&&t&&s.patchFlag>0&&s.patchFlag&2048)return Ul(s.children)}}else return}return s}const Y_=e=>{let t;for(const s in e)(s==="class"||s==="style"||oo(s))&&((t||(t={}))[s]=e[s]);return t},J_=(e,t)=>{const s={};for(const i in e)(!li(i)||!(i.slice(9)in t))&&(s[i]=e[i]);return s},gf=e=>e.shapeFlag&7||e.type===yt;function Q_(e,t,s){const{props:i,children:r,component:a}=e,{props:u,children:d,patchFlag:h}=t,_=a.emitsOptions;if({}.NODE_ENV!=="production"&&(r||d)&&ks||t.dirs||t.transition)return!0;if(s&&h>=0){if(h&1024)return!0;if(h&16)return i?_f(i,u,_):!!u;if(h&8){const p=t.dynamicProps;for(let g=0;g<p.length;g++){const w=p[g];if(u[w]!==i[w]&&!Vi(_,w))return!0}}}else return(r||d)&&(!d||!d.$stable)?!0:i===u?!1:i?u?_f(i,u,_):!0:!!u;return!1}function _f(e,t,s){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let r=0;r<i.length;r++){const a=i[r];if(t[a]!==e[a]&&!Vi(s,a))return!0}return!1}function X_({vnode:e,parent:t},s){for(;t;){const i=t.subTree;if(i.suspense&&i.suspense.activeBranch===e&&(i.el=e.el),i===e)(e=t.vnode).el=s,t=t.parent;else break}}const vf=e=>e.__isSuspense;function ev(e,t){t&&t.pendingBranch?me(e)?t.effects.push(...e):t.effects.push(e):hd(e)}const Ie=Symbol.for("v-fgt"),So=Symbol.for("v-txt"),yt=Symbol.for("v-cmt"),To=Symbol.for("v-stc"),No=[];let ls=null;function D(e=!1){No.push(ls=e?null:[])}function tv(){No.pop(),ls=No[No.length-1]||null}let Io=1;function bf(e,t=!1){Io+=e,e<0&&ls&&t&&(ls.hasOnce=!0)}function yf(e){return e.dynamicChildren=Io>0?ls||Or:null,tv(),Io>0&&ls&&ls.push(e),e}function S(e,t,s,i,r,a){return yf(c(e,t,s,i,r,a,!0))}function _t(e,t,s,i,r){return yf(M(e,t,s,i,r,!0))}function ur(e){return e?e.__v_isVNode===!0:!1}function cr(e,t){if({}.NODE_ENV!=="production"&&t.shapeFlag&6&&e.component){const s=Di.get(t.type);if(s&&s.has(e.component))return e.shapeFlag&=-257,t.shapeFlag&=-513,!1}return e.type===t.type&&e.key===t.key}const sv=(...e)=>Ef(...e),wf=({key:e})=>e??null,Fi=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?dt(e)||Ct(e)||Oe(e)?{i:bt,r:e,k:t,f:!!s}:e:null);function c(e,t=null,s=null,i=0,r=null,a=e===Ie?0:1,u=!1,d=!1){const h={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&wf(t),ref:t&&Fi(t),scopeId:Ed,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:i,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:bt};return d?(Bl(h,s),a&128&&e.normalize(h)):s&&(h.shapeFlag|=dt(s)?8:16),{}.NODE_ENV!=="production"&&h.key!==h.key&&Y("VNode created with invalid key (NaN). VNode type:",h.type),Io>0&&!u&&ls&&(h.patchFlag>0||a&6)&&h.patchFlag!==32&&ls.push(h),h}const M={}.NODE_ENV!=="production"?sv:Ef;function Ef(e,t=null,s=null,i=0,r=null,a=!1){if((!e||e===h_)&&({}.NODE_ENV!=="production"&&!e&&Y(`Invalid vnode type when creating vnode: ${e}.`),e=yt),ur(e)){const d=Ys(e,t,!0);return s&&Bl(d,s),Io>0&&!a&&ls&&(d.shapeFlag&6?ls[ls.indexOf(e)]=d:ls.push(d)),d.patchFlag=-2,d}if(Nf(e)&&(e=e.__vccOpts),t){t=nv(t);let{class:d,style:h}=t;d&&!dt(d)&&(t.class=fe(d)),Je(h)&&(_i(h)&&!me(h)&&(h=ht({},h)),t.style=as(h))}const u=dt(e)?1:vf(e)?128:Dd(e)?64:Je(e)?4:Oe(e)?2:0;return{}.NODE_ENV!=="production"&&u&4&&_i(e)&&(e=Me(e),Y("Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with `markRaw` or using `shallowRef` instead of `ref`.",`
Component that was made reactive: `,e)),c(e,t,s,i,r,u,a,!0)}function nv(e){return e?_i(e)||tf(e)?ht({},e):e:null}function Ys(e,t,s=!1,i=!1){const{props:r,ref:a,patchFlag:u,children:d,transition:h}=e,_=t?ov(r||{},t):r,p={__v_isVNode:!0,__v_skip:!0,type:e.type,props:_,key:_&&wf(_),ref:t&&t.ref?s&&a?me(a)?a.concat(Fi(t)):[a,Fi(t)]:Fi(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:{}.NODE_ENV!=="production"&&u===-1&&me(d)?d.map(Cf):d,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ie?u===-1?16:u|16:u,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:h,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ys(e.ssContent),ssFallback:e.ssFallback&&Ys(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return h&&i&&wo(p,h.clone(p)),p}function Cf(e){const t=Ys(e);return me(e.children)&&(t.children=e.children.map(Cf)),t}function qe(e=" ",t=0){return M(So,null,e,t)}function rv(e,t){const s=M(To,null,e);return s.staticCount=t,s}function Q(e="",t=!1){return t?(D(),_t(yt,null,e)):M(yt,null,e)}function Vs(e){return e==null||typeof e=="boolean"?M(yt):me(e)?M(Ie,null,e.slice()):ur(e)?kn(e):M(So,null,String(e))}function kn(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ys(e)}function Bl(e,t){let s=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(me(t))s=16;else if(typeof t=="object")if(i&65){const r=t.default;r&&(r._c&&(r._d=!1),Bl(e,r()),r._c&&(r._d=!0));return}else{s=32;const r=t._;!r&&!tf(t)?t._ctx=bt:r===3&&bt&&(bt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Oe(t)?(t={default:t,_ctx:bt},s=32):(t=String(t),i&64?(s=16,t=[qe(t)]):s=8);e.children=t,e.shapeFlag|=s}function ov(...e){const t={};for(let s=0;s<e.length;s++){const i=e[s];for(const r in i)if(r==="class")t.class!==i.class&&(t.class=fe([t.class,i.class]));else if(r==="style")t.style=as([t.style,i.style]);else if(oo(r)){const a=t[r],u=i[r];u&&a!==u&&!(me(a)&&a.includes(u))&&(t[r]=a?[].concat(a,u):u)}else r!==""&&(t[r]=i[r])}return t}function Js(e,t,s,i=null){Ps(e,t,7,[s,i])}const iv=Qd();let av=0;function lv(e,t,s){const i=e.type,r=(t?t.appContext:e.appContext)||iv,a={uid:av++,vnode:e,type:i,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Uc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:nf(i,r),emitsOptions:pf(i,r),emit:null,emitted:null,propsDefaults:tt,inheritAttrs:i.inheritAttrs,ctx:tt,data:tt,props:tt,attrs:tt,slots:tt,refs:tt,setupState:tt,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return{}.NODE_ENV!=="production"?a.ctx=m_(a):a.ctx={_:a},a.root=t?t.root:a,a.emit=Z_.bind(null,a),e.ce&&e.ce(a),a}let Tt=null;const Li=()=>Tt||bt;let Ui,$l;{const e=uo(),t=(s,i)=>{let r;return(r=e[s])||(r=e[s]=[]),r.push(i),a=>{r.length>1?r.forEach(u=>u(a)):r[0](a)}};Ui=t("__VUE_INSTANCE_SETTERS__",s=>Tt=s),$l=t("__VUE_SSR_SETTERS__",s=>Mo=s)}const Ao=e=>{const t=Tt;return Ui(e),e.scope.on(),()=>{e.scope.off(),Ui(t)}},xf=()=>{Tt&&Tt.scope.off(),Ui(null)},uv=rn("slot,component");function Hl(e,{isNativeTag:t}){(uv(e)||t(e))&&Y("Do not use built-in or reserved HTML elements as component id: "+e)}function Df(e){return e.vnode.shapeFlag&4}let Mo=!1;function cv(e,t=!1,s=!1){t&&$l(t);const{props:i,children:r}=e.vnode,a=Df(e);O_(e,i,a,t),F_(e,r,s||t);const u=a?dv(e,t):void 0;return t&&$l(!1),u}function dv(e,t){var s;const i=e.type;if({}.NODE_ENV!=="production"){if(i.name&&Hl(i.name,e.appContext.config),i.components){const a=Object.keys(i.components);for(let u=0;u<a.length;u++)Hl(a[u],e.appContext.config)}if(i.directives){const a=Object.keys(i.directives);for(let u=0;u<a.length;u++)Cd(a[u])}i.compilerOptions&&fv()&&Y('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.')}e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,zd),{}.NODE_ENV!=="production"&&g_(e);const{setup:r}=i;if(r){As();const a=e.setupContext=r.length>1?pv(e):null,u=Ao(e),d=Ir(r,e,0,[{}.NODE_ENV!=="production"?js(e.props):e.props,a]),h=Ja(d);if(Ms(),u(),(h||e.sp)&&!Pr(e)&&Ld(e),h){if(d.then(xf,xf),t)return d.then(_=>{Of(e,_,t)}).catch(_=>{_o(_,e,0)});if(e.asyncDep=d,{}.NODE_ENV!=="production"&&!e.suspense){const _=(s=i.name)!=null?s:"Anonymous";Y(`Component <${_}>: setup function returned a promise, but no <Suspense> boundary was found in the parent component tree. A component with async setup() must be nested in a <Suspense> in order to be rendered.`)}}else Of(e,d,t)}else Sf(e,t)}function Of(e,t,s){Oe(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Je(t)?({}.NODE_ENV!=="production"&&ur(t)&&Y("setup() should not return VNodes directly - return a render function instead."),{}.NODE_ENV!=="production"&&(e.devtoolsRawSetupState=t),e.setupState=ud(t),{}.NODE_ENV!=="production"&&__(e)):{}.NODE_ENV!=="production"&&t!==void 0&&Y(`setup() should return an object. Received: ${t===null?"null":typeof t}`),Sf(e,s)}let ql;const fv=()=>!ql;function Sf(e,t,s){const i=e.type;if(!e.render){if(!t&&ql&&!i.render){const r=i.template||Nl(e).template;if(r){({}).NODE_ENV!=="production"&&un(e,"compile");const{isCustomElement:a,compilerOptions:u}=e.appContext.config,{delimiters:d,compilerOptions:h}=i,_=ht(ht({isCustomElement:a,delimiters:d},u),h);i.render=ql(r,_),{}.NODE_ENV!=="production"&&cn(e,"compile")}}e.render=i.render||Ot}{const r=Ao(e);As();try{b_(e)}finally{Ms(),r()}}({}).NODE_ENV!=="production"&&!i.render&&e.render===Ot&&!t&&(i.template?Y('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):Y("Component is missing template or render function: ",i))}const Tf={}.NODE_ENV!=="production"?{get(e,t){return Ri(),St(e,"get",""),e[t]},set(){return Y("setupContext.attrs is readonly."),!1},deleteProperty(){return Y("setupContext.attrs is readonly."),!1}}:{get(e,t){return St(e,"get",""),e[t]}};function hv(e){return new Proxy(e.slots,{get(t,s){return St(e,"get","$slots"),t[s]}})}function pv(e){const t=s=>{if({}.NODE_ENV!=="production"&&(e.exposed&&Y("expose() should be called only once per setup()."),s!=null)){let i=typeof s;i==="object"&&(me(s)?i="array":Ct(s)&&(i="ref")),i!=="object"&&Y(`expose() should be passed a plain object, received ${i}.`)}e.exposed=s||{}};if({}.NODE_ENV!=="production"){let s,i;return Object.freeze({get attrs(){return s||(s=new Proxy(e.attrs,Tf))},get slots(){return i||(i=hv(e))},get emit(){return(r,...a)=>e.emit(r,...a)},expose:t})}else return{attrs:new Proxy(e.attrs,Tf),slots:e.slots,emit:e.emit,expose:t}}function Bi(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ud(hl(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in ar)return ar[s](e)},has(t,s){return s in t||s in ar}})):e.proxy}const mv=/(?:^|[-_])(\w)/g,gv=e=>e.replace(mv,t=>t.toUpperCase()).replace(/[-_]/g,"");function Wl(e,t=!0){return Oe(e)?e.displayName||e.name:e.name||t&&e.__name}function $i(e,t,s=!1){let i=Wl(t);if(!i&&t.__file){const r=t.__file.match(/([^/\\]+)\.\w+$/);r&&(i=r[1])}if(!i&&e&&e.parent){const r=a=>{for(const u in a)if(a[u]===t)return u};i=r(e.components||e.parent.type.components)||r(e.appContext.components)}return i?gv(i):s?"App":"Anonymous"}function Nf(e){return Oe(e)&&"__vccOpts"in e}const Rs=(e,t)=>{const s=S1(e,t,Mo);if({}.NODE_ENV!=="production"){const i=Li();i&&i.appContext.config.warnRecursiveComputed&&(s._warnRecursive=!0)}return s};function jl(e,t,s){const i=arguments.length;return i===2?Je(t)&&!me(t)?ur(t)?M(e,null,[t]):M(e,t):M(e,null,t):(i>3?s=Array.prototype.slice.call(arguments,2):i===3&&ur(s)&&(s=[s]),M(e,t,s))}function _v(){if({}.NODE_ENV==="production"||typeof window>"u")return;const e={style:"color:#3ba776"},t={style:"color:#1677ff"},s={style:"color:#f5222d"},i={style:"color:#eb2f96"},r={__vue_custom_formatter:!0,header(g){if(!Je(g))return null;if(g.__isVue)return["div",e,"VueInstance"];if(Ct(g)){As();const w=g.value;return Ms(),["div",{},["span",e,p(g)],"<",d(w),">"]}else{if(tr(g))return["div",{},["span",e,zt(g)?"ShallowReactive":"Reactive"],"<",d(g),`>${zs(g)?" (readonly)":""}`];if(zs(g))return["div",{},["span",e,zt(g)?"ShallowReadonly":"Readonly"],"<",d(g),">"]}return null},hasBody(g){return g&&g.__isVue},body(g){if(g&&g.__isVue)return["div",{},...a(g.$)]}};function a(g){const w=[];g.type.props&&g.props&&w.push(u("props",Me(g.props))),g.setupState!==tt&&w.push(u("setup",g.setupState)),g.data!==tt&&w.push(u("data",Me(g.data)));const x=h(g,"computed");x&&w.push(u("computed",x));const P=h(g,"inject");return P&&w.push(u("injected",P)),w.push(["div",{},["span",{style:i.style+";opacity:0.66"},"$ (internal): "],["object",{object:g}]]),w}function u(g,w){return w=ht({},w),Object.keys(w).length?["div",{style:"line-height:1.25em;margin-bottom:0.6em"},["div",{style:"color:#476582"},g],["div",{style:"padding-left:1.25em"},...Object.keys(w).map(x=>["div",{},["span",i,x+": "],d(w[x],!1)])]]:["span",{}]}function d(g,w=!0){return typeof g=="number"?["span",t,g]:typeof g=="string"?["span",s,JSON.stringify(g)]:typeof g=="boolean"?["span",i,g]:Je(g)?["object",{object:w?Me(g):g}]:["span",s,String(g)]}function h(g,w){const x=g.type;if(Oe(x))return;const P={};for(const L in g.ctx)_(x,L,w)&&(P[L]=g.ctx[L]);return P}function _(g,w,x){const P=g[x];if(me(P)&&P.includes(w)||Je(P)&&w in P||g.extends&&_(g.extends,w,x)||g.mixins&&g.mixins.some(L=>_(L,w,x)))return!0}function p(g){return zt(g)?"ShallowRef":g.effect?"ComputedRef":"Ref"}window.devtoolsFormatters?window.devtoolsFormatters.push(r):window.devtoolsFormatters=[r]}const If="3.5.17",Qs={}.NODE_ENV!=="production"?Y:Ot;/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let zl;const Af=typeof window<"u"&&window.trustedTypes;if(Af)try{zl=Af.createPolicy("vue",{createHTML:e=>e})}catch(e){({}).NODE_ENV!=="production"&&Qs(`Error creating trusted types policy: ${e}`)}const Mf=zl?e=>zl.createHTML(e):e=>e,vv="http://www.w3.org/2000/svg",bv="http://www.w3.org/1998/Math/MathML",dn=typeof document<"u"?document:null,Pf=dn&&dn.createElement("template"),yv={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,i)=>{const r=t==="svg"?dn.createElementNS(vv,e):t==="mathml"?dn.createElementNS(bv,e):s?dn.createElement(e,{is:s}):dn.createElement(e);return e==="select"&&i&&i.multiple!=null&&r.setAttribute("multiple",i.multiple),r},createText:e=>dn.createTextNode(e),createComment:e=>dn.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>dn.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,i,r,a){const u=s?s.previousSibling:t.lastChild;if(r&&(r===a||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),s),!(r===a||!(r=r.nextSibling)););else{Pf.innerHTML=Mf(i==="svg"?`<svg>${e}</svg>`:i==="mathml"?`<math>${e}</math>`:e);const d=Pf.content;if(i==="svg"||i==="mathml"){const h=d.firstChild;for(;h.firstChild;)d.appendChild(h.firstChild);d.removeChild(h)}t.insertBefore(d,s)}return[u?u.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Vn="transition",Po="animation",ko=Symbol("_vtc"),kf={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},wv=ht({},Ad,kf),Vf=(e=>(e.displayName="Transition",e.props=wv,e))((e,{slots:t})=>jl(e_,Ev(e),t)),dr=(e,t=[])=>{me(e)?e.forEach(s=>s(...t)):e&&e(...t)},Rf=e=>e?me(e)?e.some(t=>t.length>1):e.length>1:!1;function Ev(e){const t={};for(const ue in e)ue in kf||(t[ue]=e[ue]);if(e.css===!1)return t;const{name:s="v",type:i,duration:r,enterFromClass:a=`${s}-enter-from`,enterActiveClass:u=`${s}-enter-active`,enterToClass:d=`${s}-enter-to`,appearFromClass:h=a,appearActiveClass:_=u,appearToClass:p=d,leaveFromClass:g=`${s}-leave-from`,leaveActiveClass:w=`${s}-leave-active`,leaveToClass:x=`${s}-leave-to`}=e,P=Cv(r),L=P&&P[0],te=P&&P[1],{onBeforeEnter:N,onEnter:re,onEnterCancelled:J,onLeave:we,onLeaveCancelled:X,onBeforeAppear:pe=N,onAppear:be=re,onAppearCancelled:Ae=J}=t,ae=(ue,Ge,mt,ce)=>{ue._enterCancelled=ce,fr(ue,Ge?p:d),fr(ue,Ge?_:u),mt&&mt()},A=(ue,Ge)=>{ue._isLeaving=!1,fr(ue,g),fr(ue,x),fr(ue,w),Ge&&Ge()},Ee=ue=>(Ge,mt)=>{const ce=ue?be:re,it=()=>ae(Ge,ue,mt);dr(ce,[Ge,it]),Ff(()=>{fr(Ge,ue?h:a),fn(Ge,ue?p:d),Rf(ce)||Lf(Ge,i,L,it)})};return ht(t,{onBeforeEnter(ue){dr(N,[ue]),fn(ue,a),fn(ue,u)},onBeforeAppear(ue){dr(pe,[ue]),fn(ue,h),fn(ue,_)},onEnter:Ee(!1),onAppear:Ee(!0),onLeave(ue,Ge){ue._isLeaving=!0;const mt=()=>A(ue,Ge);fn(ue,g),ue._enterCancelled?(fn(ue,w),$f()):($f(),fn(ue,w)),Ff(()=>{ue._isLeaving&&(fr(ue,g),fn(ue,x),Rf(we)||Lf(ue,i,te,mt))}),dr(we,[ue,mt])},onEnterCancelled(ue){ae(ue,!1,void 0,!0),dr(J,[ue])},onAppearCancelled(ue){ae(ue,!0,void 0,!0),dr(Ae,[ue])},onLeaveCancelled(ue){A(ue),dr(X,[ue])}})}function Cv(e){if(e==null)return null;if(Je(e))return[Gl(e.enter),Gl(e.leave)];{const t=Gl(e);return[t,t]}}function Gl(e){const t=Wg(e);return{}.NODE_ENV!=="production"&&k1(t,"<transition> explicit duration"),t}function fn(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.add(s)),(e[ko]||(e[ko]=new Set)).add(t)}function fr(e,t){t.split(/\s+/).forEach(i=>i&&e.classList.remove(i));const s=e[ko];s&&(s.delete(t),s.size||(e[ko]=void 0))}function Ff(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let xv=0;function Lf(e,t,s,i){const r=e._endId=++xv,a=()=>{r===e._endId&&i()};if(s!=null)return setTimeout(a,s);const{type:u,timeout:d,propCount:h}=Dv(e,t);if(!u)return i();const _=u+"end";let p=0;const g=()=>{e.removeEventListener(_,w),a()},w=x=>{x.target===e&&++p>=h&&g()};setTimeout(()=>{p<h&&g()},d+1),e.addEventListener(_,w)}function Dv(e,t){const s=window.getComputedStyle(e),i=P=>(s[P]||"").split(", "),r=i(`${Vn}Delay`),a=i(`${Vn}Duration`),u=Uf(r,a),d=i(`${Po}Delay`),h=i(`${Po}Duration`),_=Uf(d,h);let p=null,g=0,w=0;t===Vn?u>0&&(p=Vn,g=u,w=a.length):t===Po?_>0&&(p=Po,g=_,w=h.length):(g=Math.max(u,_),p=g>0?u>_?Vn:Po:null,w=p?p===Vn?a.length:h.length:0);const x=p===Vn&&/\b(transform|all)(,|$)/.test(i(`${Vn}Property`).toString());return{type:p,timeout:g,propCount:w,hasTransform:x}}function Uf(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((s,i)=>Bf(s)+Bf(e[i])))}function Bf(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function $f(){return document.body.offsetHeight}function Ov(e,t,s){const i=e[ko];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Hi=Symbol("_vod"),Hf=Symbol("_vsh"),Kl={beforeMount(e,{value:t},{transition:s}){e[Hi]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):Vo(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:i}){!t!=!s&&(i?t?(i.beforeEnter(e),Vo(e,!0),i.enter(e)):i.leave(e,()=>{Vo(e,!1)}):Vo(e,t))},beforeUnmount(e,{value:t}){Vo(e,t)}};({}).NODE_ENV!=="production"&&(Kl.name="show");function Vo(e,t){e.style.display=t?e[Hi]:"none",e[Hf]=!t}const Sv=Symbol({}.NODE_ENV!=="production"?"CSS_VAR_TEXT":""),Tv=/(^|;)\s*display\s*:/;function Nv(e,t,s){const i=e.style,r=dt(s);let a=!1;if(s&&!r){if(t)if(dt(t))for(const u of t.split(";")){const d=u.slice(0,u.indexOf(":")).trim();s[d]==null&&qi(i,d,"")}else for(const u in t)s[u]==null&&qi(i,u,"");for(const u in s)u==="display"&&(a=!0),qi(i,u,s[u])}else if(r){if(t!==s){const u=i[Sv];u&&(s+=";"+u),i.cssText=s,a=Tv.test(s)}}else t&&e.removeAttribute("style");Hi in e&&(e[Hi]=a?i.display:"",e[Hf]&&(i.display="none"))}const Iv=/[^\\];\s*$/,qf=/\s*!important$/;function qi(e,t,s){if(me(s))s.forEach(i=>qi(e,t,i));else if(s==null&&(s=""),{}.NODE_ENV!=="production"&&Iv.test(s)&&Qs(`Unexpected semicolon at the end of '${t}' style value: '${s}'`),t.startsWith("--"))e.setProperty(t,s);else{const i=Av(e,t);qf.test(s)?e.setProperty(Tn(i),s.replace(qf,""),"important"):e[i]=s}}const Wf=["Webkit","Moz","ms"],Zl={};function Av(e,t){const s=Zl[t];if(s)return s;let i=jt(t);if(i!=="filter"&&i in e)return Zl[t]=i;i=Qn(i);for(let r=0;r<Wf.length;r++){const a=Wf[r]+i;if(a in e)return Zl[t]=a}return t}const jf="http://www.w3.org/1999/xlink";function zf(e,t,s,i,r,a=t1(t)){i&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(jf,t.slice(6,t.length)):e.setAttributeNS(jf,t,s):s==null||a&&!Rc(s)?e.removeAttribute(t):e.setAttribute(t,a?"":Ns(s)?String(s):s)}function Gf(e,t,s,i,r){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Mf(s):s);return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){const d=a==="OPTION"?e.getAttribute("value")||"":e.value,h=s==null?e.type==="checkbox"?"on":"":String(s);(d!==h||!("_value"in e))&&(e.value=h),s==null&&e.removeAttribute(t),e._value=s;return}let u=!1;if(s===""||s==null){const d=typeof e[t];d==="boolean"?s=Rc(s):s==null&&d==="string"?(s="",u=!0):d==="number"&&(s=0,u=!0)}try{e[t]=s}catch(d){({}).NODE_ENV!=="production"&&!u&&Qs(`Failed setting prop "${t}" on <${a.toLowerCase()}>: value ${s} is invalid.`,d)}u&&e.removeAttribute(r||t)}function Rn(e,t,s,i){e.addEventListener(t,s,i)}function Mv(e,t,s,i){e.removeEventListener(t,s,i)}const Kf=Symbol("_vei");function Pv(e,t,s,i,r=null){const a=e[Kf]||(e[Kf]={}),u=a[t];if(i&&u)u.value={}.NODE_ENV!=="production"?Yf(i,t):i;else{const[d,h]=kv(t);if(i){const _=a[t]=Fv({}.NODE_ENV!=="production"?Yf(i,t):i,r);Rn(e,d,_,h)}else u&&(Mv(e,d,u,h),a[t]=void 0)}}const Zf=/(?:Once|Passive|Capture)$/;function kv(e){let t;if(Zf.test(e)){t={};let i;for(;i=e.match(Zf);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Tn(e.slice(2)),t]}let Yl=0;const Vv=Promise.resolve(),Rv=()=>Yl||(Vv.then(()=>Yl=0),Yl=Date.now());function Fv(e,t){const s=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=s.attached)return;Ps(Lv(i,s.value),t,5,[i])};return s.value=e,s.attached=Rv(),s}function Yf(e,t){return Oe(e)||me(e)?e:(Qs(`Wrong type passed as event handler to ${t} - did you forget @ or : in front of your prop?
Expected function or array of functions, received type ${typeof e}.`),Ot)}function Lv(e,t){if(me(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(i=>r=>!r._stopped&&i&&i(r))}else return t}const Jf=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Uv=(e,t,s,i,r,a)=>{const u=r==="svg";t==="class"?Ov(e,i,u):t==="style"?Nv(e,s,i):oo(t)?li(t)||Pv(e,t,s,i,a):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Bv(e,t,i,u))?(Gf(e,t,i),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&zf(e,t,i,u,a,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!dt(i))?Gf(e,jt(t),i,a,t):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),zf(e,t,i,u))};function Bv(e,t,s,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&Jf(t)&&Oe(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Jf(t)&&dt(s)?!1:t in e}const Rr=e=>{const t=e.props["onUpdate:modelValue"]||!1;return me(t)?s=>Tr(t,s):t};function $v(e){e.target.composing=!0}function Qf(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const hn=Symbol("_assign"),Yt={created(e,{modifiers:{lazy:t,trim:s,number:i}},r){e[hn]=Rr(r);const a=i||r.props&&r.props.type==="number";Rn(e,t?"change":"input",u=>{if(u.target.composing)return;let d=e.value;s&&(d=d.trim()),a&&(d=ci(d)),e[hn](d)}),s&&Rn(e,"change",()=>{e.value=e.value.trim()}),t||(Rn(e,"compositionstart",$v),Rn(e,"compositionend",Qf),Rn(e,"change",Qf))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:i,trim:r,number:a}},u){if(e[hn]=Rr(u),e.composing)return;const d=(a||e.type==="number")&&!/^0\d/.test(e.value)?ci(e.value):e.value,h=t??"";d!==h&&(document.activeElement===e&&e.type!=="range"&&(i&&t===s||r&&e.value.trim()===h)||(e.value=h))}},Wi={deep:!0,created(e,t,s){e[hn]=Rr(s),Rn(e,"change",()=>{const i=e._modelValue,r=Ro(e),a=e.checked,u=e[hn];if(me(i)){const d=el(i,r),h=d!==-1;if(a&&!h)u(i.concat(r));else if(!a&&h){const _=[...i];_.splice(d,1),u(_)}}else if(Sr(i)){const d=new Set(i);a?d.add(r):d.delete(r),u(d)}else u(th(e,a))})},mounted:Xf,beforeUpdate(e,t,s){e[hn]=Rr(s),Xf(e,t,s)}};function Xf(e,{value:t,oldValue:s},i){e._modelValue=t;let r;if(me(t))r=el(t,i.props.value)>-1;else if(Sr(t))r=t.has(i.props.value);else{if(t===s)return;r=co(t,th(e,!0))}e.checked!==r&&(e.checked=r)}const Jl={deep:!0,created(e,{value:t,modifiers:{number:s}},i){const r=Sr(t);Rn(e,"change",()=>{const a=Array.prototype.filter.call(e.options,u=>u.selected).map(u=>s?ci(Ro(u)):Ro(u));e[hn](e.multiple?r?new Set(a):a:a[0]),e._assigning=!0,gl(()=>{e._assigning=!1})}),e[hn]=Rr(i)},mounted(e,{value:t}){eh(e,t)},beforeUpdate(e,t,s){e[hn]=Rr(s)},updated(e,{value:t}){e._assigning||eh(e,t)}};function eh(e,t){const s=e.multiple,i=me(t);if(s&&!i&&!Sr(t)){({}).NODE_ENV!=="production"&&Qs(`<select multiple v-model> expects an Array or Set value for its binding, but got ${Object.prototype.toString.call(t).slice(8,-1)}.`);return}for(let r=0,a=e.options.length;r<a;r++){const u=e.options[r],d=Ro(u);if(s)if(i){const h=typeof d;h==="string"||h==="number"?u.selected=t.some(_=>String(_)===String(d)):u.selected=el(t,d)>-1}else u.selected=t.has(d);else if(co(Ro(u),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}function Ro(e){return"_value"in e?e._value:e.value}function th(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const Hv=["ctrl","shift","alt","meta"],qv={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Hv.some(s=>e[`${s}Key`]&&!t.includes(s))},Pt=(e,t)=>{const s=e._withMods||(e._withMods={}),i=t.join(".");return s[i]||(s[i]=(r,...a)=>{for(let u=0;u<t.length;u++){const d=qv[t[u]];if(d&&d(r,t))return}return e(r,...a)})},Wv=ht({patchProp:Uv},yv);let sh;function jv(){return sh||(sh=B_(Wv))}const zv=(...e)=>{const t=jv().createApp(...e);({}).NODE_ENV!=="production"&&(Kv(t),Zv(t));const{mount:s}=t;return t.mount=i=>{const r=Yv(i);if(!r)return;const a=t._component;!Oe(a)&&!a.render&&!a.template&&(a.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const u=s(r,!1,Gv(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),u},t};function Gv(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Kv(e){Object.defineProperty(e.config,"isNativeTag",{value:t=>Qg(t)||Xg(t)||e1(t),writable:!1})}function Zv(e){{const t=e.config.isCustomElement;Object.defineProperty(e.config,"isCustomElement",{get(){return t},set(){Qs("The `isCustomElement` config option is deprecated. Use `compilerOptions.isCustomElement` instead.")}});const s=e.config.compilerOptions,i='The `compilerOptions` config option is only respected when using a build of Vue.js that includes the runtime compiler (aka "full build"). Since you are using the runtime-only build, `compilerOptions` must be passed to `@vue/compiler-dom` in the build setup instead.\n- For vue-loader: pass it via vue-loader\'s `compilerOptions` loader option.\n- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader\n- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-sfc';Object.defineProperty(e.config,"compilerOptions",{get(){return Qs(i),s},set(){Qs(i)}})}}function Yv(e){if(dt(e)){const t=document.querySelector(e);return{}.NODE_ENV!=="production"&&!t&&Qs(`Failed to mount app: mount target selector "${e}" returned null.`),t}return{}.NODE_ENV!=="production"&&window.ShadowRoot&&e instanceof window.ShadowRoot&&e.mode==="closed"&&Qs('mounting on a ShadowRoot with `{mode: "closed"}` may lead to unpredictable bugs'),e}/**
* vue v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Jv(){_v()}({}).NODE_ENV!=="production"&&Jv();var Qv=!1;function Xv(){return nh().__VUE_DEVTOOLS_GLOBAL_HOOK__}function nh(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const e0=typeof Proxy=="function",t0="devtools-plugin:setup",s0="plugin:settings:set";let Fr,Ql;function n0(){var e;return Fr!==void 0||(typeof window<"u"&&window.performance?(Fr=!0,Ql=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(Fr=!0,Ql=globalThis.perf_hooks.performance):Fr=!1),Fr}function r0(){return n0()?Ql.now():Date.now()}class o0{constructor(t,s){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=s;const i={};if(t.settings)for(const u in t.settings){const d=t.settings[u];i[u]=d.defaultValue}const r=`__vue-devtools-plugin-settings__${t.id}`;let a=Object.assign({},i);try{const u=localStorage.getItem(r),d=JSON.parse(u);Object.assign(a,d)}catch{}this.fallbacks={getSettings(){return a},setSettings(u){try{localStorage.setItem(r,JSON.stringify(u))}catch{}a=u},now(){return r0()}},s&&s.on(s0,(u,d)=>{u===this.plugin.id&&this.fallbacks.setSettings(d)}),this.proxiedOn=new Proxy({},{get:(u,d)=>this.target?this.target.on[d]:(...h)=>{this.onQueue.push({method:d,args:h})}}),this.proxiedTarget=new Proxy({},{get:(u,d)=>this.target?this.target[d]:d==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(d)?(...h)=>(this.targetQueue.push({method:d,args:h,resolve:()=>{}}),this.fallbacks[d](...h)):(...h)=>new Promise(_=>{this.targetQueue.push({method:d,args:h,resolve:_})})})}async setRealTarget(t){this.target=t;for(const s of this.onQueue)this.target.on[s.method](...s.args);for(const s of this.targetQueue)s.resolve(await this.target[s.method](...s.args))}}function Xl(e,t){const s=e,i=nh(),r=Xv(),a=e0&&s.enableEarlyProxy;if(r&&(i.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!a))r.emit(t0,e,t);else{const u=a?new o0(s,r):null;(i.__VUE_DEVTOOLS_PLUGINS__=i.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:s,setupFn:t,proxy:u}),u&&t(u.proxiedTarget)}}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const i0={}.NODE_ENV!=="production"?Symbol("pinia"):Symbol();var hr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(hr||(hr={}));const eu=typeof window<"u",rh=(()=>typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof global=="object"&&global.global===global?global:typeof globalThis=="object"?globalThis:{HTMLElement:null})();function a0(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}function tu(e,t,s){const i=new XMLHttpRequest;i.open("GET",e),i.responseType="blob",i.onload=function(){ah(i.response,t,s)},i.onerror=function(){console.error("could not download file")},i.send()}function oh(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function ji(e){try{e.dispatchEvent(new MouseEvent("click"))}catch{const s=document.createEvent("MouseEvents");s.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(s)}}const zi=typeof navigator=="object"?navigator:{userAgent:""},ih=(()=>/Macintosh/.test(zi.userAgent)&&/AppleWebKit/.test(zi.userAgent)&&!/Safari/.test(zi.userAgent))(),ah=eu?typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype&&!ih?l0:"msSaveOrOpenBlob"in zi?u0:c0:()=>{};function l0(e,t="download",s){const i=document.createElement("a");i.download=t,i.rel="noopener",typeof e=="string"?(i.href=e,i.origin!==location.origin?oh(i.href)?tu(e,t,s):(i.target="_blank",ji(i)):ji(i)):(i.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(i.href)},4e4),setTimeout(function(){ji(i)},0))}function u0(e,t="download",s){if(typeof e=="string")if(oh(e))tu(e,t,s);else{const i=document.createElement("a");i.href=e,i.target="_blank",setTimeout(function(){ji(i)})}else navigator.msSaveOrOpenBlob(a0(e,s),t)}function c0(e,t,s,i){if(i=i||open("","_blank"),i&&(i.document.title=i.document.body.innerText="downloading..."),typeof e=="string")return tu(e,t,s);const r=e.type==="application/octet-stream",a=/constructor/i.test(String(rh.HTMLElement))||"safari"in rh,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||r&&a||ih)&&typeof FileReader<"u"){const d=new FileReader;d.onloadend=function(){let h=d.result;if(typeof h!="string")throw i=null,new Error("Wrong reader.result type");h=u?h:h.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=h:location.assign(h),i=null},d.readAsDataURL(e)}else{const d=URL.createObjectURL(e);i?i.location.assign(d):location.href=d,i=null,setTimeout(function(){URL.revokeObjectURL(d)},4e4)}}function kt(e,t){const s="🍍 "+e;typeof __VUE_DEVTOOLS_TOAST__=="function"?__VUE_DEVTOOLS_TOAST__(s,t):t==="error"?console.error(s):t==="warn"?console.warn(s):console.log(s)}function su(e){return"_a"in e&&"install"in e}function lh(){if(!("clipboard"in navigator))return kt("Your browser doesn't support the Clipboard API","error"),!0}function uh(e){return e instanceof Error&&e.message.toLowerCase().includes("document is not focused")?(kt('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0):!1}async function d0(e){if(!lh())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),kt("Global state copied to clipboard.")}catch(t){if(uh(t))return;kt("Failed to serialize the state. Check the console for more details.","error"),console.error(t)}}async function f0(e){if(!lh())try{ch(e,JSON.parse(await navigator.clipboard.readText())),kt("Global state pasted from clipboard.")}catch(t){if(uh(t))return;kt("Failed to deserialize the state from clipboard. Check the console for more details.","error"),console.error(t)}}async function h0(e){try{ah(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){kt("Failed to export the state as JSON. Check the console for more details.","error"),console.error(t)}}let pn;function p0(){pn||(pn=document.createElement("input"),pn.type="file",pn.accept=".json");function e(){return new Promise((t,s)=>{pn.onchange=async()=>{const i=pn.files;if(!i)return t(null);const r=i.item(0);return t(r?{text:await r.text(),file:r}:null)},pn.oncancel=()=>t(null),pn.onerror=s,pn.click()})}return e}async function m0(e){try{const s=await p0()();if(!s)return;const{text:i,file:r}=s;ch(e,JSON.parse(i)),kt(`Global state imported from "${r.name}".`)}catch(t){kt("Failed to import the state from JSON. Check the console for more details.","error"),console.error(t)}}function ch(e,t){for(const s in t){const i=e.state.value[s];i?Object.assign(i,t[s]):e.state.value[s]=t[s]}}function Fs(e){return{_custom:{display:e}}}const dh="🍍 Pinia (root)",Gi="_root";function g0(e){return su(e)?{id:Gi,label:dh}:{id:e.$id,label:e.$id}}function _0(e){if(su(e)){const s=Array.from(e._s.keys()),i=e._s;return{state:s.map(a=>({editable:!0,key:a,value:e.state.value[a]})),getters:s.filter(a=>i.get(a)._getters).map(a=>{const u=i.get(a);return{editable:!1,key:a,value:u._getters.reduce((d,h)=>(d[h]=u[h],d),{})}})}}const t={state:Object.keys(e.$state).map(s=>({editable:!0,key:s,value:e.$state[s]}))};return e._getters&&e._getters.length&&(t.getters=e._getters.map(s=>({editable:!1,key:s,value:e[s]}))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map(s=>({editable:!0,key:s,value:e[s]}))),t}function v0(e){return e?Array.isArray(e)?e.reduce((t,s)=>(t.keys.push(s.key),t.operations.push(s.type),t.oldValue[s.key]=s.oldValue,t.newValue[s.key]=s.newValue,t),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:Fs(e.type),key:Fs(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function b0(e){switch(e){case hr.direct:return"mutation";case hr.patchFunction:return"$patch";case hr.patchObject:return"$patch";default:return"unknown"}}let Lr=!0;const Ki=[],pr="pinia:mutations",Ht="pinia",{assign:y0}=Object,Zi=e=>"🍍 "+e;function w0(e,t){Xl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:Ki,app:e},s=>{typeof s.now!="function"&&kt("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),s.addTimelineLayer({id:pr,label:"Pinia 🍍",color:15064968}),s.addInspector({id:Ht,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{d0(t)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await f0(t),s.sendInspectorTree(Ht),s.sendInspectorState(Ht)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{h0(t)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await m0(t),s.sendInspectorTree(Ht),s.sendInspectorState(Ht)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:i=>{const r=t._s.get(i);r?typeof r.$reset!="function"?kt(`Cannot reset "${i}" store because it doesn't have a "$reset" method implemented.`,"warn"):(r.$reset(),kt(`Store "${i}" reset.`)):kt(`Cannot reset "${i}" store because it wasn't found.`,"warn")}}]}),s.on.inspectComponent((i,r)=>{const a=i.componentInstance&&i.componentInstance.proxy;if(a&&a._pStores){const u=i.componentInstance.proxy._pStores;Object.values(u).forEach(d=>{i.instanceData.state.push({type:Zi(d.$id),key:"state",editable:!0,value:d._isOptionsAPI?{_custom:{value:Me(d.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>d.$reset()}]}}:Object.keys(d.$state).reduce((h,_)=>(h[_]=d.$state[_],h),{})}),d._getters&&d._getters.length&&i.instanceData.state.push({type:Zi(d.$id),key:"getters",editable:!1,value:d._getters.reduce((h,_)=>{try{h[_]=d[_]}catch(p){h[_]=p}return h},{})})})}}),s.on.getInspectorTree(i=>{if(i.app===e&&i.inspectorId===Ht){let r=[t];r=r.concat(Array.from(t._s.values())),i.rootNodes=(i.filter?r.filter(a=>"$id"in a?a.$id.toLowerCase().includes(i.filter.toLowerCase()):dh.toLowerCase().includes(i.filter.toLowerCase())):r).map(g0)}}),globalThis.$pinia=t,s.on.getInspectorState(i=>{if(i.app===e&&i.inspectorId===Ht){const r=i.nodeId===Gi?t:t._s.get(i.nodeId);if(!r)return;r&&(i.nodeId!==Gi&&(globalThis.$store=Me(r)),i.state=_0(r))}}),s.on.editInspectorState((i,r)=>{if(i.app===e&&i.inspectorId===Ht){const a=i.nodeId===Gi?t:t._s.get(i.nodeId);if(!a)return kt(`store "${i.nodeId}" not found`,"error");const{path:u}=i;su(a)?u.unshift("state"):(u.length!==1||!a._customProperties.has(u[0])||u[0]in a.$state)&&u.unshift("$state"),Lr=!1,i.set(a,u,i.state.value),Lr=!0}}),s.on.editComponentState(i=>{if(i.type.startsWith("🍍")){const r=i.type.replace(/^🍍\s*/,""),a=t._s.get(r);if(!a)return kt(`store "${r}" not found`,"error");const{path:u}=i;if(u[0]!=="state")return kt(`Invalid path for store "${r}":
${u}
Only state can be modified.`);u[0]="$state",Lr=!1,i.set(a,u,i.state.value),Lr=!0}})})}function E0(e,t){Ki.includes(Zi(t.$id))||Ki.push(Zi(t.$id)),Xl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:Ki,app:e,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},s=>{const i=typeof s.now=="function"?s.now.bind(s):Date.now;t.$onAction(({after:u,onError:d,name:h,args:_})=>{const p=fh++;s.addTimelineEvent({layerId:pr,event:{time:i(),title:"🛫 "+h,subtitle:"start",data:{store:Fs(t.$id),action:Fs(h),args:_},groupId:p}}),u(g=>{Fn=void 0,s.addTimelineEvent({layerId:pr,event:{time:i(),title:"🛬 "+h,subtitle:"end",data:{store:Fs(t.$id),action:Fs(h),args:_,result:g},groupId:p}})}),d(g=>{Fn=void 0,s.addTimelineEvent({layerId:pr,event:{time:i(),logType:"error",title:"💥 "+h,subtitle:"end",data:{store:Fs(t.$id),action:Fs(h),args:_,error:g},groupId:p}})})},!0),t._customProperties.forEach(u=>{Vr(()=>In(t[u]),(d,h)=>{s.notifyComponentUpdate(),s.sendInspectorState(Ht),Lr&&s.addTimelineEvent({layerId:pr,event:{time:i(),title:"Change",subtitle:u,data:{newValue:d,oldValue:h},groupId:Fn}})},{deep:!0})}),t.$subscribe(({events:u,type:d},h)=>{if(s.notifyComponentUpdate(),s.sendInspectorState(Ht),!Lr)return;const _={time:i(),title:b0(d),data:y0({store:Fs(t.$id)},v0(u)),groupId:Fn};d===hr.patchFunction?_.subtitle="⤵️":d===hr.patchObject?_.subtitle="🧩":u&&!Array.isArray(u)&&(_.subtitle=u.type),u&&(_.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:u}}),s.addTimelineEvent({layerId:pr,event:_})},{detached:!0,flush:"sync"});const r=t._hotUpdate;t._hotUpdate=hl(u=>{r(u),s.addTimelineEvent({layerId:pr,event:{time:i(),title:"🔥 "+t.$id,subtitle:"HMR update",data:{store:Fs(t.$id),info:Fs("HMR update")}}}),s.notifyComponentUpdate(),s.sendInspectorTree(Ht),s.sendInspectorState(Ht)});const{$dispose:a}=t;t.$dispose=()=>{a(),s.notifyComponentUpdate(),s.sendInspectorTree(Ht),s.sendInspectorState(Ht),s.getSettings().logStoreChanges&&kt(`Disposed "${t.$id}" store 🗑`)},s.notifyComponentUpdate(),s.sendInspectorTree(Ht),s.sendInspectorState(Ht),s.getSettings().logStoreChanges&&kt(`"${t.$id}" store installed 🆕`)})}let fh=0,Fn;function hh(e,t,s){const i=t.reduce((r,a)=>(r[a]=Me(e)[a],r),{});for(const r in i)e[r]=function(){const a=fh,u=s?new Proxy(e,{get(...h){return Fn=a,Reflect.get(...h)},set(...h){return Fn=a,Reflect.set(...h)}}):e;Fn=a;const d=i[r].apply(u,arguments);return Fn=void 0,d}}function C0({app:e,store:t,options:s}){if(!t.$id.startsWith("__hot:")){if(t._isOptionsAPI=!!s.state,!t._p._testing){hh(t,Object.keys(s.actions),t._isOptionsAPI);const i=t._hotUpdate;Me(t)._hotUpdate=function(r){i.apply(this,arguments),hh(t,Object.keys(r._hmrPayload.actions),!!t._isOptionsAPI)}}E0(e,t)}}function x0(){const e=n1(!0),t=e.run(()=>ad({}));let s=[],i=[];const r=hl({install(a){r._a=a,a.provide(i0,r),a.config.globalProperties.$pinia=r,{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&eu&&w0(a,r),i.forEach(u=>s.push(u)),i=[]},use(a){return!this._a&&!Qv?i.push(a):s.push(a),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&eu&&typeof Proxy<"u"&&r.use(C0),r}const lM="",Fe=(e,t)=>{const s=e.__vccOpts||e;for(const[i,r]of t)s[i]=r;return s},D0={name:"App",mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}}},O0={id:"app"};function S0(e,t,s,i,r,a){const u=G("router-view");return D(),S("div",O0,[M(u)])}const T0=Fe(D0,[["render",S0]]);/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const mn=typeof document<"u";function ph(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function N0(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&ph(e.default)}const Qe=Object.assign;function nu(e,t){const s={};for(const i in t){const r=t[i];s[i]=us(r)?r.map(e):e(r)}return s}const Fo=()=>{},us=Array.isArray;function $e(e){const t=Array.from(arguments).slice(1);console.warn.apply(console,["[Vue Router warn]: "+e].concat(t))}const mh=/#/g,I0=/&/g,A0=/\//g,M0=/=/g,P0=/\?/g,gh=/\+/g,k0=/%5B/g,V0=/%5D/g,_h=/%5E/g,R0=/%60/g,vh=/%7B/g,F0=/%7C/g,bh=/%7D/g,L0=/%20/g;function ru(e){return encodeURI(""+e).replace(F0,"|").replace(k0,"[").replace(V0,"]")}function U0(e){return ru(e).replace(vh,"{").replace(bh,"}").replace(_h,"^")}function ou(e){return ru(e).replace(gh,"%2B").replace(L0,"+").replace(mh,"%23").replace(I0,"%26").replace(R0,"`").replace(vh,"{").replace(bh,"}").replace(_h,"^")}function B0(e){return ou(e).replace(M0,"%3D")}function $0(e){return ru(e).replace(mh,"%23").replace(P0,"%3F")}function H0(e){return e==null?"":$0(e).replace(A0,"%2F")}function Ur(e){try{return decodeURIComponent(""+e)}catch{({}).NODE_ENV!=="production"&&$e(`Error decoding "${e}". Using original value`)}return""+e}const q0=/\/$/,W0=e=>e.replace(q0,"");function iu(e,t,s="/"){let i,r={},a="",u="";const d=t.indexOf("#");let h=t.indexOf("?");return d<h&&d>=0&&(h=-1),h>-1&&(i=t.slice(0,h),a=t.slice(h+1,d>-1?d:t.length),r=e(a)),d>-1&&(i=i||t.slice(0,d),u=t.slice(d,t.length)),i=G0(i??t,s),{fullPath:i+(a&&"?")+a+u,path:i,query:r,hash:Ur(u)}}function j0(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function yh(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function wh(e,t,s){const i=t.matched.length-1,r=s.matched.length-1;return i>-1&&i===r&&Ln(t.matched[i],s.matched[r])&&Eh(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Ln(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Eh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!z0(e[s],t[s]))return!1;return!0}function z0(e,t){return us(e)?Ch(e,t):us(t)?Ch(t,e):e===t}function Ch(e,t){return us(t)?e.length===t.length&&e.every((s,i)=>s===t[i]):e.length===1&&e[0]===t}function G0(e,t){if(e.startsWith("/"))return e;if({}.NODE_ENV!=="production"&&!t.startsWith("/"))return $e(`Cannot resolve a relative location without an absolute path. Trying to resolve "${e}" from "${t}". It should look like "/${t}".`),e;if(!e)return t;const s=t.split("/"),i=e.split("/"),r=i[i.length-1];(r===".."||r===".")&&i.push("");let a=s.length-1,u,d;for(u=0;u<i.length;u++)if(d=i[u],d!==".")if(d==="..")a>1&&a--;else break;return s.slice(0,a).join("/")+"/"+i.slice(u).join("/")}const Un={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Lo;(function(e){e.pop="pop",e.push="push"})(Lo||(Lo={}));var Uo;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Uo||(Uo={}));function K0(e){if(!e)if(mn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),W0(e)}const Z0=/^[^#]+#/;function Y0(e,t){return e.replace(Z0,"#")+t}function J0(e,t){const s=document.documentElement.getBoundingClientRect(),i=e.getBoundingClientRect();return{behavior:t.behavior,left:i.left-s.left-(t.left||0),top:i.top-s.top-(t.top||0)}}const Yi=()=>({left:window.scrollX,top:window.scrollY});function Q0(e){let t;if("el"in e){const s=e.el,i=typeof s=="string"&&s.startsWith("#");if({}.NODE_ENV!=="production"&&typeof e.el=="string"&&(!i||!document.getElementById(e.el.slice(1))))try{const a=document.querySelector(e.el);if(i&&a){$e(`The selector "${e.el}" should be passed as "el: document.querySelector('${e.el}')" because it starts with "#".`);return}}catch{$e(`The selector "${e.el}" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);return}const r=typeof s=="string"?i?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!r){({}).NODE_ENV!=="production"&&$e(`Couldn't find element using selector "${e.el}" returned by scrollBehavior.`);return}t=J0(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function xh(e,t){return(history.state?history.state.position-t:-1)+e}const au=new Map;function X0(e,t){au.set(e,t)}function eb(e){const t=au.get(e);return au.delete(e),t}let tb=()=>location.protocol+"//"+location.host;function Dh(e,t){const{pathname:s,search:i,hash:r}=t,a=e.indexOf("#");if(a>-1){let d=r.includes(e.slice(a))?e.slice(a).length:1,h=r.slice(d);return h[0]!=="/"&&(h="/"+h),yh(h,"")}return yh(s,e)+i+r}function sb(e,t,s,i){let r=[],a=[],u=null;const d=({state:w})=>{const x=Dh(e,location),P=s.value,L=t.value;let te=0;if(w){if(s.value=x,t.value=w,u&&u===P){u=null;return}te=L?w.position-L.position:0}else i(x);r.forEach(N=>{N(s.value,P,{delta:te,type:Lo.pop,direction:te?te>0?Uo.forward:Uo.back:Uo.unknown})})};function h(){u=s.value}function _(w){r.push(w);const x=()=>{const P=r.indexOf(w);P>-1&&r.splice(P,1)};return a.push(x),x}function p(){const{history:w}=window;w.state&&w.replaceState(Qe({},w.state,{scroll:Yi()}),"")}function g(){for(const w of a)w();a=[],window.removeEventListener("popstate",d),window.removeEventListener("beforeunload",p)}return window.addEventListener("popstate",d),window.addEventListener("beforeunload",p,{passive:!0}),{pauseListeners:h,listen:_,destroy:g}}function Oh(e,t,s,i=!1,r=!1){return{back:e,current:t,forward:s,replaced:i,position:window.history.length,scroll:r?Yi():null}}function nb(e){const{history:t,location:s}=window,i={value:Dh(e,s)},r={value:t.state};r.value||a(i.value,{back:null,current:i.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(h,_,p){const g=e.indexOf("#"),w=g>-1?(s.host&&document.querySelector("base")?e:e.slice(g))+h:tb()+e+h;try{t[p?"replaceState":"pushState"](_,"",w),r.value=_}catch(x){({}).NODE_ENV!=="production"?$e("Error with push/replace State",x):console.error(x),s[p?"replace":"assign"](w)}}function u(h,_){const p=Qe({},t.state,Oh(r.value.back,h,r.value.forward,!0),_,{position:r.value.position});a(h,p,!0),i.value=h}function d(h,_){const p=Qe({},r.value,t.state,{forward:h,scroll:Yi()});({}).NODE_ENV!=="production"&&!t.state&&$e(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:

history.replaceState(history.state, '', url)

You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`),a(p.current,p,!0);const g=Qe({},Oh(i.value,h,null),{position:p.position+1},_);a(h,g,!1),i.value=h}return{location:i,state:r,push:d,replace:u}}function rb(e){e=K0(e);const t=nb(e),s=sb(e,t.state,t.location,t.replace);function i(a,u=!0){u||s.pauseListeners(),history.go(a)}const r=Qe({location:"",base:e,go:i,createHref:Y0.bind(null,e)},t,s);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Ji(e){return typeof e=="string"||e&&typeof e=="object"}function Sh(e){return typeof e=="string"||typeof e=="symbol"}const lu=Symbol({}.NODE_ENV!=="production"?"navigation failure":"");var Th;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Th||(Th={}));const ob={1({location:e,currentLocation:t}){return`No match for
 ${JSON.stringify(e)}${t?`
while being at
`+JSON.stringify(t):""}`},2({from:e,to:t}){return`Redirected from "${e.fullPath}" to "${ab(t)}" via a navigation guard.`},4({from:e,to:t}){return`Navigation aborted from "${e.fullPath}" to "${t.fullPath}" via a navigation guard.`},8({from:e,to:t}){return`Navigation cancelled from "${e.fullPath}" to "${t.fullPath}" with a new navigation.`},16({from:e,to:t}){return`Avoided redundant navigation to current location: "${e.fullPath}".`}};function Br(e,t){return{}.NODE_ENV!=="production"?Qe(new Error(ob[e](t)),{type:e,[lu]:!0},t):Qe(new Error,{type:e,[lu]:!0},t)}function gn(e,t){return e instanceof Error&&lu in e&&(t==null||!!(e.type&t))}const ib=["params","query","hash"];function ab(e){if(typeof e=="string")return e;if(e.path!=null)return e.path;const t={};for(const s of ib)s in e&&(t[s]=e[s]);return JSON.stringify(t,null,2)}const Nh="[^/]+?",lb={sensitive:!1,strict:!1,start:!0,end:!0},ub=/[.+*?^${}()[\]/\\]/g;function cb(e,t){const s=Qe({},lb,t),i=[];let r=s.start?"^":"";const a=[];for(const _ of e){const p=_.length?[]:[90];s.strict&&!_.length&&(r+="/");for(let g=0;g<_.length;g++){const w=_[g];let x=40+(s.sensitive?.25:0);if(w.type===0)g||(r+="/"),r+=w.value.replace(ub,"\\$&"),x+=40;else if(w.type===1){const{value:P,repeatable:L,optional:te,regexp:N}=w;a.push({name:P,repeatable:L,optional:te});const re=N||Nh;if(re!==Nh){x+=10;try{new RegExp(`(${re})`)}catch(we){throw new Error(`Invalid custom RegExp for param "${P}" (${re}): `+we.message)}}let J=L?`((?:${re})(?:/(?:${re}))*)`:`(${re})`;g||(J=te&&_.length<2?`(?:/${J})`:"/"+J),te&&(J+="?"),r+=J,x+=20,te&&(x+=-8),L&&(x+=-20),re===".*"&&(x+=-50)}p.push(x)}i.push(p)}if(s.strict&&s.end){const _=i.length-1;i[_][i[_].length-1]+=.7000000000000001}s.strict||(r+="/?"),s.end?r+="$":s.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const u=new RegExp(r,s.sensitive?"":"i");function d(_){const p=_.match(u),g={};if(!p)return null;for(let w=1;w<p.length;w++){const x=p[w]||"",P=a[w-1];g[P.name]=x&&P.repeatable?x.split("/"):x}return g}function h(_){let p="",g=!1;for(const w of e){(!g||!p.endsWith("/"))&&(p+="/"),g=!1;for(const x of w)if(x.type===0)p+=x.value;else if(x.type===1){const{value:P,repeatable:L,optional:te}=x,N=P in _?_[P]:"";if(us(N)&&!L)throw new Error(`Provided param "${P}" is an array but it is not repeatable (* or + modifiers)`);const re=us(N)?N.join("/"):N;if(!re)if(te)w.length<2&&(p.endsWith("/")?p=p.slice(0,-1):g=!0);else throw new Error(`Missing required param "${P}"`);p+=re}}return p||"/"}return{re:u,score:i,keys:a,parse:d,stringify:h}}function db(e,t){let s=0;for(;s<e.length&&s<t.length;){const i=t[s]-e[s];if(i)return i;s++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function Ih(e,t){let s=0;const i=e.score,r=t.score;for(;s<i.length&&s<r.length;){const a=db(i[s],r[s]);if(a)return a;s++}if(Math.abs(r.length-i.length)===1){if(Ah(i))return 1;if(Ah(r))return-1}return r.length-i.length}function Ah(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const fb={type:0,value:""},hb=/[a-zA-Z0-9_]/;function pb(e){if(!e)return[[]];if(e==="/")return[[fb]];if(!e.startsWith("/"))throw new Error({}.NODE_ENV!=="production"?`Route paths should start with a "/": "${e}" should be "/${e}".`:`Invalid path "${e}"`);function t(x){throw new Error(`ERR (${s})/"${_}": ${x}`)}let s=0,i=s;const r=[];let a;function u(){a&&r.push(a),a=[]}let d=0,h,_="",p="";function g(){_&&(s===0?a.push({type:0,value:_}):s===1||s===2||s===3?(a.length>1&&(h==="*"||h==="+")&&t(`A repeatable param (${_}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:_,regexp:p,repeatable:h==="*"||h==="+",optional:h==="*"||h==="?"})):t("Invalid state to consume buffer"),_="")}function w(){_+=h}for(;d<e.length;){if(h=e[d++],h==="\\"&&s!==2){i=s,s=4;continue}switch(s){case 0:h==="/"?(_&&g(),u()):h===":"?(g(),s=1):w();break;case 4:w(),s=i;break;case 1:h==="("?s=2:hb.test(h)?w():(g(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&d--);break;case 2:h===")"?p[p.length-1]=="\\"?p=p.slice(0,-1)+h:s=3:p+=h;break;case 3:g(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&d--,p="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${_}"`),g(),u(),r}function mb(e,t,s){const i=cb(pb(e.path),s);if({}.NODE_ENV!=="production"){const a=new Set;for(const u of i.keys)a.has(u.name)&&$e(`Found duplicated params with name "${u.name}" for path "${e.path}". Only the last one will be available on "$route.params".`),a.add(u.name)}const r=Qe(i,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function gb(e,t){const s=[],i=new Map;t=Vh({strict:!1,end:!0,sensitive:!1},t);function r(g){return i.get(g)}function a(g,w,x){const P=!x,L=Ph(g);({}).NODE_ENV!=="production"&&yb(L,w),L.aliasOf=x&&x.record;const te=Vh(t,g),N=[L];if("alias"in g){const we=typeof g.alias=="string"?[g.alias]:g.alias;for(const X of we)N.push(Ph(Qe({},L,{components:x?x.record.components:L.components,path:X,aliasOf:x?x.record:L})))}let re,J;for(const we of N){const{path:X}=we;if(w&&X[0]!=="/"){const pe=w.record.path,be=pe[pe.length-1]==="/"?"":"/";we.path=w.record.path+(X&&be+X)}if({}.NODE_ENV!=="production"&&we.path==="*")throw new Error(`Catch all routes ("*") must now be defined using a param with a custom regexp.
See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.`);if(re=mb(we,w,te),{}.NODE_ENV!=="production"&&w&&X[0]==="/"&&Eb(re,w),x?(x.alias.push(re),{}.NODE_ENV!=="production"&&bb(x,re)):(J=J||re,J!==re&&J.alias.push(re),P&&g.name&&!kh(re)&&({}.NODE_ENV!=="production"&&wb(g,w),u(g.name))),Rh(re)&&h(re),L.children){const pe=L.children;for(let be=0;be<pe.length;be++)a(pe[be],re,x&&x.children[be])}x=x||re}return J?()=>{u(J)}:Fo}function u(g){if(Sh(g)){const w=i.get(g);w&&(i.delete(g),s.splice(s.indexOf(w),1),w.children.forEach(u),w.alias.forEach(u))}else{const w=s.indexOf(g);w>-1&&(s.splice(w,1),g.record.name&&i.delete(g.record.name),g.children.forEach(u),g.alias.forEach(u))}}function d(){return s}function h(g){const w=Cb(g,s);s.splice(w,0,g),g.record.name&&!kh(g)&&i.set(g.record.name,g)}function _(g,w){let x,P={},L,te;if("name"in g&&g.name){if(x=i.get(g.name),!x)throw Br(1,{location:g});if({}.NODE_ENV!=="production"){const J=Object.keys(g.params||{}).filter(we=>!x.keys.find(X=>X.name===we));J.length&&$e(`Discarded invalid param(s) "${J.join('", "')}" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`)}te=x.record.name,P=Qe(Mh(w.params,x.keys.filter(J=>!J.optional).concat(x.parent?x.parent.keys.filter(J=>J.optional):[]).map(J=>J.name)),g.params&&Mh(g.params,x.keys.map(J=>J.name))),L=x.stringify(P)}else if(g.path!=null)L=g.path,{}.NODE_ENV!=="production"&&!L.startsWith("/")&&$e(`The Matcher cannot resolve relative paths but received "${L}". Unless you directly called \`matcher.resolve("${L}")\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`),x=s.find(J=>J.re.test(L)),x&&(P=x.parse(L),te=x.record.name);else{if(x=w.name?i.get(w.name):s.find(J=>J.re.test(w.path)),!x)throw Br(1,{location:g,currentLocation:w});te=x.record.name,P=Qe({},w.params,g.params),L=x.stringify(P)}const N=[];let re=x;for(;re;)N.unshift(re.record),re=re.parent;return{name:te,path:L,params:P,matched:N,meta:vb(N)}}e.forEach(g=>a(g));function p(){s.length=0,i.clear()}return{addRoute:a,resolve:_,removeRoute:u,clearRoutes:p,getRoutes:d,getRecordMatcher:r}}function Mh(e,t){const s={};for(const i of t)i in e&&(s[i]=e[i]);return s}function Ph(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:_b(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function _b(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const i in e.components)t[i]=typeof s=="object"?s[i]:s;return t}function kh(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function vb(e){return e.reduce((t,s)=>Qe(t,s.meta),{})}function Vh(e,t){const s={};for(const i in e)s[i]=i in t?t[i]:e[i];return s}function uu(e,t){return e.name===t.name&&e.optional===t.optional&&e.repeatable===t.repeatable}function bb(e,t){for(const s of e.keys)if(!s.optional&&!t.keys.find(uu.bind(null,s)))return $e(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`);for(const s of t.keys)if(!s.optional&&!e.keys.find(uu.bind(null,s)))return $e(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`)}function yb(e,t){t&&t.record.name&&!e.name&&!e.path&&$e(`The route named "${String(t.record.name)}" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`)}function wb(e,t){for(let s=t;s;s=s.parent)if(s.record.name===e.name)throw new Error(`A route named "${String(e.name)}" has been added as a ${t===s?"child":"descendant"} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`)}function Eb(e,t){for(const s of t.keys)if(!e.keys.find(uu.bind(null,s)))return $e(`Absolute path "${e.record.path}" must have the exact same param named "${s.name}" as its parent "${t.record.path}".`)}function Cb(e,t){let s=0,i=t.length;for(;s!==i;){const a=s+i>>1;Ih(e,t[a])<0?i=a:s=a+1}const r=xb(e);return r&&(i=t.lastIndexOf(r,i-1),{}.NODE_ENV!=="production"&&i<0&&$e(`Finding ancestor route "${r.record.path}" failed for "${e.record.path}"`)),i}function xb(e){let t=e;for(;t=t.parent;)if(Rh(t)&&Ih(e,t)===0)return t}function Rh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Db(e){const t={};if(e===""||e==="?")return t;const i=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<i.length;++r){const a=i[r].replace(gh," "),u=a.indexOf("="),d=Ur(u<0?a:a.slice(0,u)),h=u<0?null:Ur(a.slice(u+1));if(d in t){let _=t[d];us(_)||(_=t[d]=[_]),_.push(h)}else t[d]=h}return t}function Fh(e){let t="";for(let s in e){const i=e[s];if(s=B0(s),i==null){i!==void 0&&(t+=(t.length?"&":"")+s);continue}(us(i)?i.map(a=>a&&ou(a)):[i&&ou(i)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+s,a!=null&&(t+="="+a))})}return t}function Ob(e){const t={};for(const s in e){const i=e[s];i!==void 0&&(t[s]=us(i)?i.map(r=>r==null?null:""+r):i==null?i:""+i)}return t}const Sb=Symbol({}.NODE_ENV!=="production"?"router view location matched":""),Lh=Symbol({}.NODE_ENV!=="production"?"router view depth":""),Qi=Symbol({}.NODE_ENV!=="production"?"router":""),Uh=Symbol({}.NODE_ENV!=="production"?"route location":""),cu=Symbol({}.NODE_ENV!=="production"?"router view location":"");function Bo(){let e=[];function t(i){return e.push(i),()=>{const r=e.indexOf(i);r>-1&&e.splice(r,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function Bn(e,t,s,i,r,a=u=>u()){const u=i&&(i.enterCallbacks[r]=i.enterCallbacks[r]||[]);return()=>new Promise((d,h)=>{const _=w=>{w===!1?h(Br(4,{from:s,to:t})):w instanceof Error?h(w):Ji(w)?h(Br(2,{from:t,to:w})):(u&&i.enterCallbacks[r]===u&&typeof w=="function"&&u.push(w),d())},p=a(()=>e.call(i&&i.instances[r],t,s,{}.NODE_ENV!=="production"?Tb(_,t,s):_));let g=Promise.resolve(p);if(e.length<3&&(g=g.then(_)),{}.NODE_ENV!=="production"&&e.length>2){const w=`The "next" callback was never called inside of ${e.name?'"'+e.name+'"':""}:
${e.toString()}
. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;if(typeof p=="object"&&"then"in p)g=g.then(x=>_._called?x:($e(w),Promise.reject(new Error("Invalid navigation guard"))));else if(p!==void 0&&!_._called){$e(w),h(new Error("Invalid navigation guard"));return}}g.catch(w=>h(w))})}function Tb(e,t,s){let i=0;return function(){i++===1&&$e(`The "next" callback was called more than once in one navigation guard when going from "${s.fullPath}" to "${t.fullPath}". It should be called exactly one time in each navigation guard. This will fail in production.`),e._called=!0,i===1&&e.apply(null,arguments)}}function du(e,t,s,i,r=a=>a()){const a=[];for(const u of e){({}).NODE_ENV!=="production"&&!u.components&&!u.children.length&&$e(`Record with path "${u.path}" is either missing a "component(s)" or "children" property.`);for(const d in u.components){let h=u.components[d];if({}.NODE_ENV!=="production"){if(!h||typeof h!="object"&&typeof h!="function")throw $e(`Component "${d}" in record with path "${u.path}" is not a valid component. Received "${String(h)}".`),new Error("Invalid route component");if("then"in h){$e(`Component "${d}" in record with path "${u.path}" is a Promise instead of a function that returns a Promise. Did you write "import('./MyPage.vue')" instead of "() => import('./MyPage.vue')" ? This will break in production if not fixed.`);const _=h;h=()=>_}else h.__asyncLoader&&!h.__warnedDefineAsync&&(h.__warnedDefineAsync=!0,$e(`Component "${d}" in record with path "${u.path}" is defined using "defineAsyncComponent()". Write "() => import('./MyPage.vue')" instead of "defineAsyncComponent(() => import('./MyPage.vue'))".`))}if(!(t!=="beforeRouteEnter"&&!u.instances[d]))if(ph(h)){const p=(h.__vccOpts||h)[t];p&&a.push(Bn(p,s,i,u,d,r))}else{let _=h();({}).NODE_ENV!=="production"&&!("catch"in _)&&($e(`Component "${d}" in record with path "${u.path}" is a function that does not return a Promise. If you were passing a functional component, make sure to add a "displayName" to the component. This will break in production if not fixed.`),_=Promise.resolve(_)),a.push(()=>_.then(p=>{if(!p)throw new Error(`Couldn't resolve component "${d}" at "${u.path}"`);const g=N0(p)?p.default:p;u.mods[d]=p,u.components[d]=g;const x=(g.__vccOpts||g)[t];return x&&Bn(x,s,i,u,d,r)()}))}}}return a}function Bh(e){const t=Zs(Qi),s=Zs(Uh);let i=!1,r=null;const a=Rs(()=>{const p=In(e.to);return{}.NODE_ENV!=="production"&&(!i||p!==r)&&(Ji(p)||(i?$e(`Invalid value for prop "to" in useLink()
- to:`,p,`
- previous to:`,r,`
- props:`,e):$e(`Invalid value for prop "to" in useLink()
- to:`,p,`
- props:`,e)),r=p,i=!0),t.resolve(p)}),u=Rs(()=>{const{matched:p}=a.value,{length:g}=p,w=p[g-1],x=s.matched;if(!w||!x.length)return-1;const P=x.findIndex(Ln.bind(null,w));if(P>-1)return P;const L=$h(p[g-2]);return g>1&&$h(w)===L&&x[x.length-1].path!==L?x.findIndex(Ln.bind(null,p[g-2])):P}),d=Rs(()=>u.value>-1&&Mb(s.params,a.value.params)),h=Rs(()=>u.value>-1&&u.value===s.matched.length-1&&Eh(s.params,a.value.params));function _(p={}){if(Ab(p)){const g=t[In(e.replace)?"replace":"push"](In(e.to)).catch(Fo);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>g),g}return Promise.resolve()}if({}.NODE_ENV!=="production"&&mn){const p=Li();if(p){const g={route:a.value,isActive:d.value,isExactActive:h.value,error:null};p.__vrl_devtools=p.__vrl_devtools||[],p.__vrl_devtools.push(g),z_(()=>{g.route=a.value,g.isActive=d.value,g.isExactActive=h.value,g.error=Ji(In(e.to))?null:'Invalid "to" value'},{flush:"post"})}}return{route:a,href:Rs(()=>a.value.href),isActive:d,isExactActive:h,navigate:_}}function Nb(e){return e.length===1?e[0]:e}const Ib=Fd({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Bh,setup(e,{slots:t}){const s=mi(Bh(e)),{options:i}=Zs(Qi),r=Rs(()=>({[Hh(e.activeClass,i.linkActiveClass,"router-link-active")]:s.isActive,[Hh(e.exactActiveClass,i.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const a=t.default&&Nb(t.default(s));return e.custom?a:jl("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:r.value},a)}}});function Ab(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Mb(e,t){for(const s in t){const i=t[s],r=e[s];if(typeof i=="string"){if(i!==r)return!1}else if(!us(r)||r.length!==i.length||i.some((a,u)=>a!==r[u]))return!1}return!0}function $h(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Hh=(e,t,s)=>e??t??s,Pb=Fd({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){({}).NODE_ENV!=="production"&&Vb();const i=Zs(cu),r=Rs(()=>e.route||i.value),a=Zs(Lh,0),u=Rs(()=>{let _=In(a);const{matched:p}=r.value;let g;for(;(g=p[_])&&!g.components;)_++;return _}),d=Rs(()=>r.value.matched[u.value]);Pi(Lh,Rs(()=>u.value+1)),Pi(Sb,d),Pi(cu,r);const h=ad();return Vr(()=>[h.value,d.value,e.name],([_,p,g],[w,x,P])=>{p&&(p.instances[g]=_,x&&x!==p&&_&&_===w&&(p.leaveGuards.size||(p.leaveGuards=x.leaveGuards),p.updateGuards.size||(p.updateGuards=x.updateGuards))),_&&p&&(!x||!Ln(p,x)||!w)&&(p.enterCallbacks[g]||[]).forEach(L=>L(_))},{flush:"post"}),()=>{const _=r.value,p=e.name,g=d.value,w=g&&g.components[p];if(!w)return qh(s.default,{Component:w,route:_});const x=g.props[p],P=x?x===!0?_.params:typeof x=="function"?x(_):x:null,te=jl(w,Qe({},P,t,{onVnodeUnmounted:N=>{N.component.isUnmounted&&(g.instances[p]=null)},ref:h}));if({}.NODE_ENV!=="production"&&mn&&te.ref){const N={depth:u.value,name:g.name,path:g.path,meta:g.meta};(us(te.ref)?te.ref.map(J=>J.i):[te.ref.i]).forEach(J=>{J.__vrv_devtools=N})}return qh(s.default,{Component:te,route:_})||te}}});function qh(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const kb=Pb;function Vb(){const e=Li(),t=e.parent&&e.parent.type.name,s=e.parent&&e.parent.subTree&&e.parent.subTree.type;if(t&&(t==="KeepAlive"||t.includes("Transition"))&&typeof s=="object"&&s.name==="RouterView"){const i=t==="KeepAlive"?"keep-alive":"transition";$e(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.
Use slot props instead:

<router-view v-slot="{ Component }">
  <${i}>
    <component :is="Component" />
  </${i}>
</router-view>`)}}function $o(e,t){const s=Qe({},e,{matched:e.matched.map(i=>zb(i,["instances","children","aliasOf"]))});return{_custom:{type:null,readOnly:!0,display:e.fullPath,tooltip:t,value:s}}}function Xi(e){return{_custom:{display:e}}}let Rb=0;function Fb(e,t,s){if(t.__hasDevtools)return;t.__hasDevtools=!0;const i=Rb++;Xl({id:"org.vuejs.router"+(i?"."+i:""),label:"Vue Router",packageName:"vue-router",homepage:"https://router.vuejs.org",logo:"https://router.vuejs.org/logo.png",componentStateTypes:["Routing"],app:e},r=>{typeof r.now!="function"&&console.warn("[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),r.on.inspectComponent((p,g)=>{p.instanceData&&p.instanceData.state.push({type:"Routing",key:"$route",editable:!1,value:$o(t.currentRoute.value,"Current Route")})}),r.on.visitComponentTree(({treeNode:p,componentInstance:g})=>{if(g.__vrv_devtools){const w=g.__vrv_devtools;p.tags.push({label:(w.name?`${w.name.toString()}: `:"")+w.path,textColor:0,tooltip:"This component is rendered by &lt;router-view&gt;",backgroundColor:Wh})}us(g.__vrl_devtools)&&(g.__devtoolsApi=r,g.__vrl_devtools.forEach(w=>{let x=w.route.path,P=Gh,L="",te=0;w.error?(x=w.error,P=Hb,te=qb):w.isExactActive?(P=zh,L="This is exactly active"):w.isActive&&(P=jh,L="This link is active"),p.tags.push({label:x,textColor:te,tooltip:L,backgroundColor:P})}))}),Vr(t.currentRoute,()=>{h(),r.notifyComponentUpdate(),r.sendInspectorTree(d),r.sendInspectorState(d)});const a="router:navigations:"+i;r.addTimelineLayer({id:a,label:`Router${i?" "+i:""} Navigations`,color:4237508}),t.onError((p,g)=>{r.addTimelineEvent({layerId:a,event:{title:"Error during Navigation",subtitle:g.fullPath,logType:"error",time:r.now(),data:{error:p},groupId:g.meta.__navigationId}})});let u=0;t.beforeEach((p,g)=>{const w={guard:Xi("beforeEach"),from:$o(g,"Current Location during this navigation"),to:$o(p,"Target location")};Object.defineProperty(p.meta,"__navigationId",{value:u++}),r.addTimelineEvent({layerId:a,event:{time:r.now(),title:"Start of navigation",subtitle:p.fullPath,data:w,groupId:p.meta.__navigationId}})}),t.afterEach((p,g,w)=>{const x={guard:Xi("afterEach")};w?(x.failure={_custom:{type:Error,readOnly:!0,display:w?w.message:"",tooltip:"Navigation Failure",value:w}},x.status=Xi("❌")):x.status=Xi("✅"),x.from=$o(g,"Current Location during this navigation"),x.to=$o(p,"Target location"),r.addTimelineEvent({layerId:a,event:{title:"End of navigation",subtitle:p.fullPath,time:r.now(),data:x,logType:w?"warning":"default",groupId:p.meta.__navigationId}})});const d="router-inspector:"+i;r.addInspector({id:d,label:"Routes"+(i?" "+i:""),icon:"book",treeFilterPlaceholder:"Search routes"});function h(){if(!_)return;const p=_;let g=s.getRoutes().filter(w=>!w.parent||!w.parent.record.components);g.forEach(Yh),p.filter&&(g=g.filter(w=>fu(w,p.filter.toLowerCase()))),g.forEach(w=>Zh(w,t.currentRoute.value)),p.rootNodes=g.map(Kh)}let _;r.on.getInspectorTree(p=>{_=p,p.app===e&&p.inspectorId===d&&h()}),r.on.getInspectorState(p=>{if(p.app===e&&p.inspectorId===d){const w=s.getRoutes().find(x=>x.record.__vd_id===p.nodeId);w&&(p.state={options:Ub(w)})}}),r.sendInspectorTree(d),r.sendInspectorState(d)})}function Lb(e){return e.optional?e.repeatable?"*":"?":e.repeatable?"+":""}function Ub(e){const{record:t}=e,s=[{editable:!1,key:"path",value:t.path}];return t.name!=null&&s.push({editable:!1,key:"name",value:t.name}),s.push({editable:!1,key:"regexp",value:e.re}),e.keys.length&&s.push({editable:!1,key:"keys",value:{_custom:{type:null,readOnly:!0,display:e.keys.map(i=>`${i.name}${Lb(i)}`).join(" "),tooltip:"Param keys",value:e.keys}}}),t.redirect!=null&&s.push({editable:!1,key:"redirect",value:t.redirect}),e.alias.length&&s.push({editable:!1,key:"aliases",value:e.alias.map(i=>i.record.path)}),Object.keys(e.record.meta).length&&s.push({editable:!1,key:"meta",value:e.record.meta}),s.push({key:"score",editable:!1,value:{_custom:{type:null,readOnly:!0,display:e.score.map(i=>i.join(", ")).join(" | "),tooltip:"Score used to sort routes",value:e.score}}}),s}const Wh=15485081,jh=2450411,zh=8702998,Bb=2282478,Gh=16486972,$b=6710886,Hb=16704226,qb=12131356;function Kh(e){const t=[],{record:s}=e;s.name!=null&&t.push({label:String(s.name),textColor:0,backgroundColor:Bb}),s.aliasOf&&t.push({label:"alias",textColor:0,backgroundColor:Gh}),e.__vd_match&&t.push({label:"matches",textColor:0,backgroundColor:Wh}),e.__vd_exactActive&&t.push({label:"exact",textColor:0,backgroundColor:zh}),e.__vd_active&&t.push({label:"active",textColor:0,backgroundColor:jh}),s.redirect&&t.push({label:typeof s.redirect=="string"?`redirect: ${s.redirect}`:"redirects",textColor:16777215,backgroundColor:$b});let i=s.__vd_id;return i==null&&(i=String(Wb++),s.__vd_id=i),{id:i,label:s.path,tags:t,children:e.children.map(Kh)}}let Wb=0;const jb=/^\/(.*)\/([a-z]*)$/;function Zh(e,t){const s=t.matched.length&&Ln(t.matched[t.matched.length-1],e.record);e.__vd_exactActive=e.__vd_active=s,s||(e.__vd_active=t.matched.some(i=>Ln(i,e.record))),e.children.forEach(i=>Zh(i,t))}function Yh(e){e.__vd_match=!1,e.children.forEach(Yh)}function fu(e,t){const s=String(e.re).match(jb);if(e.__vd_match=!1,!s||s.length<3)return!1;if(new RegExp(s[1].replace(/\$$/,""),s[2]).test(t))return e.children.forEach(u=>fu(u,t)),e.record.path!=="/"||t==="/"?(e.__vd_match=e.re.test(t),!0):!1;const r=e.record.path.toLowerCase(),a=Ur(r);return!t.startsWith("/")&&(a.includes(t)||r.includes(t))||a.startsWith(t)||r.startsWith(t)||e.record.name&&String(e.record.name).includes(t)?!0:e.children.some(u=>fu(u,t))}function zb(e,t){const s={};for(const i in e)t.includes(i)||(s[i]=e[i]);return s}function Gb(e){const t=gb(e.routes,e),s=e.parseQuery||Db,i=e.stringifyQuery||Fh,r=e.history;if({}.NODE_ENV!=="production"&&!r)throw new Error('Provide the "history" option when calling "createRouter()": https://router.vuejs.org/api/interfaces/RouterOptions.html#history');const a=Bo(),u=Bo(),d=Bo(),h=C1(Un);let _=Un;mn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const p=nu.bind(null,R=>""+R),g=nu.bind(null,H0),w=nu.bind(null,Ur);function x(R,ie){let oe,ge;return Sh(R)?(oe=t.getRecordMatcher(R),{}.NODE_ENV!=="production"&&!oe&&$e(`Parent route "${String(R)}" not found when adding child route`,ie),ge=ie):ge=R,t.addRoute(ge,oe)}function P(R){const ie=t.getRecordMatcher(R);ie?t.removeRoute(ie):{}.NODE_ENV!=="production"&&$e(`Cannot remove non-existent route "${String(R)}"`)}function L(){return t.getRoutes().map(R=>R.record)}function te(R){return!!t.getRecordMatcher(R)}function N(R,ie){if(ie=Qe({},ie||h.value),typeof R=="string"){const y=iu(s,R,ie.path),C=t.resolve({path:y.path},ie),V=r.createHref(y.fullPath);return{}.NODE_ENV!=="production"&&(V.startsWith("//")?$e(`Location "${R}" resolved to "${V}". A resolved location cannot start with multiple slashes.`):C.matched.length||$e(`No match found for location with path "${R}"`)),Qe(y,C,{params:w(C.params),hash:Ur(y.hash),redirectedFrom:void 0,href:V})}if({}.NODE_ENV!=="production"&&!Ji(R))return $e(`router.resolve() was passed an invalid location. This will fail in production.
- Location:`,R),N({});let oe;if(R.path!=null)({}).NODE_ENV!=="production"&&"params"in R&&!("name"in R)&&Object.keys(R.params).length&&$e(`Path "${R.path}" was passed with params but they will be ignored. Use a named route alongside params instead.`),oe=Qe({},R,{path:iu(s,R.path,ie.path).path});else{const y=Qe({},R.params);for(const C in y)y[C]==null&&delete y[C];oe=Qe({},R,{params:g(y)}),ie.params=g(ie.params)}const ge=t.resolve(oe,ie),ke=R.hash||"";({}).NODE_ENV!=="production"&&ke&&!ke.startsWith("#")&&$e(`A \`hash\` should always start with the character "#". Replace "${ke}" with "#${ke}".`),ge.params=p(w(ge.params));const rt=j0(i,Qe({},R,{hash:U0(ke),path:ge.path})),Ve=r.createHref(rt);return{}.NODE_ENV!=="production"&&(Ve.startsWith("//")?$e(`Location "${R}" resolved to "${Ve}". A resolved location cannot start with multiple slashes.`):ge.matched.length||$e(`No match found for location with path "${R.path!=null?R.path:R}"`)),Qe({fullPath:rt,hash:ke,query:i===Fh?Ob(R.query):R.query||{}},ge,{redirectedFrom:void 0,href:Ve})}function re(R){return typeof R=="string"?iu(s,R,h.value.path):Qe({},R)}function J(R,ie){if(_!==R)return Br(8,{from:ie,to:R})}function we(R){return be(R)}function X(R){return we(Qe(re(R),{replace:!0}))}function pe(R){const ie=R.matched[R.matched.length-1];if(ie&&ie.redirect){const{redirect:oe}=ie;let ge=typeof oe=="function"?oe(R):oe;if(typeof ge=="string"&&(ge=ge.includes("?")||ge.includes("#")?ge=re(ge):{path:ge},ge.params={}),{}.NODE_ENV!=="production"&&ge.path==null&&!("name"in ge))throw $e(`Invalid redirect found:
${JSON.stringify(ge,null,2)}
 when navigating to "${R.fullPath}". A redirect must contain a name or path. This will break in production.`),new Error("Invalid redirect");return Qe({query:R.query,hash:R.hash,params:ge.path!=null?{}:R.params},ge)}}function be(R,ie){const oe=_=N(R),ge=h.value,ke=R.state,rt=R.force,Ve=R.replace===!0,y=pe(oe);if(y)return be(Qe(re(y),{state:typeof y=="object"?Qe({},ke,y.state):ke,force:rt,replace:Ve}),ie||oe);const C=oe;C.redirectedFrom=ie;let V;return!rt&&wh(i,ge,oe)&&(V=Br(16,{to:C,from:ge}),Nt(ge,ge,!0,!1)),(V?Promise.resolve(V):A(C,ge)).catch(U=>gn(U)?gn(U,2)?U:vs(U):Ce(U,C,ge)).then(U=>{if(U){if(gn(U,2))return{}.NODE_ENV!=="production"&&wh(i,N(U.to),C)&&ie&&(ie._count=ie._count?ie._count+1:1)>30?($e(`Detected a possibly infinite redirection in a navigation guard when going from "${ge.fullPath}" to "${C.fullPath}". Aborting to avoid a Stack Overflow.
 Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`),Promise.reject(new Error("Infinite redirect in navigation guard"))):be(Qe({replace:Ve},re(U.to),{state:typeof U.to=="object"?Qe({},ke,U.to.state):ke,force:rt}),ie||C)}else U=ue(C,ge,!0,Ve,ke);return Ee(C,ge,U),U})}function Ae(R,ie){const oe=J(R,ie);return oe?Promise.reject(oe):Promise.resolve()}function ae(R){const ie=en.values().next().value;return ie&&typeof ie.runWithContext=="function"?ie.runWithContext(R):R()}function A(R,ie){let oe;const[ge,ke,rt]=Kb(R,ie);oe=du(ge.reverse(),"beforeRouteLeave",R,ie);for(const y of ge)y.leaveGuards.forEach(C=>{oe.push(Bn(C,R,ie))});const Ve=Ae.bind(null,R,ie);return oe.push(Ve),cs(oe).then(()=>{oe=[];for(const y of a.list())oe.push(Bn(y,R,ie));return oe.push(Ve),cs(oe)}).then(()=>{oe=du(ke,"beforeRouteUpdate",R,ie);for(const y of ke)y.updateGuards.forEach(C=>{oe.push(Bn(C,R,ie))});return oe.push(Ve),cs(oe)}).then(()=>{oe=[];for(const y of rt)if(y.beforeEnter)if(us(y.beforeEnter))for(const C of y.beforeEnter)oe.push(Bn(C,R,ie));else oe.push(Bn(y.beforeEnter,R,ie));return oe.push(Ve),cs(oe)}).then(()=>(R.matched.forEach(y=>y.enterCallbacks={}),oe=du(rt,"beforeRouteEnter",R,ie,ae),oe.push(Ve),cs(oe))).then(()=>{oe=[];for(const y of u.list())oe.push(Bn(y,R,ie));return oe.push(Ve),cs(oe)}).catch(y=>gn(y,8)?y:Promise.reject(y))}function Ee(R,ie,oe){d.list().forEach(ge=>ae(()=>ge(R,ie,oe)))}function ue(R,ie,oe,ge,ke){const rt=J(R,ie);if(rt)return rt;const Ve=ie===Un,y=mn?history.state:{};oe&&(ge||Ve?r.replace(R.fullPath,Qe({scroll:Ve&&y&&y.scroll},ke)):r.push(R.fullPath,ke)),h.value=R,Nt(R,ie,oe,Ve),vs()}let Ge;function mt(){Ge||(Ge=r.listen((R,ie,oe)=>{if(!Ls.listening)return;const ge=N(R),ke=pe(ge);if(ke){be(Qe(ke,{replace:!0,force:!0}),ge).catch(Fo);return}_=ge;const rt=h.value;mn&&X0(xh(rt.fullPath,oe.delta),Yi()),A(ge,rt).catch(Ve=>gn(Ve,12)?Ve:gn(Ve,2)?(be(Qe(re(Ve.to),{force:!0}),ge).then(y=>{gn(y,20)&&!oe.delta&&oe.type===Lo.pop&&r.go(-1,!1)}).catch(Fo),Promise.reject()):(oe.delta&&r.go(-oe.delta,!1),Ce(Ve,ge,rt))).then(Ve=>{Ve=Ve||ue(ge,rt,!1),Ve&&(oe.delta&&!gn(Ve,8)?r.go(-oe.delta,!1):oe.type===Lo.pop&&gn(Ve,20)&&r.go(-1,!1)),Ee(ge,rt,Ve)}).catch(Fo)}))}let ce=Bo(),it=Bo(),de;function Ce(R,ie,oe){vs(R);const ge=it.list();return ge.length?ge.forEach(ke=>ke(R,ie,oe)):({}.NODE_ENV!=="production"&&$e("uncaught error during route navigation:"),console.error(R)),Promise.reject(R)}function xt(){return de&&h.value!==Un?Promise.resolve():new Promise((R,ie)=>{ce.add([R,ie])})}function vs(R){return de||(de=!R,mt(),ce.list().forEach(([ie,oe])=>R?oe(R):ie()),ce.reset()),R}function Nt(R,ie,oe,ge){const{scrollBehavior:ke}=e;if(!mn||!ke)return Promise.resolve();const rt=!oe&&eb(xh(R.fullPath,0))||(ge||!oe)&&history.state&&history.state.scroll||null;return gl().then(()=>ke(R,ie,rt)).then(Ve=>Ve&&Q0(Ve)).catch(Ve=>Ce(Ve,R,ie))}const bs=R=>r.go(R);let ss;const en=new Set,Ls={currentRoute:h,listening:!0,addRoute:x,removeRoute:P,clearRoutes:t.clearRoutes,hasRoute:te,getRoutes:L,resolve:N,options:e,push:we,replace:X,go:bs,back:()=>bs(-1),forward:()=>bs(1),beforeEach:a.add,beforeResolve:u.add,afterEach:d.add,onError:it.add,isReady:xt,install(R){const ie=this;R.component("RouterLink",Ib),R.component("RouterView",kb),R.config.globalProperties.$router=ie,Object.defineProperty(R.config.globalProperties,"$route",{enumerable:!0,get:()=>In(h)}),mn&&!ss&&h.value===Un&&(ss=!0,we(r.location).catch(ke=>{({}).NODE_ENV!=="production"&&$e("Unexpected error when starting the router:",ke)}));const oe={};for(const ke in Un)Object.defineProperty(oe,ke,{get:()=>h.value[ke],enumerable:!0});R.provide(Qi,ie),R.provide(Uh,od(oe)),R.provide(cu,h);const ge=R.unmount;en.add(R),R.unmount=function(){en.delete(R),en.size<1&&(_=Un,Ge&&Ge(),Ge=null,h.value=Un,ss=!1,de=!1),ge()},{}.NODE_ENV!=="production"&&mn&&Fb(R,ie,t)}};function cs(R){return R.reduce((ie,oe)=>ie.then(()=>ae(oe)),Promise.resolve())}return Ls}function Kb(e,t){const s=[],i=[],r=[],a=Math.max(t.matched.length,e.matched.length);for(let u=0;u<a;u++){const d=t.matched[u];d&&(e.matched.find(_=>Ln(_,d))?i.push(d):s.push(d));const h=e.matched[u];h&&(t.matched.find(_=>Ln(_,h))||r.push(h))}return[s,i,r]}function Jh(){return Zs(Qi)}var Ho=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},ea={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */ea.exports,function(e,t){(function(){var s,i="4.17.21",r=200,a="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",u="Expected a function",d="Invalid `variable` option passed into `_.template`",h="__lodash_hash_undefined__",_=500,p="__lodash_placeholder__",g=1,w=2,x=4,P=1,L=2,te=1,N=2,re=4,J=8,we=16,X=32,pe=64,be=128,Ae=256,ae=512,A=30,Ee="...",ue=800,Ge=16,mt=1,ce=2,it=3,de=1/0,Ce=9007199254740991,xt=17976931348623157e292,vs=0/0,Nt=**********,bs=Nt-1,ss=Nt>>>1,en=[["ary",be],["bind",te],["bindKey",N],["curry",J],["curryRight",we],["flip",ae],["partial",X],["partialRight",pe],["rearg",Ae]],Ls="[object Arguments]",cs="[object Array]",R="[object AsyncFunction]",ie="[object Boolean]",oe="[object Date]",ge="[object DOMException]",ke="[object Error]",rt="[object Function]",Ve="[object GeneratorFunction]",y="[object Map]",C="[object Number]",V="[object Null]",U="[object Object]",q="[object Promise]",j="[object Proxy]",se="[object RegExp]",Z="[object Set]",ee="[object String]",K="[object Symbol]",ye="[object Undefined]",ne="[object WeakMap]",_e="[object WeakSet]",xe="[object ArrayBuffer]",Le="[object DataView]",Xe="[object Float32Array]",Ye="[object Float64Array]",Vt="[object Int8Array]",Dt="[object Int16Array]",Jt="[object Int32Array]",Ut="[object Uint8Array]",vn="[object Uint8ClampedArray]",Wr="[object Uint16Array]",It="[object Uint32Array]",ys=/\b__p \+= '';/g,aa=/\b(__p \+=) '' \+/g,N5=/(__e\(.*?\)|\b__t\)) \+\n'';/g,op=/&(?:amp|lt|gt|quot|#39);/g,ip=/[&<>"']/g,I5=RegExp(op.source),A5=RegExp(ip.source),M5=/<%-([\s\S]+?)%>/g,P5=/<%([\s\S]+?)%>/g,ap=/<%=([\s\S]+?)%>/g,k5=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,V5=/^\w*$/,R5=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,yu=/[\\^$.*+?()[\]{}|]/g,F5=RegExp(yu.source),wu=/^\s+/,L5=/\s/,U5=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,B5=/\{\n\/\* \[wrapped with (.+)\] \*/,$5=/,? & /,H5=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,q5=/[()=,{}\[\]\/\s]/,W5=/\\(\\)?/g,j5=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,lp=/\w*$/,z5=/^[-+]0x[0-9a-f]+$/i,G5=/^0b[01]+$/i,K5=/^\[object .+?Constructor\]$/,Z5=/^0o[0-7]+$/i,Y5=/^(?:0|[1-9]\d*)$/,J5=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,la=/($^)/,Q5=/['\n\r\u2028\u2029\\]/g,ua="\\ud800-\\udfff",X5="\\u0300-\\u036f",e3="\\ufe20-\\ufe2f",t3="\\u20d0-\\u20ff",up=X5+e3+t3,cp="\\u2700-\\u27bf",dp="a-z\\xdf-\\xf6\\xf8-\\xff",s3="\\xac\\xb1\\xd7\\xf7",n3="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",r3="\\u2000-\\u206f",o3=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",fp="A-Z\\xc0-\\xd6\\xd8-\\xde",hp="\\ufe0e\\ufe0f",pp=s3+n3+r3+o3,Eu="['’]",i3="["+ua+"]",mp="["+pp+"]",ca="["+up+"]",gp="\\d+",a3="["+cp+"]",_p="["+dp+"]",vp="[^"+ua+pp+gp+cp+dp+fp+"]",Cu="\\ud83c[\\udffb-\\udfff]",l3="(?:"+ca+"|"+Cu+")",bp="[^"+ua+"]",xu="(?:\\ud83c[\\udde6-\\uddff]){2}",Du="[\\ud800-\\udbff][\\udc00-\\udfff]",jr="["+fp+"]",yp="\\u200d",wp="(?:"+_p+"|"+vp+")",u3="(?:"+jr+"|"+vp+")",Ep="(?:"+Eu+"(?:d|ll|m|re|s|t|ve))?",Cp="(?:"+Eu+"(?:D|LL|M|RE|S|T|VE))?",xp=l3+"?",Dp="["+hp+"]?",c3="(?:"+yp+"(?:"+[bp,xu,Du].join("|")+")"+Dp+xp+")*",d3="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",f3="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Op=Dp+xp+c3,h3="(?:"+[a3,xu,Du].join("|")+")"+Op,p3="(?:"+[bp+ca+"?",ca,xu,Du,i3].join("|")+")",m3=RegExp(Eu,"g"),g3=RegExp(ca,"g"),Ou=RegExp(Cu+"(?="+Cu+")|"+p3+Op,"g"),_3=RegExp([jr+"?"+_p+"+"+Ep+"(?="+[mp,jr,"$"].join("|")+")",u3+"+"+Cp+"(?="+[mp,jr+wp,"$"].join("|")+")",jr+"?"+wp+"+"+Ep,jr+"+"+Cp,f3,d3,gp,h3].join("|"),"g"),v3=RegExp("["+yp+ua+up+hp+"]"),b3=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,y3=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],w3=-1,ft={};ft[Xe]=ft[Ye]=ft[Vt]=ft[Dt]=ft[Jt]=ft[Ut]=ft[vn]=ft[Wr]=ft[It]=!0,ft[Ls]=ft[cs]=ft[xe]=ft[ie]=ft[Le]=ft[oe]=ft[ke]=ft[rt]=ft[y]=ft[C]=ft[U]=ft[se]=ft[Z]=ft[ee]=ft[ne]=!1;var ct={};ct[Ls]=ct[cs]=ct[xe]=ct[Le]=ct[ie]=ct[oe]=ct[Xe]=ct[Ye]=ct[Vt]=ct[Dt]=ct[Jt]=ct[y]=ct[C]=ct[U]=ct[se]=ct[Z]=ct[ee]=ct[K]=ct[Ut]=ct[vn]=ct[Wr]=ct[It]=!0,ct[ke]=ct[rt]=ct[ne]=!1;var E3={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},C3={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},x3={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},D3={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},O3=parseFloat,S3=parseInt,Sp=typeof Ho=="object"&&Ho&&Ho.Object===Object&&Ho,T3=typeof self=="object"&&self&&self.Object===Object&&self,Bt=Sp||T3||Function("return this")(),Su=t&&!t.nodeType&&t,_r=Su&&!0&&e&&!e.nodeType&&e,Tp=_r&&_r.exports===Su,Tu=Tp&&Sp.process,ws=function(){try{var T=_r&&_r.require&&_r.require("util").types;return T||Tu&&Tu.binding&&Tu.binding("util")}catch{}}(),Np=ws&&ws.isArrayBuffer,Ip=ws&&ws.isDate,Ap=ws&&ws.isMap,Mp=ws&&ws.isRegExp,Pp=ws&&ws.isSet,kp=ws&&ws.isTypedArray;function ds(T,F,k){switch(k.length){case 0:return T.call(F);case 1:return T.call(F,k[0]);case 2:return T.call(F,k[0],k[1]);case 3:return T.call(F,k[0],k[1],k[2])}return T.apply(F,k)}function N3(T,F,k,he){for(var Pe=-1,et=T==null?0:T.length;++Pe<et;){var At=T[Pe];F(he,At,k(At),T)}return he}function Es(T,F){for(var k=-1,he=T==null?0:T.length;++k<he&&F(T[k],k,T)!==!1;);return T}function I3(T,F){for(var k=T==null?0:T.length;k--&&F(T[k],k,T)!==!1;);return T}function Vp(T,F){for(var k=-1,he=T==null?0:T.length;++k<he;)if(!F(T[k],k,T))return!1;return!0}function Hn(T,F){for(var k=-1,he=T==null?0:T.length,Pe=0,et=[];++k<he;){var At=T[k];F(At,k,T)&&(et[Pe++]=At)}return et}function da(T,F){var k=T==null?0:T.length;return!!k&&zr(T,F,0)>-1}function Nu(T,F,k){for(var he=-1,Pe=T==null?0:T.length;++he<Pe;)if(k(F,T[he]))return!0;return!1}function pt(T,F){for(var k=-1,he=T==null?0:T.length,Pe=Array(he);++k<he;)Pe[k]=F(T[k],k,T);return Pe}function qn(T,F){for(var k=-1,he=F.length,Pe=T.length;++k<he;)T[Pe+k]=F[k];return T}function Iu(T,F,k,he){var Pe=-1,et=T==null?0:T.length;for(he&&et&&(k=T[++Pe]);++Pe<et;)k=F(k,T[Pe],Pe,T);return k}function A3(T,F,k,he){var Pe=T==null?0:T.length;for(he&&Pe&&(k=T[--Pe]);Pe--;)k=F(k,T[Pe],Pe,T);return k}function Au(T,F){for(var k=-1,he=T==null?0:T.length;++k<he;)if(F(T[k],k,T))return!0;return!1}var M3=Mu("length");function P3(T){return T.split("")}function k3(T){return T.match(H5)||[]}function Rp(T,F,k){var he;return k(T,function(Pe,et,At){if(F(Pe,et,At))return he=et,!1}),he}function fa(T,F,k,he){for(var Pe=T.length,et=k+(he?1:-1);he?et--:++et<Pe;)if(F(T[et],et,T))return et;return-1}function zr(T,F,k){return F===F?z3(T,F,k):fa(T,Fp,k)}function V3(T,F,k,he){for(var Pe=k-1,et=T.length;++Pe<et;)if(he(T[Pe],F))return Pe;return-1}function Fp(T){return T!==T}function Lp(T,F){var k=T==null?0:T.length;return k?ku(T,F)/k:vs}function Mu(T){return function(F){return F==null?s:F[T]}}function Pu(T){return function(F){return T==null?s:T[F]}}function Up(T,F,k,he,Pe){return Pe(T,function(et,At,ut){k=he?(he=!1,et):F(k,et,At,ut)}),k}function R3(T,F){var k=T.length;for(T.sort(F);k--;)T[k]=T[k].value;return T}function ku(T,F){for(var k,he=-1,Pe=T.length;++he<Pe;){var et=F(T[he]);et!==s&&(k=k===s?et:k+et)}return k}function Vu(T,F){for(var k=-1,he=Array(T);++k<T;)he[k]=F(k);return he}function F3(T,F){return pt(F,function(k){return[k,T[k]]})}function Bp(T){return T&&T.slice(0,Wp(T)+1).replace(wu,"")}function fs(T){return function(F){return T(F)}}function Ru(T,F){return pt(F,function(k){return T[k]})}function Go(T,F){return T.has(F)}function $p(T,F){for(var k=-1,he=T.length;++k<he&&zr(F,T[k],0)>-1;);return k}function Hp(T,F){for(var k=T.length;k--&&zr(F,T[k],0)>-1;);return k}function L3(T,F){for(var k=T.length,he=0;k--;)T[k]===F&&++he;return he}var U3=Pu(E3),B3=Pu(C3);function $3(T){return"\\"+D3[T]}function H3(T,F){return T==null?s:T[F]}function Gr(T){return v3.test(T)}function q3(T){return b3.test(T)}function W3(T){for(var F,k=[];!(F=T.next()).done;)k.push(F.value);return k}function Fu(T){var F=-1,k=Array(T.size);return T.forEach(function(he,Pe){k[++F]=[Pe,he]}),k}function qp(T,F){return function(k){return T(F(k))}}function Wn(T,F){for(var k=-1,he=T.length,Pe=0,et=[];++k<he;){var At=T[k];(At===F||At===p)&&(T[k]=p,et[Pe++]=k)}return et}function ha(T){var F=-1,k=Array(T.size);return T.forEach(function(he){k[++F]=he}),k}function j3(T){var F=-1,k=Array(T.size);return T.forEach(function(he){k[++F]=[he,he]}),k}function z3(T,F,k){for(var he=k-1,Pe=T.length;++he<Pe;)if(T[he]===F)return he;return-1}function G3(T,F,k){for(var he=k+1;he--;)if(T[he]===F)return he;return he}function Kr(T){return Gr(T)?Z3(T):M3(T)}function Us(T){return Gr(T)?Y3(T):P3(T)}function Wp(T){for(var F=T.length;F--&&L5.test(T.charAt(F)););return F}var K3=Pu(x3);function Z3(T){for(var F=Ou.lastIndex=0;Ou.test(T);)++F;return F}function Y3(T){return T.match(Ou)||[]}function J3(T){return T.match(_3)||[]}var Q3=function T(F){F=F==null?Bt:Zr.defaults(Bt.Object(),F,Zr.pick(Bt,y3));var k=F.Array,he=F.Date,Pe=F.Error,et=F.Function,At=F.Math,ut=F.Object,Lu=F.RegExp,X3=F.String,Cs=F.TypeError,pa=k.prototype,eN=et.prototype,Yr=ut.prototype,ma=F["__core-js_shared__"],ga=eN.toString,ot=Yr.hasOwnProperty,tN=0,jp=function(){var n=/[^.]+$/.exec(ma&&ma.keys&&ma.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}(),_a=Yr.toString,sN=ga.call(ut),nN=Bt._,rN=Lu("^"+ga.call(ot).replace(yu,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),va=Tp?F.Buffer:s,jn=F.Symbol,ba=F.Uint8Array,zp=va?va.allocUnsafe:s,ya=qp(ut.getPrototypeOf,ut),Gp=ut.create,Kp=Yr.propertyIsEnumerable,wa=pa.splice,Zp=jn?jn.isConcatSpreadable:s,Ko=jn?jn.iterator:s,vr=jn?jn.toStringTag:s,Ea=function(){try{var n=Cr(ut,"defineProperty");return n({},"",{}),n}catch{}}(),oN=F.clearTimeout!==Bt.clearTimeout&&F.clearTimeout,iN=he&&he.now!==Bt.Date.now&&he.now,aN=F.setTimeout!==Bt.setTimeout&&F.setTimeout,Ca=At.ceil,xa=At.floor,Uu=ut.getOwnPropertySymbols,lN=va?va.isBuffer:s,Yp=F.isFinite,uN=pa.join,cN=qp(ut.keys,ut),Mt=At.max,qt=At.min,dN=he.now,fN=F.parseInt,Jp=At.random,hN=pa.reverse,Bu=Cr(F,"DataView"),Zo=Cr(F,"Map"),$u=Cr(F,"Promise"),Jr=Cr(F,"Set"),Yo=Cr(F,"WeakMap"),Jo=Cr(ut,"create"),Da=Yo&&new Yo,Qr={},pN=xr(Bu),mN=xr(Zo),gN=xr($u),_N=xr(Jr),vN=xr(Yo),Oa=jn?jn.prototype:s,Qo=Oa?Oa.valueOf:s,Qp=Oa?Oa.toString:s;function v(n){if(vt(n)&&!Re(n)&&!(n instanceof ze)){if(n instanceof xs)return n;if(ot.call(n,"__wrapped__"))return Xm(n)}return new xs(n)}var Xr=function(){function n(){}return function(o){if(!gt(o))return{};if(Gp)return Gp(o);n.prototype=o;var l=new n;return n.prototype=s,l}}();function Sa(){}function xs(n,o){this.__wrapped__=n,this.__actions__=[],this.__chain__=!!o,this.__index__=0,this.__values__=s}v.templateSettings={escape:M5,evaluate:P5,interpolate:ap,variable:"",imports:{_:v}},v.prototype=Sa.prototype,v.prototype.constructor=v,xs.prototype=Xr(Sa.prototype),xs.prototype.constructor=xs;function ze(n){this.__wrapped__=n,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=Nt,this.__views__=[]}function bN(){var n=new ze(this.__wrapped__);return n.__actions__=ns(this.__actions__),n.__dir__=this.__dir__,n.__filtered__=this.__filtered__,n.__iteratees__=ns(this.__iteratees__),n.__takeCount__=this.__takeCount__,n.__views__=ns(this.__views__),n}function yN(){if(this.__filtered__){var n=new ze(this);n.__dir__=-1,n.__filtered__=!0}else n=this.clone(),n.__dir__*=-1;return n}function wN(){var n=this.__wrapped__.value(),o=this.__dir__,l=Re(n),f=o<0,m=l?n.length:0,b=PI(0,m,this.__views__),E=b.start,O=b.end,I=O-E,B=f?O:E-1,H=this.__iteratees__,z=H.length,le=0,ve=qt(I,this.__takeCount__);if(!l||!f&&m==I&&ve==I)return Em(n,this.__actions__);var Te=[];e:for(;I--&&le<ve;){B+=o;for(var Be=-1,Ne=n[B];++Be<z;){var We=H[Be],Ke=We.iteratee,ms=We.type,es=Ke(Ne);if(ms==ce)Ne=es;else if(!es){if(ms==mt)continue e;break e}}Te[le++]=Ne}return Te}ze.prototype=Xr(Sa.prototype),ze.prototype.constructor=ze;function br(n){var o=-1,l=n==null?0:n.length;for(this.clear();++o<l;){var f=n[o];this.set(f[0],f[1])}}function EN(){this.__data__=Jo?Jo(null):{},this.size=0}function CN(n){var o=this.has(n)&&delete this.__data__[n];return this.size-=o?1:0,o}function xN(n){var o=this.__data__;if(Jo){var l=o[n];return l===h?s:l}return ot.call(o,n)?o[n]:s}function DN(n){var o=this.__data__;return Jo?o[n]!==s:ot.call(o,n)}function ON(n,o){var l=this.__data__;return this.size+=this.has(n)?0:1,l[n]=Jo&&o===s?h:o,this}br.prototype.clear=EN,br.prototype.delete=CN,br.prototype.get=xN,br.prototype.has=DN,br.prototype.set=ON;function bn(n){var o=-1,l=n==null?0:n.length;for(this.clear();++o<l;){var f=n[o];this.set(f[0],f[1])}}function SN(){this.__data__=[],this.size=0}function TN(n){var o=this.__data__,l=Ta(o,n);if(l<0)return!1;var f=o.length-1;return l==f?o.pop():wa.call(o,l,1),--this.size,!0}function NN(n){var o=this.__data__,l=Ta(o,n);return l<0?s:o[l][1]}function IN(n){return Ta(this.__data__,n)>-1}function AN(n,o){var l=this.__data__,f=Ta(l,n);return f<0?(++this.size,l.push([n,o])):l[f][1]=o,this}bn.prototype.clear=SN,bn.prototype.delete=TN,bn.prototype.get=NN,bn.prototype.has=IN,bn.prototype.set=AN;function yn(n){var o=-1,l=n==null?0:n.length;for(this.clear();++o<l;){var f=n[o];this.set(f[0],f[1])}}function MN(){this.size=0,this.__data__={hash:new br,map:new(Zo||bn),string:new br}}function PN(n){var o=Ba(this,n).delete(n);return this.size-=o?1:0,o}function kN(n){return Ba(this,n).get(n)}function VN(n){return Ba(this,n).has(n)}function RN(n,o){var l=Ba(this,n),f=l.size;return l.set(n,o),this.size+=l.size==f?0:1,this}yn.prototype.clear=MN,yn.prototype.delete=PN,yn.prototype.get=kN,yn.prototype.has=VN,yn.prototype.set=RN;function yr(n){var o=-1,l=n==null?0:n.length;for(this.__data__=new yn;++o<l;)this.add(n[o])}function FN(n){return this.__data__.set(n,h),this}function LN(n){return this.__data__.has(n)}yr.prototype.add=yr.prototype.push=FN,yr.prototype.has=LN;function Bs(n){var o=this.__data__=new bn(n);this.size=o.size}function UN(){this.__data__=new bn,this.size=0}function BN(n){var o=this.__data__,l=o.delete(n);return this.size=o.size,l}function $N(n){return this.__data__.get(n)}function HN(n){return this.__data__.has(n)}function qN(n,o){var l=this.__data__;if(l instanceof bn){var f=l.__data__;if(!Zo||f.length<r-1)return f.push([n,o]),this.size=++l.size,this;l=this.__data__=new yn(f)}return l.set(n,o),this.size=l.size,this}Bs.prototype.clear=UN,Bs.prototype.delete=BN,Bs.prototype.get=$N,Bs.prototype.has=HN,Bs.prototype.set=qN;function Xp(n,o){var l=Re(n),f=!l&&Dr(n),m=!l&&!f&&Yn(n),b=!l&&!f&&!m&&no(n),E=l||f||m||b,O=E?Vu(n.length,X3):[],I=O.length;for(var B in n)(o||ot.call(n,B))&&!(E&&(B=="length"||m&&(B=="offset"||B=="parent")||b&&(B=="buffer"||B=="byteLength"||B=="byteOffset")||xn(B,I)))&&O.push(B);return O}function em(n){var o=n.length;return o?n[Qu(0,o-1)]:s}function WN(n,o){return $a(ns(n),wr(o,0,n.length))}function jN(n){return $a(ns(n))}function Hu(n,o,l){(l!==s&&!$s(n[o],l)||l===s&&!(o in n))&&wn(n,o,l)}function Xo(n,o,l){var f=n[o];(!(ot.call(n,o)&&$s(f,l))||l===s&&!(o in n))&&wn(n,o,l)}function Ta(n,o){for(var l=n.length;l--;)if($s(n[l][0],o))return l;return-1}function zN(n,o,l,f){return zn(n,function(m,b,E){o(f,m,l(m),E)}),f}function tm(n,o){return n&&sn(o,Rt(o),n)}function GN(n,o){return n&&sn(o,os(o),n)}function wn(n,o,l){o=="__proto__"&&Ea?Ea(n,o,{configurable:!0,enumerable:!0,value:l,writable:!0}):n[o]=l}function qu(n,o){for(var l=-1,f=o.length,m=k(f),b=n==null;++l<f;)m[l]=b?s:Cc(n,o[l]);return m}function wr(n,o,l){return n===n&&(l!==s&&(n=n<=l?n:l),o!==s&&(n=n>=o?n:o)),n}function Ds(n,o,l,f,m,b){var E,O=o&g,I=o&w,B=o&x;if(l&&(E=m?l(n,f,m,b):l(n)),E!==s)return E;if(!gt(n))return n;var H=Re(n);if(H){if(E=VI(n),!O)return ns(n,E)}else{var z=Wt(n),le=z==rt||z==Ve;if(Yn(n))return Dm(n,O);if(z==U||z==Ls||le&&!m){if(E=I||le?{}:Wm(n),!O)return I?xI(n,GN(E,n)):CI(n,tm(E,n))}else{if(!ct[z])return m?n:{};E=RI(n,z,O)}}b||(b=new Bs);var ve=b.get(n);if(ve)return ve;b.set(n,E),bg(n)?n.forEach(function(Ne){E.add(Ds(Ne,o,l,Ne,n,b))}):_g(n)&&n.forEach(function(Ne,We){E.set(We,Ds(Ne,o,l,We,n,b))});var Te=B?I?uc:lc:I?os:Rt,Be=H?s:Te(n);return Es(Be||n,function(Ne,We){Be&&(We=Ne,Ne=n[We]),Xo(E,We,Ds(Ne,o,l,We,n,b))}),E}function KN(n){var o=Rt(n);return function(l){return sm(l,n,o)}}function sm(n,o,l){var f=l.length;if(n==null)return!f;for(n=ut(n);f--;){var m=l[f],b=o[m],E=n[m];if(E===s&&!(m in n)||!b(E))return!1}return!0}function nm(n,o,l){if(typeof n!="function")throw new Cs(u);return ii(function(){n.apply(s,l)},o)}function ei(n,o,l,f){var m=-1,b=da,E=!0,O=n.length,I=[],B=o.length;if(!O)return I;l&&(o=pt(o,fs(l))),f?(b=Nu,E=!1):o.length>=r&&(b=Go,E=!1,o=new yr(o));e:for(;++m<O;){var H=n[m],z=l==null?H:l(H);if(H=f||H!==0?H:0,E&&z===z){for(var le=B;le--;)if(o[le]===z)continue e;I.push(H)}else b(o,z,f)||I.push(H)}return I}var zn=Im(tn),rm=Im(ju,!0);function ZN(n,o){var l=!0;return zn(n,function(f,m,b){return l=!!o(f,m,b),l}),l}function Na(n,o,l){for(var f=-1,m=n.length;++f<m;){var b=n[f],E=o(b);if(E!=null&&(O===s?E===E&&!ps(E):l(E,O)))var O=E,I=b}return I}function YN(n,o,l,f){var m=n.length;for(l=Ue(l),l<0&&(l=-l>m?0:m+l),f=f===s||f>m?m:Ue(f),f<0&&(f+=m),f=l>f?0:wg(f);l<f;)n[l++]=o;return n}function om(n,o){var l=[];return zn(n,function(f,m,b){o(f,m,b)&&l.push(f)}),l}function $t(n,o,l,f,m){var b=-1,E=n.length;for(l||(l=LI),m||(m=[]);++b<E;){var O=n[b];o>0&&l(O)?o>1?$t(O,o-1,l,f,m):qn(m,O):f||(m[m.length]=O)}return m}var Wu=Am(),im=Am(!0);function tn(n,o){return n&&Wu(n,o,Rt)}function ju(n,o){return n&&im(n,o,Rt)}function Ia(n,o){return Hn(o,function(l){return Dn(n[l])})}function Er(n,o){o=Kn(o,n);for(var l=0,f=o.length;n!=null&&l<f;)n=n[nn(o[l++])];return l&&l==f?n:s}function am(n,o,l){var f=o(n);return Re(n)?f:qn(f,l(n))}function Qt(n){return n==null?n===s?ye:V:vr&&vr in ut(n)?MI(n):jI(n)}function zu(n,o){return n>o}function JN(n,o){return n!=null&&ot.call(n,o)}function QN(n,o){return n!=null&&o in ut(n)}function XN(n,o,l){return n>=qt(o,l)&&n<Mt(o,l)}function Gu(n,o,l){for(var f=l?Nu:da,m=n[0].length,b=n.length,E=b,O=k(b),I=1/0,B=[];E--;){var H=n[E];E&&o&&(H=pt(H,fs(o))),I=qt(H.length,I),O[E]=!l&&(o||m>=120&&H.length>=120)?new yr(E&&H):s}H=n[0];var z=-1,le=O[0];e:for(;++z<m&&B.length<I;){var ve=H[z],Te=o?o(ve):ve;if(ve=l||ve!==0?ve:0,!(le?Go(le,Te):f(B,Te,l))){for(E=b;--E;){var Be=O[E];if(!(Be?Go(Be,Te):f(n[E],Te,l)))continue e}le&&le.push(Te),B.push(ve)}}return B}function eI(n,o,l,f){return tn(n,function(m,b,E){o(f,l(m),b,E)}),f}function ti(n,o,l){o=Kn(o,n),n=Km(n,o);var f=n==null?n:n[nn(Ss(o))];return f==null?s:ds(f,n,l)}function lm(n){return vt(n)&&Qt(n)==Ls}function tI(n){return vt(n)&&Qt(n)==xe}function sI(n){return vt(n)&&Qt(n)==oe}function si(n,o,l,f,m){return n===o?!0:n==null||o==null||!vt(n)&&!vt(o)?n!==n&&o!==o:nI(n,o,l,f,si,m)}function nI(n,o,l,f,m,b){var E=Re(n),O=Re(o),I=E?cs:Wt(n),B=O?cs:Wt(o);I=I==Ls?U:I,B=B==Ls?U:B;var H=I==U,z=B==U,le=I==B;if(le&&Yn(n)){if(!Yn(o))return!1;E=!0,H=!1}if(le&&!H)return b||(b=new Bs),E||no(n)?$m(n,o,l,f,m,b):II(n,o,I,l,f,m,b);if(!(l&P)){var ve=H&&ot.call(n,"__wrapped__"),Te=z&&ot.call(o,"__wrapped__");if(ve||Te){var Be=ve?n.value():n,Ne=Te?o.value():o;return b||(b=new Bs),m(Be,Ne,l,f,b)}}return le?(b||(b=new Bs),AI(n,o,l,f,m,b)):!1}function rI(n){return vt(n)&&Wt(n)==y}function Ku(n,o,l,f){var m=l.length,b=m,E=!f;if(n==null)return!b;for(n=ut(n);m--;){var O=l[m];if(E&&O[2]?O[1]!==n[O[0]]:!(O[0]in n))return!1}for(;++m<b;){O=l[m];var I=O[0],B=n[I],H=O[1];if(E&&O[2]){if(B===s&&!(I in n))return!1}else{var z=new Bs;if(f)var le=f(B,H,I,n,o,z);if(!(le===s?si(H,B,P|L,f,z):le))return!1}}return!0}function um(n){if(!gt(n)||BI(n))return!1;var o=Dn(n)?rN:K5;return o.test(xr(n))}function oI(n){return vt(n)&&Qt(n)==se}function iI(n){return vt(n)&&Wt(n)==Z}function aI(n){return vt(n)&&Ga(n.length)&&!!ft[Qt(n)]}function cm(n){return typeof n=="function"?n:n==null?is:typeof n=="object"?Re(n)?hm(n[0],n[1]):fm(n):Mg(n)}function Zu(n){if(!oi(n))return cN(n);var o=[];for(var l in ut(n))ot.call(n,l)&&l!="constructor"&&o.push(l);return o}function lI(n){if(!gt(n))return WI(n);var o=oi(n),l=[];for(var f in n)f=="constructor"&&(o||!ot.call(n,f))||l.push(f);return l}function Yu(n,o){return n<o}function dm(n,o){var l=-1,f=rs(n)?k(n.length):[];return zn(n,function(m,b,E){f[++l]=o(m,b,E)}),f}function fm(n){var o=dc(n);return o.length==1&&o[0][2]?zm(o[0][0],o[0][1]):function(l){return l===n||Ku(l,n,o)}}function hm(n,o){return hc(n)&&jm(o)?zm(nn(n),o):function(l){var f=Cc(l,n);return f===s&&f===o?xc(l,n):si(o,f,P|L)}}function Aa(n,o,l,f,m){n!==o&&Wu(o,function(b,E){if(m||(m=new Bs),gt(b))uI(n,o,E,l,Aa,f,m);else{var O=f?f(mc(n,E),b,E+"",n,o,m):s;O===s&&(O=b),Hu(n,E,O)}},os)}function uI(n,o,l,f,m,b,E){var O=mc(n,l),I=mc(o,l),B=E.get(I);if(B){Hu(n,l,B);return}var H=b?b(O,I,l+"",n,o,E):s,z=H===s;if(z){var le=Re(I),ve=!le&&Yn(I),Te=!le&&!ve&&no(I);H=I,le||ve||Te?Re(O)?H=O:wt(O)?H=ns(O):ve?(z=!1,H=Dm(I,!0)):Te?(z=!1,H=Om(I,!0)):H=[]:ai(I)||Dr(I)?(H=O,Dr(O)?H=Eg(O):(!gt(O)||Dn(O))&&(H=Wm(I))):z=!1}z&&(E.set(I,H),m(H,I,f,b,E),E.delete(I)),Hu(n,l,H)}function pm(n,o){var l=n.length;if(l)return o+=o<0?l:0,xn(o,l)?n[o]:s}function mm(n,o,l){o.length?o=pt(o,function(b){return Re(b)?function(E){return Er(E,b.length===1?b[0]:b)}:b}):o=[is];var f=-1;o=pt(o,fs(De()));var m=dm(n,function(b,E,O){var I=pt(o,function(B){return B(b)});return{criteria:I,index:++f,value:b}});return R3(m,function(b,E){return EI(b,E,l)})}function cI(n,o){return gm(n,o,function(l,f){return xc(n,f)})}function gm(n,o,l){for(var f=-1,m=o.length,b={};++f<m;){var E=o[f],O=Er(n,E);l(O,E)&&ni(b,Kn(E,n),O)}return b}function dI(n){return function(o){return Er(o,n)}}function Ju(n,o,l,f){var m=f?V3:zr,b=-1,E=o.length,O=n;for(n===o&&(o=ns(o)),l&&(O=pt(n,fs(l)));++b<E;)for(var I=0,B=o[b],H=l?l(B):B;(I=m(O,H,I,f))>-1;)O!==n&&wa.call(O,I,1),wa.call(n,I,1);return n}function _m(n,o){for(var l=n?o.length:0,f=l-1;l--;){var m=o[l];if(l==f||m!==b){var b=m;xn(m)?wa.call(n,m,1):tc(n,m)}}return n}function Qu(n,o){return n+xa(Jp()*(o-n+1))}function fI(n,o,l,f){for(var m=-1,b=Mt(Ca((o-n)/(l||1)),0),E=k(b);b--;)E[f?b:++m]=n,n+=l;return E}function Xu(n,o){var l="";if(!n||o<1||o>Ce)return l;do o%2&&(l+=n),o=xa(o/2),o&&(n+=n);while(o);return l}function He(n,o){return gc(Gm(n,o,is),n+"")}function hI(n){return em(ro(n))}function pI(n,o){var l=ro(n);return $a(l,wr(o,0,l.length))}function ni(n,o,l,f){if(!gt(n))return n;o=Kn(o,n);for(var m=-1,b=o.length,E=b-1,O=n;O!=null&&++m<b;){var I=nn(o[m]),B=l;if(I==="__proto__"||I==="constructor"||I==="prototype")return n;if(m!=E){var H=O[I];B=f?f(H,I,O):s,B===s&&(B=gt(H)?H:xn(o[m+1])?[]:{})}Xo(O,I,B),O=O[I]}return n}var vm=Da?function(n,o){return Da.set(n,o),n}:is,mI=Ea?function(n,o){return Ea(n,"toString",{configurable:!0,enumerable:!1,value:Oc(o),writable:!0})}:is;function gI(n){return $a(ro(n))}function Os(n,o,l){var f=-1,m=n.length;o<0&&(o=-o>m?0:m+o),l=l>m?m:l,l<0&&(l+=m),m=o>l?0:l-o>>>0,o>>>=0;for(var b=k(m);++f<m;)b[f]=n[f+o];return b}function _I(n,o){var l;return zn(n,function(f,m,b){return l=o(f,m,b),!l}),!!l}function Ma(n,o,l){var f=0,m=n==null?f:n.length;if(typeof o=="number"&&o===o&&m<=ss){for(;f<m;){var b=f+m>>>1,E=n[b];E!==null&&!ps(E)&&(l?E<=o:E<o)?f=b+1:m=b}return m}return ec(n,o,is,l)}function ec(n,o,l,f){var m=0,b=n==null?0:n.length;if(b===0)return 0;o=l(o);for(var E=o!==o,O=o===null,I=ps(o),B=o===s;m<b;){var H=xa((m+b)/2),z=l(n[H]),le=z!==s,ve=z===null,Te=z===z,Be=ps(z);if(E)var Ne=f||Te;else B?Ne=Te&&(f||le):O?Ne=Te&&le&&(f||!ve):I?Ne=Te&&le&&!ve&&(f||!Be):ve||Be?Ne=!1:Ne=f?z<=o:z<o;Ne?m=H+1:b=H}return qt(b,bs)}function bm(n,o){for(var l=-1,f=n.length,m=0,b=[];++l<f;){var E=n[l],O=o?o(E):E;if(!l||!$s(O,I)){var I=O;b[m++]=E===0?0:E}}return b}function ym(n){return typeof n=="number"?n:ps(n)?vs:+n}function hs(n){if(typeof n=="string")return n;if(Re(n))return pt(n,hs)+"";if(ps(n))return Qp?Qp.call(n):"";var o=n+"";return o=="0"&&1/n==-de?"-0":o}function Gn(n,o,l){var f=-1,m=da,b=n.length,E=!0,O=[],I=O;if(l)E=!1,m=Nu;else if(b>=r){var B=o?null:TI(n);if(B)return ha(B);E=!1,m=Go,I=new yr}else I=o?[]:O;e:for(;++f<b;){var H=n[f],z=o?o(H):H;if(H=l||H!==0?H:0,E&&z===z){for(var le=I.length;le--;)if(I[le]===z)continue e;o&&I.push(z),O.push(H)}else m(I,z,l)||(I!==O&&I.push(z),O.push(H))}return O}function tc(n,o){return o=Kn(o,n),n=Km(n,o),n==null||delete n[nn(Ss(o))]}function wm(n,o,l,f){return ni(n,o,l(Er(n,o)),f)}function Pa(n,o,l,f){for(var m=n.length,b=f?m:-1;(f?b--:++b<m)&&o(n[b],b,n););return l?Os(n,f?0:b,f?b+1:m):Os(n,f?b+1:0,f?m:b)}function Em(n,o){var l=n;return l instanceof ze&&(l=l.value()),Iu(o,function(f,m){return m.func.apply(m.thisArg,qn([f],m.args))},l)}function sc(n,o,l){var f=n.length;if(f<2)return f?Gn(n[0]):[];for(var m=-1,b=k(f);++m<f;)for(var E=n[m],O=-1;++O<f;)O!=m&&(b[m]=ei(b[m]||E,n[O],o,l));return Gn($t(b,1),o,l)}function Cm(n,o,l){for(var f=-1,m=n.length,b=o.length,E={};++f<m;){var O=f<b?o[f]:s;l(E,n[f],O)}return E}function nc(n){return wt(n)?n:[]}function rc(n){return typeof n=="function"?n:is}function Kn(n,o){return Re(n)?n:hc(n,o)?[n]:Qm(nt(n))}var vI=He;function Zn(n,o,l){var f=n.length;return l=l===s?f:l,!o&&l>=f?n:Os(n,o,l)}var xm=oN||function(n){return Bt.clearTimeout(n)};function Dm(n,o){if(o)return n.slice();var l=n.length,f=zp?zp(l):new n.constructor(l);return n.copy(f),f}function oc(n){var o=new n.constructor(n.byteLength);return new ba(o).set(new ba(n)),o}function bI(n,o){var l=o?oc(n.buffer):n.buffer;return new n.constructor(l,n.byteOffset,n.byteLength)}function yI(n){var o=new n.constructor(n.source,lp.exec(n));return o.lastIndex=n.lastIndex,o}function wI(n){return Qo?ut(Qo.call(n)):{}}function Om(n,o){var l=o?oc(n.buffer):n.buffer;return new n.constructor(l,n.byteOffset,n.length)}function Sm(n,o){if(n!==o){var l=n!==s,f=n===null,m=n===n,b=ps(n),E=o!==s,O=o===null,I=o===o,B=ps(o);if(!O&&!B&&!b&&n>o||b&&E&&I&&!O&&!B||f&&E&&I||!l&&I||!m)return 1;if(!f&&!b&&!B&&n<o||B&&l&&m&&!f&&!b||O&&l&&m||!E&&m||!I)return-1}return 0}function EI(n,o,l){for(var f=-1,m=n.criteria,b=o.criteria,E=m.length,O=l.length;++f<E;){var I=Sm(m[f],b[f]);if(I){if(f>=O)return I;var B=l[f];return I*(B=="desc"?-1:1)}}return n.index-o.index}function Tm(n,o,l,f){for(var m=-1,b=n.length,E=l.length,O=-1,I=o.length,B=Mt(b-E,0),H=k(I+B),z=!f;++O<I;)H[O]=o[O];for(;++m<E;)(z||m<b)&&(H[l[m]]=n[m]);for(;B--;)H[O++]=n[m++];return H}function Nm(n,o,l,f){for(var m=-1,b=n.length,E=-1,O=l.length,I=-1,B=o.length,H=Mt(b-O,0),z=k(H+B),le=!f;++m<H;)z[m]=n[m];for(var ve=m;++I<B;)z[ve+I]=o[I];for(;++E<O;)(le||m<b)&&(z[ve+l[E]]=n[m++]);return z}function ns(n,o){var l=-1,f=n.length;for(o||(o=k(f));++l<f;)o[l]=n[l];return o}function sn(n,o,l,f){var m=!l;l||(l={});for(var b=-1,E=o.length;++b<E;){var O=o[b],I=f?f(l[O],n[O],O,l,n):s;I===s&&(I=n[O]),m?wn(l,O,I):Xo(l,O,I)}return l}function CI(n,o){return sn(n,fc(n),o)}function xI(n,o){return sn(n,Hm(n),o)}function ka(n,o){return function(l,f){var m=Re(l)?N3:zN,b=o?o():{};return m(l,n,De(f,2),b)}}function eo(n){return He(function(o,l){var f=-1,m=l.length,b=m>1?l[m-1]:s,E=m>2?l[2]:s;for(b=n.length>3&&typeof b=="function"?(m--,b):s,E&&Xt(l[0],l[1],E)&&(b=m<3?s:b,m=1),o=ut(o);++f<m;){var O=l[f];O&&n(o,O,f,b)}return o})}function Im(n,o){return function(l,f){if(l==null)return l;if(!rs(l))return n(l,f);for(var m=l.length,b=o?m:-1,E=ut(l);(o?b--:++b<m)&&f(E[b],b,E)!==!1;);return l}}function Am(n){return function(o,l,f){for(var m=-1,b=ut(o),E=f(o),O=E.length;O--;){var I=E[n?O:++m];if(l(b[I],I,b)===!1)break}return o}}function DI(n,o,l){var f=o&te,m=ri(n);function b(){var E=this&&this!==Bt&&this instanceof b?m:n;return E.apply(f?l:this,arguments)}return b}function Mm(n){return function(o){o=nt(o);var l=Gr(o)?Us(o):s,f=l?l[0]:o.charAt(0),m=l?Zn(l,1).join(""):o.slice(1);return f[n]()+m}}function to(n){return function(o){return Iu(Ig(Ng(o).replace(m3,"")),n,"")}}function ri(n){return function(){var o=arguments;switch(o.length){case 0:return new n;case 1:return new n(o[0]);case 2:return new n(o[0],o[1]);case 3:return new n(o[0],o[1],o[2]);case 4:return new n(o[0],o[1],o[2],o[3]);case 5:return new n(o[0],o[1],o[2],o[3],o[4]);case 6:return new n(o[0],o[1],o[2],o[3],o[4],o[5]);case 7:return new n(o[0],o[1],o[2],o[3],o[4],o[5],o[6])}var l=Xr(n.prototype),f=n.apply(l,o);return gt(f)?f:l}}function OI(n,o,l){var f=ri(n);function m(){for(var b=arguments.length,E=k(b),O=b,I=so(m);O--;)E[O]=arguments[O];var B=b<3&&E[0]!==I&&E[b-1]!==I?[]:Wn(E,I);if(b-=B.length,b<l)return Fm(n,o,Va,m.placeholder,s,E,B,s,s,l-b);var H=this&&this!==Bt&&this instanceof m?f:n;return ds(H,this,E)}return m}function Pm(n){return function(o,l,f){var m=ut(o);if(!rs(o)){var b=De(l,3);o=Rt(o),l=function(O){return b(m[O],O,m)}}var E=n(o,l,f);return E>-1?m[b?o[E]:E]:s}}function km(n){return Cn(function(o){var l=o.length,f=l,m=xs.prototype.thru;for(n&&o.reverse();f--;){var b=o[f];if(typeof b!="function")throw new Cs(u);if(m&&!E&&Ua(b)=="wrapper")var E=new xs([],!0)}for(f=E?f:l;++f<l;){b=o[f];var O=Ua(b),I=O=="wrapper"?cc(b):s;I&&pc(I[0])&&I[1]==(be|J|X|Ae)&&!I[4].length&&I[9]==1?E=E[Ua(I[0])].apply(E,I[3]):E=b.length==1&&pc(b)?E[O]():E.thru(b)}return function(){var B=arguments,H=B[0];if(E&&B.length==1&&Re(H))return E.plant(H).value();for(var z=0,le=l?o[z].apply(this,B):H;++z<l;)le=o[z].call(this,le);return le}})}function Va(n,o,l,f,m,b,E,O,I,B){var H=o&be,z=o&te,le=o&N,ve=o&(J|we),Te=o&ae,Be=le?s:ri(n);function Ne(){for(var We=arguments.length,Ke=k(We),ms=We;ms--;)Ke[ms]=arguments[ms];if(ve)var es=so(Ne),gs=L3(Ke,es);if(f&&(Ke=Tm(Ke,f,m,ve)),b&&(Ke=Nm(Ke,b,E,ve)),We-=gs,ve&&We<B){var Et=Wn(Ke,es);return Fm(n,o,Va,Ne.placeholder,l,Ke,Et,O,I,B-We)}var Hs=z?l:this,Sn=le?Hs[n]:n;return We=Ke.length,O?Ke=zI(Ke,O):Te&&We>1&&Ke.reverse(),H&&I<We&&(Ke.length=I),this&&this!==Bt&&this instanceof Ne&&(Sn=Be||ri(Sn)),Sn.apply(Hs,Ke)}return Ne}function Vm(n,o){return function(l,f){return eI(l,n,o(f),{})}}function Ra(n,o){return function(l,f){var m;if(l===s&&f===s)return o;if(l!==s&&(m=l),f!==s){if(m===s)return f;typeof l=="string"||typeof f=="string"?(l=hs(l),f=hs(f)):(l=ym(l),f=ym(f)),m=n(l,f)}return m}}function ic(n){return Cn(function(o){return o=pt(o,fs(De())),He(function(l){var f=this;return n(o,function(m){return ds(m,f,l)})})})}function Fa(n,o){o=o===s?" ":hs(o);var l=o.length;if(l<2)return l?Xu(o,n):o;var f=Xu(o,Ca(n/Kr(o)));return Gr(o)?Zn(Us(f),0,n).join(""):f.slice(0,n)}function SI(n,o,l,f){var m=o&te,b=ri(n);function E(){for(var O=-1,I=arguments.length,B=-1,H=f.length,z=k(H+I),le=this&&this!==Bt&&this instanceof E?b:n;++B<H;)z[B]=f[B];for(;I--;)z[B++]=arguments[++O];return ds(le,m?l:this,z)}return E}function Rm(n){return function(o,l,f){return f&&typeof f!="number"&&Xt(o,l,f)&&(l=f=s),o=On(o),l===s?(l=o,o=0):l=On(l),f=f===s?o<l?1:-1:On(f),fI(o,l,f,n)}}function La(n){return function(o,l){return typeof o=="string"&&typeof l=="string"||(o=Ts(o),l=Ts(l)),n(o,l)}}function Fm(n,o,l,f,m,b,E,O,I,B){var H=o&J,z=H?E:s,le=H?s:E,ve=H?b:s,Te=H?s:b;o|=H?X:pe,o&=~(H?pe:X),o&re||(o&=~(te|N));var Be=[n,o,m,ve,z,Te,le,O,I,B],Ne=l.apply(s,Be);return pc(n)&&Zm(Ne,Be),Ne.placeholder=f,Ym(Ne,n,o)}function ac(n){var o=At[n];return function(l,f){if(l=Ts(l),f=f==null?0:qt(Ue(f),292),f&&Yp(l)){var m=(nt(l)+"e").split("e"),b=o(m[0]+"e"+(+m[1]+f));return m=(nt(b)+"e").split("e"),+(m[0]+"e"+(+m[1]-f))}return o(l)}}var TI=Jr&&1/ha(new Jr([,-0]))[1]==de?function(n){return new Jr(n)}:Nc;function Lm(n){return function(o){var l=Wt(o);return l==y?Fu(o):l==Z?j3(o):F3(o,n(o))}}function En(n,o,l,f,m,b,E,O){var I=o&N;if(!I&&typeof n!="function")throw new Cs(u);var B=f?f.length:0;if(B||(o&=~(X|pe),f=m=s),E=E===s?E:Mt(Ue(E),0),O=O===s?O:Ue(O),B-=m?m.length:0,o&pe){var H=f,z=m;f=m=s}var le=I?s:cc(n),ve=[n,o,l,f,m,H,z,b,E,O];if(le&&qI(ve,le),n=ve[0],o=ve[1],l=ve[2],f=ve[3],m=ve[4],O=ve[9]=ve[9]===s?I?0:n.length:Mt(ve[9]-B,0),!O&&o&(J|we)&&(o&=~(J|we)),!o||o==te)var Te=DI(n,o,l);else o==J||o==we?Te=OI(n,o,O):(o==X||o==(te|X))&&!m.length?Te=SI(n,o,l,f):Te=Va.apply(s,ve);var Be=le?vm:Zm;return Ym(Be(Te,ve),n,o)}function Um(n,o,l,f){return n===s||$s(n,Yr[l])&&!ot.call(f,l)?o:n}function Bm(n,o,l,f,m,b){return gt(n)&&gt(o)&&(b.set(o,n),Aa(n,o,s,Bm,b),b.delete(o)),n}function NI(n){return ai(n)?s:n}function $m(n,o,l,f,m,b){var E=l&P,O=n.length,I=o.length;if(O!=I&&!(E&&I>O))return!1;var B=b.get(n),H=b.get(o);if(B&&H)return B==o&&H==n;var z=-1,le=!0,ve=l&L?new yr:s;for(b.set(n,o),b.set(o,n);++z<O;){var Te=n[z],Be=o[z];if(f)var Ne=E?f(Be,Te,z,o,n,b):f(Te,Be,z,n,o,b);if(Ne!==s){if(Ne)continue;le=!1;break}if(ve){if(!Au(o,function(We,Ke){if(!Go(ve,Ke)&&(Te===We||m(Te,We,l,f,b)))return ve.push(Ke)})){le=!1;break}}else if(!(Te===Be||m(Te,Be,l,f,b))){le=!1;break}}return b.delete(n),b.delete(o),le}function II(n,o,l,f,m,b,E){switch(l){case Le:if(n.byteLength!=o.byteLength||n.byteOffset!=o.byteOffset)return!1;n=n.buffer,o=o.buffer;case xe:return!(n.byteLength!=o.byteLength||!b(new ba(n),new ba(o)));case ie:case oe:case C:return $s(+n,+o);case ke:return n.name==o.name&&n.message==o.message;case se:case ee:return n==o+"";case y:var O=Fu;case Z:var I=f&P;if(O||(O=ha),n.size!=o.size&&!I)return!1;var B=E.get(n);if(B)return B==o;f|=L,E.set(n,o);var H=$m(O(n),O(o),f,m,b,E);return E.delete(n),H;case K:if(Qo)return Qo.call(n)==Qo.call(o)}return!1}function AI(n,o,l,f,m,b){var E=l&P,O=lc(n),I=O.length,B=lc(o),H=B.length;if(I!=H&&!E)return!1;for(var z=I;z--;){var le=O[z];if(!(E?le in o:ot.call(o,le)))return!1}var ve=b.get(n),Te=b.get(o);if(ve&&Te)return ve==o&&Te==n;var Be=!0;b.set(n,o),b.set(o,n);for(var Ne=E;++z<I;){le=O[z];var We=n[le],Ke=o[le];if(f)var ms=E?f(Ke,We,le,o,n,b):f(We,Ke,le,n,o,b);if(!(ms===s?We===Ke||m(We,Ke,l,f,b):ms)){Be=!1;break}Ne||(Ne=le=="constructor")}if(Be&&!Ne){var es=n.constructor,gs=o.constructor;es!=gs&&"constructor"in n&&"constructor"in o&&!(typeof es=="function"&&es instanceof es&&typeof gs=="function"&&gs instanceof gs)&&(Be=!1)}return b.delete(n),b.delete(o),Be}function Cn(n){return gc(Gm(n,s,sg),n+"")}function lc(n){return am(n,Rt,fc)}function uc(n){return am(n,os,Hm)}var cc=Da?function(n){return Da.get(n)}:Nc;function Ua(n){for(var o=n.name+"",l=Qr[o],f=ot.call(Qr,o)?l.length:0;f--;){var m=l[f],b=m.func;if(b==null||b==n)return m.name}return o}function so(n){var o=ot.call(v,"placeholder")?v:n;return o.placeholder}function De(){var n=v.iteratee||Sc;return n=n===Sc?cm:n,arguments.length?n(arguments[0],arguments[1]):n}function Ba(n,o){var l=n.__data__;return UI(o)?l[typeof o=="string"?"string":"hash"]:l.map}function dc(n){for(var o=Rt(n),l=o.length;l--;){var f=o[l],m=n[f];o[l]=[f,m,jm(m)]}return o}function Cr(n,o){var l=H3(n,o);return um(l)?l:s}function MI(n){var o=ot.call(n,vr),l=n[vr];try{n[vr]=s;var f=!0}catch{}var m=_a.call(n);return f&&(o?n[vr]=l:delete n[vr]),m}var fc=Uu?function(n){return n==null?[]:(n=ut(n),Hn(Uu(n),function(o){return Kp.call(n,o)}))}:Ic,Hm=Uu?function(n){for(var o=[];n;)qn(o,fc(n)),n=ya(n);return o}:Ic,Wt=Qt;(Bu&&Wt(new Bu(new ArrayBuffer(1)))!=Le||Zo&&Wt(new Zo)!=y||$u&&Wt($u.resolve())!=q||Jr&&Wt(new Jr)!=Z||Yo&&Wt(new Yo)!=ne)&&(Wt=function(n){var o=Qt(n),l=o==U?n.constructor:s,f=l?xr(l):"";if(f)switch(f){case pN:return Le;case mN:return y;case gN:return q;case _N:return Z;case vN:return ne}return o});function PI(n,o,l){for(var f=-1,m=l.length;++f<m;){var b=l[f],E=b.size;switch(b.type){case"drop":n+=E;break;case"dropRight":o-=E;break;case"take":o=qt(o,n+E);break;case"takeRight":n=Mt(n,o-E);break}}return{start:n,end:o}}function kI(n){var o=n.match(B5);return o?o[1].split($5):[]}function qm(n,o,l){o=Kn(o,n);for(var f=-1,m=o.length,b=!1;++f<m;){var E=nn(o[f]);if(!(b=n!=null&&l(n,E)))break;n=n[E]}return b||++f!=m?b:(m=n==null?0:n.length,!!m&&Ga(m)&&xn(E,m)&&(Re(n)||Dr(n)))}function VI(n){var o=n.length,l=new n.constructor(o);return o&&typeof n[0]=="string"&&ot.call(n,"index")&&(l.index=n.index,l.input=n.input),l}function Wm(n){return typeof n.constructor=="function"&&!oi(n)?Xr(ya(n)):{}}function RI(n,o,l){var f=n.constructor;switch(o){case xe:return oc(n);case ie:case oe:return new f(+n);case Le:return bI(n,l);case Xe:case Ye:case Vt:case Dt:case Jt:case Ut:case vn:case Wr:case It:return Om(n,l);case y:return new f;case C:case ee:return new f(n);case se:return yI(n);case Z:return new f;case K:return wI(n)}}function FI(n,o){var l=o.length;if(!l)return n;var f=l-1;return o[f]=(l>1?"& ":"")+o[f],o=o.join(l>2?", ":" "),n.replace(U5,`{
/* [wrapped with `+o+`] */
`)}function LI(n){return Re(n)||Dr(n)||!!(Zp&&n&&n[Zp])}function xn(n,o){var l=typeof n;return o=o??Ce,!!o&&(l=="number"||l!="symbol"&&Y5.test(n))&&n>-1&&n%1==0&&n<o}function Xt(n,o,l){if(!gt(l))return!1;var f=typeof o;return(f=="number"?rs(l)&&xn(o,l.length):f=="string"&&o in l)?$s(l[o],n):!1}function hc(n,o){if(Re(n))return!1;var l=typeof n;return l=="number"||l=="symbol"||l=="boolean"||n==null||ps(n)?!0:V5.test(n)||!k5.test(n)||o!=null&&n in ut(o)}function UI(n){var o=typeof n;return o=="string"||o=="number"||o=="symbol"||o=="boolean"?n!=="__proto__":n===null}function pc(n){var o=Ua(n),l=v[o];if(typeof l!="function"||!(o in ze.prototype))return!1;if(n===l)return!0;var f=cc(l);return!!f&&n===f[0]}function BI(n){return!!jp&&jp in n}var $I=ma?Dn:Ac;function oi(n){var o=n&&n.constructor,l=typeof o=="function"&&o.prototype||Yr;return n===l}function jm(n){return n===n&&!gt(n)}function zm(n,o){return function(l){return l==null?!1:l[n]===o&&(o!==s||n in ut(l))}}function HI(n){var o=ja(n,function(f){return l.size===_&&l.clear(),f}),l=o.cache;return o}function qI(n,o){var l=n[1],f=o[1],m=l|f,b=m<(te|N|be),E=f==be&&l==J||f==be&&l==Ae&&n[7].length<=o[8]||f==(be|Ae)&&o[7].length<=o[8]&&l==J;if(!(b||E))return n;f&te&&(n[2]=o[2],m|=l&te?0:re);var O=o[3];if(O){var I=n[3];n[3]=I?Tm(I,O,o[4]):O,n[4]=I?Wn(n[3],p):o[4]}return O=o[5],O&&(I=n[5],n[5]=I?Nm(I,O,o[6]):O,n[6]=I?Wn(n[5],p):o[6]),O=o[7],O&&(n[7]=O),f&be&&(n[8]=n[8]==null?o[8]:qt(n[8],o[8])),n[9]==null&&(n[9]=o[9]),n[0]=o[0],n[1]=m,n}function WI(n){var o=[];if(n!=null)for(var l in ut(n))o.push(l);return o}function jI(n){return _a.call(n)}function Gm(n,o,l){return o=Mt(o===s?n.length-1:o,0),function(){for(var f=arguments,m=-1,b=Mt(f.length-o,0),E=k(b);++m<b;)E[m]=f[o+m];m=-1;for(var O=k(o+1);++m<o;)O[m]=f[m];return O[o]=l(E),ds(n,this,O)}}function Km(n,o){return o.length<2?n:Er(n,Os(o,0,-1))}function zI(n,o){for(var l=n.length,f=qt(o.length,l),m=ns(n);f--;){var b=o[f];n[f]=xn(b,l)?m[b]:s}return n}function mc(n,o){if(!(o==="constructor"&&typeof n[o]=="function")&&o!="__proto__")return n[o]}var Zm=Jm(vm),ii=aN||function(n,o){return Bt.setTimeout(n,o)},gc=Jm(mI);function Ym(n,o,l){var f=o+"";return gc(n,FI(f,GI(kI(f),l)))}function Jm(n){var o=0,l=0;return function(){var f=dN(),m=Ge-(f-l);if(l=f,m>0){if(++o>=ue)return arguments[0]}else o=0;return n.apply(s,arguments)}}function $a(n,o){var l=-1,f=n.length,m=f-1;for(o=o===s?f:o;++l<o;){var b=Qu(l,m),E=n[b];n[b]=n[l],n[l]=E}return n.length=o,n}var Qm=HI(function(n){var o=[];return n.charCodeAt(0)===46&&o.push(""),n.replace(R5,function(l,f,m,b){o.push(m?b.replace(W5,"$1"):f||l)}),o});function nn(n){if(typeof n=="string"||ps(n))return n;var o=n+"";return o=="0"&&1/n==-de?"-0":o}function xr(n){if(n!=null){try{return ga.call(n)}catch{}try{return n+""}catch{}}return""}function GI(n,o){return Es(en,function(l){var f="_."+l[0];o&l[1]&&!da(n,f)&&n.push(f)}),n.sort()}function Xm(n){if(n instanceof ze)return n.clone();var o=new xs(n.__wrapped__,n.__chain__);return o.__actions__=ns(n.__actions__),o.__index__=n.__index__,o.__values__=n.__values__,o}function KI(n,o,l){(l?Xt(n,o,l):o===s)?o=1:o=Mt(Ue(o),0);var f=n==null?0:n.length;if(!f||o<1)return[];for(var m=0,b=0,E=k(Ca(f/o));m<f;)E[b++]=Os(n,m,m+=o);return E}function ZI(n){for(var o=-1,l=n==null?0:n.length,f=0,m=[];++o<l;){var b=n[o];b&&(m[f++]=b)}return m}function YI(){var n=arguments.length;if(!n)return[];for(var o=k(n-1),l=arguments[0],f=n;f--;)o[f-1]=arguments[f];return qn(Re(l)?ns(l):[l],$t(o,1))}var JI=He(function(n,o){return wt(n)?ei(n,$t(o,1,wt,!0)):[]}),QI=He(function(n,o){var l=Ss(o);return wt(l)&&(l=s),wt(n)?ei(n,$t(o,1,wt,!0),De(l,2)):[]}),XI=He(function(n,o){var l=Ss(o);return wt(l)&&(l=s),wt(n)?ei(n,$t(o,1,wt,!0),s,l):[]});function e4(n,o,l){var f=n==null?0:n.length;return f?(o=l||o===s?1:Ue(o),Os(n,o<0?0:o,f)):[]}function t4(n,o,l){var f=n==null?0:n.length;return f?(o=l||o===s?1:Ue(o),o=f-o,Os(n,0,o<0?0:o)):[]}function s4(n,o){return n&&n.length?Pa(n,De(o,3),!0,!0):[]}function n4(n,o){return n&&n.length?Pa(n,De(o,3),!0):[]}function r4(n,o,l,f){var m=n==null?0:n.length;return m?(l&&typeof l!="number"&&Xt(n,o,l)&&(l=0,f=m),YN(n,o,l,f)):[]}function eg(n,o,l){var f=n==null?0:n.length;if(!f)return-1;var m=l==null?0:Ue(l);return m<0&&(m=Mt(f+m,0)),fa(n,De(o,3),m)}function tg(n,o,l){var f=n==null?0:n.length;if(!f)return-1;var m=f-1;return l!==s&&(m=Ue(l),m=l<0?Mt(f+m,0):qt(m,f-1)),fa(n,De(o,3),m,!0)}function sg(n){var o=n==null?0:n.length;return o?$t(n,1):[]}function o4(n){var o=n==null?0:n.length;return o?$t(n,de):[]}function i4(n,o){var l=n==null?0:n.length;return l?(o=o===s?1:Ue(o),$t(n,o)):[]}function a4(n){for(var o=-1,l=n==null?0:n.length,f={};++o<l;){var m=n[o];f[m[0]]=m[1]}return f}function ng(n){return n&&n.length?n[0]:s}function l4(n,o,l){var f=n==null?0:n.length;if(!f)return-1;var m=l==null?0:Ue(l);return m<0&&(m=Mt(f+m,0)),zr(n,o,m)}function u4(n){var o=n==null?0:n.length;return o?Os(n,0,-1):[]}var c4=He(function(n){var o=pt(n,nc);return o.length&&o[0]===n[0]?Gu(o):[]}),d4=He(function(n){var o=Ss(n),l=pt(n,nc);return o===Ss(l)?o=s:l.pop(),l.length&&l[0]===n[0]?Gu(l,De(o,2)):[]}),f4=He(function(n){var o=Ss(n),l=pt(n,nc);return o=typeof o=="function"?o:s,o&&l.pop(),l.length&&l[0]===n[0]?Gu(l,s,o):[]});function h4(n,o){return n==null?"":uN.call(n,o)}function Ss(n){var o=n==null?0:n.length;return o?n[o-1]:s}function p4(n,o,l){var f=n==null?0:n.length;if(!f)return-1;var m=f;return l!==s&&(m=Ue(l),m=m<0?Mt(f+m,0):qt(m,f-1)),o===o?G3(n,o,m):fa(n,Fp,m,!0)}function m4(n,o){return n&&n.length?pm(n,Ue(o)):s}var g4=He(rg);function rg(n,o){return n&&n.length&&o&&o.length?Ju(n,o):n}function _4(n,o,l){return n&&n.length&&o&&o.length?Ju(n,o,De(l,2)):n}function v4(n,o,l){return n&&n.length&&o&&o.length?Ju(n,o,s,l):n}var b4=Cn(function(n,o){var l=n==null?0:n.length,f=qu(n,o);return _m(n,pt(o,function(m){return xn(m,l)?+m:m}).sort(Sm)),f});function y4(n,o){var l=[];if(!(n&&n.length))return l;var f=-1,m=[],b=n.length;for(o=De(o,3);++f<b;){var E=n[f];o(E,f,n)&&(l.push(E),m.push(f))}return _m(n,m),l}function _c(n){return n==null?n:hN.call(n)}function w4(n,o,l){var f=n==null?0:n.length;return f?(l&&typeof l!="number"&&Xt(n,o,l)?(o=0,l=f):(o=o==null?0:Ue(o),l=l===s?f:Ue(l)),Os(n,o,l)):[]}function E4(n,o){return Ma(n,o)}function C4(n,o,l){return ec(n,o,De(l,2))}function x4(n,o){var l=n==null?0:n.length;if(l){var f=Ma(n,o);if(f<l&&$s(n[f],o))return f}return-1}function D4(n,o){return Ma(n,o,!0)}function O4(n,o,l){return ec(n,o,De(l,2),!0)}function S4(n,o){var l=n==null?0:n.length;if(l){var f=Ma(n,o,!0)-1;if($s(n[f],o))return f}return-1}function T4(n){return n&&n.length?bm(n):[]}function N4(n,o){return n&&n.length?bm(n,De(o,2)):[]}function I4(n){var o=n==null?0:n.length;return o?Os(n,1,o):[]}function A4(n,o,l){return n&&n.length?(o=l||o===s?1:Ue(o),Os(n,0,o<0?0:o)):[]}function M4(n,o,l){var f=n==null?0:n.length;return f?(o=l||o===s?1:Ue(o),o=f-o,Os(n,o<0?0:o,f)):[]}function P4(n,o){return n&&n.length?Pa(n,De(o,3),!1,!0):[]}function k4(n,o){return n&&n.length?Pa(n,De(o,3)):[]}var V4=He(function(n){return Gn($t(n,1,wt,!0))}),R4=He(function(n){var o=Ss(n);return wt(o)&&(o=s),Gn($t(n,1,wt,!0),De(o,2))}),F4=He(function(n){var o=Ss(n);return o=typeof o=="function"?o:s,Gn($t(n,1,wt,!0),s,o)});function L4(n){return n&&n.length?Gn(n):[]}function U4(n,o){return n&&n.length?Gn(n,De(o,2)):[]}function B4(n,o){return o=typeof o=="function"?o:s,n&&n.length?Gn(n,s,o):[]}function vc(n){if(!(n&&n.length))return[];var o=0;return n=Hn(n,function(l){if(wt(l))return o=Mt(l.length,o),!0}),Vu(o,function(l){return pt(n,Mu(l))})}function og(n,o){if(!(n&&n.length))return[];var l=vc(n);return o==null?l:pt(l,function(f){return ds(o,s,f)})}var $4=He(function(n,o){return wt(n)?ei(n,o):[]}),H4=He(function(n){return sc(Hn(n,wt))}),q4=He(function(n){var o=Ss(n);return wt(o)&&(o=s),sc(Hn(n,wt),De(o,2))}),W4=He(function(n){var o=Ss(n);return o=typeof o=="function"?o:s,sc(Hn(n,wt),s,o)}),j4=He(vc);function z4(n,o){return Cm(n||[],o||[],Xo)}function G4(n,o){return Cm(n||[],o||[],ni)}var K4=He(function(n){var o=n.length,l=o>1?n[o-1]:s;return l=typeof l=="function"?(n.pop(),l):s,og(n,l)});function ig(n){var o=v(n);return o.__chain__=!0,o}function Z4(n,o){return o(n),n}function Ha(n,o){return o(n)}var Y4=Cn(function(n){var o=n.length,l=o?n[0]:0,f=this.__wrapped__,m=function(b){return qu(b,n)};return o>1||this.__actions__.length||!(f instanceof ze)||!xn(l)?this.thru(m):(f=f.slice(l,+l+(o?1:0)),f.__actions__.push({func:Ha,args:[m],thisArg:s}),new xs(f,this.__chain__).thru(function(b){return o&&!b.length&&b.push(s),b}))});function J4(){return ig(this)}function Q4(){return new xs(this.value(),this.__chain__)}function X4(){this.__values__===s&&(this.__values__=yg(this.value()));var n=this.__index__>=this.__values__.length,o=n?s:this.__values__[this.__index__++];return{done:n,value:o}}function eA(){return this}function tA(n){for(var o,l=this;l instanceof Sa;){var f=Xm(l);f.__index__=0,f.__values__=s,o?m.__wrapped__=f:o=f;var m=f;l=l.__wrapped__}return m.__wrapped__=n,o}function sA(){var n=this.__wrapped__;if(n instanceof ze){var o=n;return this.__actions__.length&&(o=new ze(this)),o=o.reverse(),o.__actions__.push({func:Ha,args:[_c],thisArg:s}),new xs(o,this.__chain__)}return this.thru(_c)}function nA(){return Em(this.__wrapped__,this.__actions__)}var rA=ka(function(n,o,l){ot.call(n,l)?++n[l]:wn(n,l,1)});function oA(n,o,l){var f=Re(n)?Vp:ZN;return l&&Xt(n,o,l)&&(o=s),f(n,De(o,3))}function iA(n,o){var l=Re(n)?Hn:om;return l(n,De(o,3))}var aA=Pm(eg),lA=Pm(tg);function uA(n,o){return $t(qa(n,o),1)}function cA(n,o){return $t(qa(n,o),de)}function dA(n,o,l){return l=l===s?1:Ue(l),$t(qa(n,o),l)}function ag(n,o){var l=Re(n)?Es:zn;return l(n,De(o,3))}function lg(n,o){var l=Re(n)?I3:rm;return l(n,De(o,3))}var fA=ka(function(n,o,l){ot.call(n,l)?n[l].push(o):wn(n,l,[o])});function hA(n,o,l,f){n=rs(n)?n:ro(n),l=l&&!f?Ue(l):0;var m=n.length;return l<0&&(l=Mt(m+l,0)),Ka(n)?l<=m&&n.indexOf(o,l)>-1:!!m&&zr(n,o,l)>-1}var pA=He(function(n,o,l){var f=-1,m=typeof o=="function",b=rs(n)?k(n.length):[];return zn(n,function(E){b[++f]=m?ds(o,E,l):ti(E,o,l)}),b}),mA=ka(function(n,o,l){wn(n,l,o)});function qa(n,o){var l=Re(n)?pt:dm;return l(n,De(o,3))}function gA(n,o,l,f){return n==null?[]:(Re(o)||(o=o==null?[]:[o]),l=f?s:l,Re(l)||(l=l==null?[]:[l]),mm(n,o,l))}var _A=ka(function(n,o,l){n[l?0:1].push(o)},function(){return[[],[]]});function vA(n,o,l){var f=Re(n)?Iu:Up,m=arguments.length<3;return f(n,De(o,4),l,m,zn)}function bA(n,o,l){var f=Re(n)?A3:Up,m=arguments.length<3;return f(n,De(o,4),l,m,rm)}function yA(n,o){var l=Re(n)?Hn:om;return l(n,za(De(o,3)))}function wA(n){var o=Re(n)?em:hI;return o(n)}function EA(n,o,l){(l?Xt(n,o,l):o===s)?o=1:o=Ue(o);var f=Re(n)?WN:pI;return f(n,o)}function CA(n){var o=Re(n)?jN:gI;return o(n)}function xA(n){if(n==null)return 0;if(rs(n))return Ka(n)?Kr(n):n.length;var o=Wt(n);return o==y||o==Z?n.size:Zu(n).length}function DA(n,o,l){var f=Re(n)?Au:_I;return l&&Xt(n,o,l)&&(o=s),f(n,De(o,3))}var OA=He(function(n,o){if(n==null)return[];var l=o.length;return l>1&&Xt(n,o[0],o[1])?o=[]:l>2&&Xt(o[0],o[1],o[2])&&(o=[o[0]]),mm(n,$t(o,1),[])}),Wa=iN||function(){return Bt.Date.now()};function SA(n,o){if(typeof o!="function")throw new Cs(u);return n=Ue(n),function(){if(--n<1)return o.apply(this,arguments)}}function ug(n,o,l){return o=l?s:o,o=n&&o==null?n.length:o,En(n,be,s,s,s,s,o)}function cg(n,o){var l;if(typeof o!="function")throw new Cs(u);return n=Ue(n),function(){return--n>0&&(l=o.apply(this,arguments)),n<=1&&(o=s),l}}var bc=He(function(n,o,l){var f=te;if(l.length){var m=Wn(l,so(bc));f|=X}return En(n,f,o,l,m)}),dg=He(function(n,o,l){var f=te|N;if(l.length){var m=Wn(l,so(dg));f|=X}return En(o,f,n,l,m)});function fg(n,o,l){o=l?s:o;var f=En(n,J,s,s,s,s,s,o);return f.placeholder=fg.placeholder,f}function hg(n,o,l){o=l?s:o;var f=En(n,we,s,s,s,s,s,o);return f.placeholder=hg.placeholder,f}function pg(n,o,l){var f,m,b,E,O,I,B=0,H=!1,z=!1,le=!0;if(typeof n!="function")throw new Cs(u);o=Ts(o)||0,gt(l)&&(H=!!l.leading,z="maxWait"in l,b=z?Mt(Ts(l.maxWait)||0,o):b,le="trailing"in l?!!l.trailing:le);function ve(Et){var Hs=f,Sn=m;return f=m=s,B=Et,E=n.apply(Sn,Hs),E}function Te(Et){return B=Et,O=ii(We,o),H?ve(Et):E}function Be(Et){var Hs=Et-I,Sn=Et-B,Pg=o-Hs;return z?qt(Pg,b-Sn):Pg}function Ne(Et){var Hs=Et-I,Sn=Et-B;return I===s||Hs>=o||Hs<0||z&&Sn>=b}function We(){var Et=Wa();if(Ne(Et))return Ke(Et);O=ii(We,Be(Et))}function Ke(Et){return O=s,le&&f?ve(Et):(f=m=s,E)}function ms(){O!==s&&xm(O),B=0,f=I=m=O=s}function es(){return O===s?E:Ke(Wa())}function gs(){var Et=Wa(),Hs=Ne(Et);if(f=arguments,m=this,I=Et,Hs){if(O===s)return Te(I);if(z)return xm(O),O=ii(We,o),ve(I)}return O===s&&(O=ii(We,o)),E}return gs.cancel=ms,gs.flush=es,gs}var TA=He(function(n,o){return nm(n,1,o)}),NA=He(function(n,o,l){return nm(n,Ts(o)||0,l)});function IA(n){return En(n,ae)}function ja(n,o){if(typeof n!="function"||o!=null&&typeof o!="function")throw new Cs(u);var l=function(){var f=arguments,m=o?o.apply(this,f):f[0],b=l.cache;if(b.has(m))return b.get(m);var E=n.apply(this,f);return l.cache=b.set(m,E)||b,E};return l.cache=new(ja.Cache||yn),l}ja.Cache=yn;function za(n){if(typeof n!="function")throw new Cs(u);return function(){var o=arguments;switch(o.length){case 0:return!n.call(this);case 1:return!n.call(this,o[0]);case 2:return!n.call(this,o[0],o[1]);case 3:return!n.call(this,o[0],o[1],o[2])}return!n.apply(this,o)}}function AA(n){return cg(2,n)}var MA=vI(function(n,o){o=o.length==1&&Re(o[0])?pt(o[0],fs(De())):pt($t(o,1),fs(De()));var l=o.length;return He(function(f){for(var m=-1,b=qt(f.length,l);++m<b;)f[m]=o[m].call(this,f[m]);return ds(n,this,f)})}),yc=He(function(n,o){var l=Wn(o,so(yc));return En(n,X,s,o,l)}),mg=He(function(n,o){var l=Wn(o,so(mg));return En(n,pe,s,o,l)}),PA=Cn(function(n,o){return En(n,Ae,s,s,s,o)});function kA(n,o){if(typeof n!="function")throw new Cs(u);return o=o===s?o:Ue(o),He(n,o)}function VA(n,o){if(typeof n!="function")throw new Cs(u);return o=o==null?0:Mt(Ue(o),0),He(function(l){var f=l[o],m=Zn(l,0,o);return f&&qn(m,f),ds(n,this,m)})}function RA(n,o,l){var f=!0,m=!0;if(typeof n!="function")throw new Cs(u);return gt(l)&&(f="leading"in l?!!l.leading:f,m="trailing"in l?!!l.trailing:m),pg(n,o,{leading:f,maxWait:o,trailing:m})}function FA(n){return ug(n,1)}function LA(n,o){return yc(rc(o),n)}function UA(){if(!arguments.length)return[];var n=arguments[0];return Re(n)?n:[n]}function BA(n){return Ds(n,x)}function $A(n,o){return o=typeof o=="function"?o:s,Ds(n,x,o)}function HA(n){return Ds(n,g|x)}function qA(n,o){return o=typeof o=="function"?o:s,Ds(n,g|x,o)}function WA(n,o){return o==null||sm(n,o,Rt(o))}function $s(n,o){return n===o||n!==n&&o!==o}var jA=La(zu),zA=La(function(n,o){return n>=o}),Dr=lm(function(){return arguments}())?lm:function(n){return vt(n)&&ot.call(n,"callee")&&!Kp.call(n,"callee")},Re=k.isArray,GA=Np?fs(Np):tI;function rs(n){return n!=null&&Ga(n.length)&&!Dn(n)}function wt(n){return vt(n)&&rs(n)}function KA(n){return n===!0||n===!1||vt(n)&&Qt(n)==ie}var Yn=lN||Ac,ZA=Ip?fs(Ip):sI;function YA(n){return vt(n)&&n.nodeType===1&&!ai(n)}function JA(n){if(n==null)return!0;if(rs(n)&&(Re(n)||typeof n=="string"||typeof n.splice=="function"||Yn(n)||no(n)||Dr(n)))return!n.length;var o=Wt(n);if(o==y||o==Z)return!n.size;if(oi(n))return!Zu(n).length;for(var l in n)if(ot.call(n,l))return!1;return!0}function QA(n,o){return si(n,o)}function XA(n,o,l){l=typeof l=="function"?l:s;var f=l?l(n,o):s;return f===s?si(n,o,s,l):!!f}function wc(n){if(!vt(n))return!1;var o=Qt(n);return o==ke||o==ge||typeof n.message=="string"&&typeof n.name=="string"&&!ai(n)}function e8(n){return typeof n=="number"&&Yp(n)}function Dn(n){if(!gt(n))return!1;var o=Qt(n);return o==rt||o==Ve||o==R||o==j}function gg(n){return typeof n=="number"&&n==Ue(n)}function Ga(n){return typeof n=="number"&&n>-1&&n%1==0&&n<=Ce}function gt(n){var o=typeof n;return n!=null&&(o=="object"||o=="function")}function vt(n){return n!=null&&typeof n=="object"}var _g=Ap?fs(Ap):rI;function t8(n,o){return n===o||Ku(n,o,dc(o))}function s8(n,o,l){return l=typeof l=="function"?l:s,Ku(n,o,dc(o),l)}function n8(n){return vg(n)&&n!=+n}function r8(n){if($I(n))throw new Pe(a);return um(n)}function o8(n){return n===null}function i8(n){return n==null}function vg(n){return typeof n=="number"||vt(n)&&Qt(n)==C}function ai(n){if(!vt(n)||Qt(n)!=U)return!1;var o=ya(n);if(o===null)return!0;var l=ot.call(o,"constructor")&&o.constructor;return typeof l=="function"&&l instanceof l&&ga.call(l)==sN}var Ec=Mp?fs(Mp):oI;function a8(n){return gg(n)&&n>=-Ce&&n<=Ce}var bg=Pp?fs(Pp):iI;function Ka(n){return typeof n=="string"||!Re(n)&&vt(n)&&Qt(n)==ee}function ps(n){return typeof n=="symbol"||vt(n)&&Qt(n)==K}var no=kp?fs(kp):aI;function l8(n){return n===s}function u8(n){return vt(n)&&Wt(n)==ne}function c8(n){return vt(n)&&Qt(n)==_e}var d8=La(Yu),f8=La(function(n,o){return n<=o});function yg(n){if(!n)return[];if(rs(n))return Ka(n)?Us(n):ns(n);if(Ko&&n[Ko])return W3(n[Ko]());var o=Wt(n),l=o==y?Fu:o==Z?ha:ro;return l(n)}function On(n){if(!n)return n===0?n:0;if(n=Ts(n),n===de||n===-de){var o=n<0?-1:1;return o*xt}return n===n?n:0}function Ue(n){var o=On(n),l=o%1;return o===o?l?o-l:o:0}function wg(n){return n?wr(Ue(n),0,Nt):0}function Ts(n){if(typeof n=="number")return n;if(ps(n))return vs;if(gt(n)){var o=typeof n.valueOf=="function"?n.valueOf():n;n=gt(o)?o+"":o}if(typeof n!="string")return n===0?n:+n;n=Bp(n);var l=G5.test(n);return l||Z5.test(n)?S3(n.slice(2),l?2:8):z5.test(n)?vs:+n}function Eg(n){return sn(n,os(n))}function h8(n){return n?wr(Ue(n),-Ce,Ce):n===0?n:0}function nt(n){return n==null?"":hs(n)}var p8=eo(function(n,o){if(oi(o)||rs(o)){sn(o,Rt(o),n);return}for(var l in o)ot.call(o,l)&&Xo(n,l,o[l])}),Cg=eo(function(n,o){sn(o,os(o),n)}),Za=eo(function(n,o,l,f){sn(o,os(o),n,f)}),m8=eo(function(n,o,l,f){sn(o,Rt(o),n,f)}),g8=Cn(qu);function _8(n,o){var l=Xr(n);return o==null?l:tm(l,o)}var v8=He(function(n,o){n=ut(n);var l=-1,f=o.length,m=f>2?o[2]:s;for(m&&Xt(o[0],o[1],m)&&(f=1);++l<f;)for(var b=o[l],E=os(b),O=-1,I=E.length;++O<I;){var B=E[O],H=n[B];(H===s||$s(H,Yr[B])&&!ot.call(n,B))&&(n[B]=b[B])}return n}),b8=He(function(n){return n.push(s,Bm),ds(xg,s,n)});function y8(n,o){return Rp(n,De(o,3),tn)}function w8(n,o){return Rp(n,De(o,3),ju)}function E8(n,o){return n==null?n:Wu(n,De(o,3),os)}function C8(n,o){return n==null?n:im(n,De(o,3),os)}function x8(n,o){return n&&tn(n,De(o,3))}function D8(n,o){return n&&ju(n,De(o,3))}function O8(n){return n==null?[]:Ia(n,Rt(n))}function S8(n){return n==null?[]:Ia(n,os(n))}function Cc(n,o,l){var f=n==null?s:Er(n,o);return f===s?l:f}function T8(n,o){return n!=null&&qm(n,o,JN)}function xc(n,o){return n!=null&&qm(n,o,QN)}var N8=Vm(function(n,o,l){o!=null&&typeof o.toString!="function"&&(o=_a.call(o)),n[o]=l},Oc(is)),I8=Vm(function(n,o,l){o!=null&&typeof o.toString!="function"&&(o=_a.call(o)),ot.call(n,o)?n[o].push(l):n[o]=[l]},De),A8=He(ti);function Rt(n){return rs(n)?Xp(n):Zu(n)}function os(n){return rs(n)?Xp(n,!0):lI(n)}function M8(n,o){var l={};return o=De(o,3),tn(n,function(f,m,b){wn(l,o(f,m,b),f)}),l}function P8(n,o){var l={};return o=De(o,3),tn(n,function(f,m,b){wn(l,m,o(f,m,b))}),l}var k8=eo(function(n,o,l){Aa(n,o,l)}),xg=eo(function(n,o,l,f){Aa(n,o,l,f)}),V8=Cn(function(n,o){var l={};if(n==null)return l;var f=!1;o=pt(o,function(b){return b=Kn(b,n),f||(f=b.length>1),b}),sn(n,uc(n),l),f&&(l=Ds(l,g|w|x,NI));for(var m=o.length;m--;)tc(l,o[m]);return l});function R8(n,o){return Dg(n,za(De(o)))}var F8=Cn(function(n,o){return n==null?{}:cI(n,o)});function Dg(n,o){if(n==null)return{};var l=pt(uc(n),function(f){return[f]});return o=De(o),gm(n,l,function(f,m){return o(f,m[0])})}function L8(n,o,l){o=Kn(o,n);var f=-1,m=o.length;for(m||(m=1,n=s);++f<m;){var b=n==null?s:n[nn(o[f])];b===s&&(f=m,b=l),n=Dn(b)?b.call(n):b}return n}function U8(n,o,l){return n==null?n:ni(n,o,l)}function B8(n,o,l,f){return f=typeof f=="function"?f:s,n==null?n:ni(n,o,l,f)}var Og=Lm(Rt),Sg=Lm(os);function $8(n,o,l){var f=Re(n),m=f||Yn(n)||no(n);if(o=De(o,4),l==null){var b=n&&n.constructor;m?l=f?new b:[]:gt(n)?l=Dn(b)?Xr(ya(n)):{}:l={}}return(m?Es:tn)(n,function(E,O,I){return o(l,E,O,I)}),l}function H8(n,o){return n==null?!0:tc(n,o)}function q8(n,o,l){return n==null?n:wm(n,o,rc(l))}function W8(n,o,l,f){return f=typeof f=="function"?f:s,n==null?n:wm(n,o,rc(l),f)}function ro(n){return n==null?[]:Ru(n,Rt(n))}function j8(n){return n==null?[]:Ru(n,os(n))}function z8(n,o,l){return l===s&&(l=o,o=s),l!==s&&(l=Ts(l),l=l===l?l:0),o!==s&&(o=Ts(o),o=o===o?o:0),wr(Ts(n),o,l)}function G8(n,o,l){return o=On(o),l===s?(l=o,o=0):l=On(l),n=Ts(n),XN(n,o,l)}function K8(n,o,l){if(l&&typeof l!="boolean"&&Xt(n,o,l)&&(o=l=s),l===s&&(typeof o=="boolean"?(l=o,o=s):typeof n=="boolean"&&(l=n,n=s)),n===s&&o===s?(n=0,o=1):(n=On(n),o===s?(o=n,n=0):o=On(o)),n>o){var f=n;n=o,o=f}if(l||n%1||o%1){var m=Jp();return qt(n+m*(o-n+O3("1e-"+((m+"").length-1))),o)}return Qu(n,o)}var Z8=to(function(n,o,l){return o=o.toLowerCase(),n+(l?Tg(o):o)});function Tg(n){return Dc(nt(n).toLowerCase())}function Ng(n){return n=nt(n),n&&n.replace(J5,U3).replace(g3,"")}function Y8(n,o,l){n=nt(n),o=hs(o);var f=n.length;l=l===s?f:wr(Ue(l),0,f);var m=l;return l-=o.length,l>=0&&n.slice(l,m)==o}function J8(n){return n=nt(n),n&&A5.test(n)?n.replace(ip,B3):n}function Q8(n){return n=nt(n),n&&F5.test(n)?n.replace(yu,"\\$&"):n}var X8=to(function(n,o,l){return n+(l?"-":"")+o.toLowerCase()}),e6=to(function(n,o,l){return n+(l?" ":"")+o.toLowerCase()}),t6=Mm("toLowerCase");function s6(n,o,l){n=nt(n),o=Ue(o);var f=o?Kr(n):0;if(!o||f>=o)return n;var m=(o-f)/2;return Fa(xa(m),l)+n+Fa(Ca(m),l)}function n6(n,o,l){n=nt(n),o=Ue(o);var f=o?Kr(n):0;return o&&f<o?n+Fa(o-f,l):n}function r6(n,o,l){n=nt(n),o=Ue(o);var f=o?Kr(n):0;return o&&f<o?Fa(o-f,l)+n:n}function o6(n,o,l){return l||o==null?o=0:o&&(o=+o),fN(nt(n).replace(wu,""),o||0)}function i6(n,o,l){return(l?Xt(n,o,l):o===s)?o=1:o=Ue(o),Xu(nt(n),o)}function a6(){var n=arguments,o=nt(n[0]);return n.length<3?o:o.replace(n[1],n[2])}var l6=to(function(n,o,l){return n+(l?"_":"")+o.toLowerCase()});function u6(n,o,l){return l&&typeof l!="number"&&Xt(n,o,l)&&(o=l=s),l=l===s?Nt:l>>>0,l?(n=nt(n),n&&(typeof o=="string"||o!=null&&!Ec(o))&&(o=hs(o),!o&&Gr(n))?Zn(Us(n),0,l):n.split(o,l)):[]}var c6=to(function(n,o,l){return n+(l?" ":"")+Dc(o)});function d6(n,o,l){return n=nt(n),l=l==null?0:wr(Ue(l),0,n.length),o=hs(o),n.slice(l,l+o.length)==o}function f6(n,o,l){var f=v.templateSettings;l&&Xt(n,o,l)&&(o=s),n=nt(n),o=Za({},o,f,Um);var m=Za({},o.imports,f.imports,Um),b=Rt(m),E=Ru(m,b),O,I,B=0,H=o.interpolate||la,z="__p += '",le=Lu((o.escape||la).source+"|"+H.source+"|"+(H===ap?j5:la).source+"|"+(o.evaluate||la).source+"|$","g"),ve="//# sourceURL="+(ot.call(o,"sourceURL")?(o.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++w3+"]")+`
`;n.replace(le,function(Ne,We,Ke,ms,es,gs){return Ke||(Ke=ms),z+=n.slice(B,gs).replace(Q5,$3),We&&(O=!0,z+=`' +
__e(`+We+`) +
'`),es&&(I=!0,z+=`';
`+es+`;
__p += '`),Ke&&(z+=`' +
((__t = (`+Ke+`)) == null ? '' : __t) +
'`),B=gs+Ne.length,Ne}),z+=`';
`;var Te=ot.call(o,"variable")&&o.variable;if(!Te)z=`with (obj) {
`+z+`
}
`;else if(q5.test(Te))throw new Pe(d);z=(I?z.replace(ys,""):z).replace(aa,"$1").replace(N5,"$1;"),z="function("+(Te||"obj")+`) {
`+(Te?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(O?", __e = _.escape":"")+(I?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+z+`return __p
}`;var Be=Ag(function(){return et(b,ve+"return "+z).apply(s,E)});if(Be.source=z,wc(Be))throw Be;return Be}function h6(n){return nt(n).toLowerCase()}function p6(n){return nt(n).toUpperCase()}function m6(n,o,l){if(n=nt(n),n&&(l||o===s))return Bp(n);if(!n||!(o=hs(o)))return n;var f=Us(n),m=Us(o),b=$p(f,m),E=Hp(f,m)+1;return Zn(f,b,E).join("")}function g6(n,o,l){if(n=nt(n),n&&(l||o===s))return n.slice(0,Wp(n)+1);if(!n||!(o=hs(o)))return n;var f=Us(n),m=Hp(f,Us(o))+1;return Zn(f,0,m).join("")}function _6(n,o,l){if(n=nt(n),n&&(l||o===s))return n.replace(wu,"");if(!n||!(o=hs(o)))return n;var f=Us(n),m=$p(f,Us(o));return Zn(f,m).join("")}function v6(n,o){var l=A,f=Ee;if(gt(o)){var m="separator"in o?o.separator:m;l="length"in o?Ue(o.length):l,f="omission"in o?hs(o.omission):f}n=nt(n);var b=n.length;if(Gr(n)){var E=Us(n);b=E.length}if(l>=b)return n;var O=l-Kr(f);if(O<1)return f;var I=E?Zn(E,0,O).join(""):n.slice(0,O);if(m===s)return I+f;if(E&&(O+=I.length-O),Ec(m)){if(n.slice(O).search(m)){var B,H=I;for(m.global||(m=Lu(m.source,nt(lp.exec(m))+"g")),m.lastIndex=0;B=m.exec(H);)var z=B.index;I=I.slice(0,z===s?O:z)}}else if(n.indexOf(hs(m),O)!=O){var le=I.lastIndexOf(m);le>-1&&(I=I.slice(0,le))}return I+f}function b6(n){return n=nt(n),n&&I5.test(n)?n.replace(op,K3):n}var y6=to(function(n,o,l){return n+(l?" ":"")+o.toUpperCase()}),Dc=Mm("toUpperCase");function Ig(n,o,l){return n=nt(n),o=l?s:o,o===s?q3(n)?J3(n):k3(n):n.match(o)||[]}var Ag=He(function(n,o){try{return ds(n,s,o)}catch(l){return wc(l)?l:new Pe(l)}}),w6=Cn(function(n,o){return Es(o,function(l){l=nn(l),wn(n,l,bc(n[l],n))}),n});function E6(n){var o=n==null?0:n.length,l=De();return n=o?pt(n,function(f){if(typeof f[1]!="function")throw new Cs(u);return[l(f[0]),f[1]]}):[],He(function(f){for(var m=-1;++m<o;){var b=n[m];if(ds(b[0],this,f))return ds(b[1],this,f)}})}function C6(n){return KN(Ds(n,g))}function Oc(n){return function(){return n}}function x6(n,o){return n==null||n!==n?o:n}var D6=km(),O6=km(!0);function is(n){return n}function Sc(n){return cm(typeof n=="function"?n:Ds(n,g))}function S6(n){return fm(Ds(n,g))}function T6(n,o){return hm(n,Ds(o,g))}var N6=He(function(n,o){return function(l){return ti(l,n,o)}}),I6=He(function(n,o){return function(l){return ti(n,l,o)}});function Tc(n,o,l){var f=Rt(o),m=Ia(o,f);l==null&&!(gt(o)&&(m.length||!f.length))&&(l=o,o=n,n=this,m=Ia(o,Rt(o)));var b=!(gt(l)&&"chain"in l)||!!l.chain,E=Dn(n);return Es(m,function(O){var I=o[O];n[O]=I,E&&(n.prototype[O]=function(){var B=this.__chain__;if(b||B){var H=n(this.__wrapped__),z=H.__actions__=ns(this.__actions__);return z.push({func:I,args:arguments,thisArg:n}),H.__chain__=B,H}return I.apply(n,qn([this.value()],arguments))})}),n}function A6(){return Bt._===this&&(Bt._=nN),this}function Nc(){}function M6(n){return n=Ue(n),He(function(o){return pm(o,n)})}var P6=ic(pt),k6=ic(Vp),V6=ic(Au);function Mg(n){return hc(n)?Mu(nn(n)):dI(n)}function R6(n){return function(o){return n==null?s:Er(n,o)}}var F6=Rm(),L6=Rm(!0);function Ic(){return[]}function Ac(){return!1}function U6(){return{}}function B6(){return""}function $6(){return!0}function H6(n,o){if(n=Ue(n),n<1||n>Ce)return[];var l=Nt,f=qt(n,Nt);o=De(o),n-=Nt;for(var m=Vu(f,o);++l<n;)o(l);return m}function q6(n){return Re(n)?pt(n,nn):ps(n)?[n]:ns(Qm(nt(n)))}function W6(n){var o=++tN;return nt(n)+o}var j6=Ra(function(n,o){return n+o},0),z6=ac("ceil"),G6=Ra(function(n,o){return n/o},1),K6=ac("floor");function Z6(n){return n&&n.length?Na(n,is,zu):s}function Y6(n,o){return n&&n.length?Na(n,De(o,2),zu):s}function J6(n){return Lp(n,is)}function Q6(n,o){return Lp(n,De(o,2))}function X6(n){return n&&n.length?Na(n,is,Yu):s}function eM(n,o){return n&&n.length?Na(n,De(o,2),Yu):s}var tM=Ra(function(n,o){return n*o},1),sM=ac("round"),nM=Ra(function(n,o){return n-o},0);function rM(n){return n&&n.length?ku(n,is):0}function oM(n,o){return n&&n.length?ku(n,De(o,2)):0}return v.after=SA,v.ary=ug,v.assign=p8,v.assignIn=Cg,v.assignInWith=Za,v.assignWith=m8,v.at=g8,v.before=cg,v.bind=bc,v.bindAll=w6,v.bindKey=dg,v.castArray=UA,v.chain=ig,v.chunk=KI,v.compact=ZI,v.concat=YI,v.cond=E6,v.conforms=C6,v.constant=Oc,v.countBy=rA,v.create=_8,v.curry=fg,v.curryRight=hg,v.debounce=pg,v.defaults=v8,v.defaultsDeep=b8,v.defer=TA,v.delay=NA,v.difference=JI,v.differenceBy=QI,v.differenceWith=XI,v.drop=e4,v.dropRight=t4,v.dropRightWhile=s4,v.dropWhile=n4,v.fill=r4,v.filter=iA,v.flatMap=uA,v.flatMapDeep=cA,v.flatMapDepth=dA,v.flatten=sg,v.flattenDeep=o4,v.flattenDepth=i4,v.flip=IA,v.flow=D6,v.flowRight=O6,v.fromPairs=a4,v.functions=O8,v.functionsIn=S8,v.groupBy=fA,v.initial=u4,v.intersection=c4,v.intersectionBy=d4,v.intersectionWith=f4,v.invert=N8,v.invertBy=I8,v.invokeMap=pA,v.iteratee=Sc,v.keyBy=mA,v.keys=Rt,v.keysIn=os,v.map=qa,v.mapKeys=M8,v.mapValues=P8,v.matches=S6,v.matchesProperty=T6,v.memoize=ja,v.merge=k8,v.mergeWith=xg,v.method=N6,v.methodOf=I6,v.mixin=Tc,v.negate=za,v.nthArg=M6,v.omit=V8,v.omitBy=R8,v.once=AA,v.orderBy=gA,v.over=P6,v.overArgs=MA,v.overEvery=k6,v.overSome=V6,v.partial=yc,v.partialRight=mg,v.partition=_A,v.pick=F8,v.pickBy=Dg,v.property=Mg,v.propertyOf=R6,v.pull=g4,v.pullAll=rg,v.pullAllBy=_4,v.pullAllWith=v4,v.pullAt=b4,v.range=F6,v.rangeRight=L6,v.rearg=PA,v.reject=yA,v.remove=y4,v.rest=kA,v.reverse=_c,v.sampleSize=EA,v.set=U8,v.setWith=B8,v.shuffle=CA,v.slice=w4,v.sortBy=OA,v.sortedUniq=T4,v.sortedUniqBy=N4,v.split=u6,v.spread=VA,v.tail=I4,v.take=A4,v.takeRight=M4,v.takeRightWhile=P4,v.takeWhile=k4,v.tap=Z4,v.throttle=RA,v.thru=Ha,v.toArray=yg,v.toPairs=Og,v.toPairsIn=Sg,v.toPath=q6,v.toPlainObject=Eg,v.transform=$8,v.unary=FA,v.union=V4,v.unionBy=R4,v.unionWith=F4,v.uniq=L4,v.uniqBy=U4,v.uniqWith=B4,v.unset=H8,v.unzip=vc,v.unzipWith=og,v.update=q8,v.updateWith=W8,v.values=ro,v.valuesIn=j8,v.without=$4,v.words=Ig,v.wrap=LA,v.xor=H4,v.xorBy=q4,v.xorWith=W4,v.zip=j4,v.zipObject=z4,v.zipObjectDeep=G4,v.zipWith=K4,v.entries=Og,v.entriesIn=Sg,v.extend=Cg,v.extendWith=Za,Tc(v,v),v.add=j6,v.attempt=Ag,v.camelCase=Z8,v.capitalize=Tg,v.ceil=z6,v.clamp=z8,v.clone=BA,v.cloneDeep=HA,v.cloneDeepWith=qA,v.cloneWith=$A,v.conformsTo=WA,v.deburr=Ng,v.defaultTo=x6,v.divide=G6,v.endsWith=Y8,v.eq=$s,v.escape=J8,v.escapeRegExp=Q8,v.every=oA,v.find=aA,v.findIndex=eg,v.findKey=y8,v.findLast=lA,v.findLastIndex=tg,v.findLastKey=w8,v.floor=K6,v.forEach=ag,v.forEachRight=lg,v.forIn=E8,v.forInRight=C8,v.forOwn=x8,v.forOwnRight=D8,v.get=Cc,v.gt=jA,v.gte=zA,v.has=T8,v.hasIn=xc,v.head=ng,v.identity=is,v.includes=hA,v.indexOf=l4,v.inRange=G8,v.invoke=A8,v.isArguments=Dr,v.isArray=Re,v.isArrayBuffer=GA,v.isArrayLike=rs,v.isArrayLikeObject=wt,v.isBoolean=KA,v.isBuffer=Yn,v.isDate=ZA,v.isElement=YA,v.isEmpty=JA,v.isEqual=QA,v.isEqualWith=XA,v.isError=wc,v.isFinite=e8,v.isFunction=Dn,v.isInteger=gg,v.isLength=Ga,v.isMap=_g,v.isMatch=t8,v.isMatchWith=s8,v.isNaN=n8,v.isNative=r8,v.isNil=i8,v.isNull=o8,v.isNumber=vg,v.isObject=gt,v.isObjectLike=vt,v.isPlainObject=ai,v.isRegExp=Ec,v.isSafeInteger=a8,v.isSet=bg,v.isString=Ka,v.isSymbol=ps,v.isTypedArray=no,v.isUndefined=l8,v.isWeakMap=u8,v.isWeakSet=c8,v.join=h4,v.kebabCase=X8,v.last=Ss,v.lastIndexOf=p4,v.lowerCase=e6,v.lowerFirst=t6,v.lt=d8,v.lte=f8,v.max=Z6,v.maxBy=Y6,v.mean=J6,v.meanBy=Q6,v.min=X6,v.minBy=eM,v.stubArray=Ic,v.stubFalse=Ac,v.stubObject=U6,v.stubString=B6,v.stubTrue=$6,v.multiply=tM,v.nth=m4,v.noConflict=A6,v.noop=Nc,v.now=Wa,v.pad=s6,v.padEnd=n6,v.padStart=r6,v.parseInt=o6,v.random=K8,v.reduce=vA,v.reduceRight=bA,v.repeat=i6,v.replace=a6,v.result=L8,v.round=sM,v.runInContext=T,v.sample=wA,v.size=xA,v.snakeCase=l6,v.some=DA,v.sortedIndex=E4,v.sortedIndexBy=C4,v.sortedIndexOf=x4,v.sortedLastIndex=D4,v.sortedLastIndexBy=O4,v.sortedLastIndexOf=S4,v.startCase=c6,v.startsWith=d6,v.subtract=nM,v.sum=rM,v.sumBy=oM,v.template=f6,v.times=H6,v.toFinite=On,v.toInteger=Ue,v.toLength=wg,v.toLower=h6,v.toNumber=Ts,v.toSafeInteger=h8,v.toString=nt,v.toUpper=p6,v.trim=m6,v.trimEnd=g6,v.trimStart=_6,v.truncate=v6,v.unescape=b6,v.uniqueId=W6,v.upperCase=y6,v.upperFirst=Dc,v.each=ag,v.eachRight=lg,v.first=ng,Tc(v,function(){var n={};return tn(v,function(o,l){ot.call(v.prototype,l)||(n[l]=o)}),n}(),{chain:!1}),v.VERSION=i,Es(["bind","bindKey","curry","curryRight","partial","partialRight"],function(n){v[n].placeholder=v}),Es(["drop","take"],function(n,o){ze.prototype[n]=function(l){l=l===s?1:Mt(Ue(l),0);var f=this.__filtered__&&!o?new ze(this):this.clone();return f.__filtered__?f.__takeCount__=qt(l,f.__takeCount__):f.__views__.push({size:qt(l,Nt),type:n+(f.__dir__<0?"Right":"")}),f},ze.prototype[n+"Right"]=function(l){return this.reverse()[n](l).reverse()}}),Es(["filter","map","takeWhile"],function(n,o){var l=o+1,f=l==mt||l==it;ze.prototype[n]=function(m){var b=this.clone();return b.__iteratees__.push({iteratee:De(m,3),type:l}),b.__filtered__=b.__filtered__||f,b}}),Es(["head","last"],function(n,o){var l="take"+(o?"Right":"");ze.prototype[n]=function(){return this[l](1).value()[0]}}),Es(["initial","tail"],function(n,o){var l="drop"+(o?"":"Right");ze.prototype[n]=function(){return this.__filtered__?new ze(this):this[l](1)}}),ze.prototype.compact=function(){return this.filter(is)},ze.prototype.find=function(n){return this.filter(n).head()},ze.prototype.findLast=function(n){return this.reverse().find(n)},ze.prototype.invokeMap=He(function(n,o){return typeof n=="function"?new ze(this):this.map(function(l){return ti(l,n,o)})}),ze.prototype.reject=function(n){return this.filter(za(De(n)))},ze.prototype.slice=function(n,o){n=Ue(n);var l=this;return l.__filtered__&&(n>0||o<0)?new ze(l):(n<0?l=l.takeRight(-n):n&&(l=l.drop(n)),o!==s&&(o=Ue(o),l=o<0?l.dropRight(-o):l.take(o-n)),l)},ze.prototype.takeRightWhile=function(n){return this.reverse().takeWhile(n).reverse()},ze.prototype.toArray=function(){return this.take(Nt)},tn(ze.prototype,function(n,o){var l=/^(?:filter|find|map|reject)|While$/.test(o),f=/^(?:head|last)$/.test(o),m=v[f?"take"+(o=="last"?"Right":""):o],b=f||/^find/.test(o);m&&(v.prototype[o]=function(){var E=this.__wrapped__,O=f?[1]:arguments,I=E instanceof ze,B=O[0],H=I||Re(E),z=function(We){var Ke=m.apply(v,qn([We],O));return f&&le?Ke[0]:Ke};H&&l&&typeof B=="function"&&B.length!=1&&(I=H=!1);var le=this.__chain__,ve=!!this.__actions__.length,Te=b&&!le,Be=I&&!ve;if(!b&&H){E=Be?E:new ze(this);var Ne=n.apply(E,O);return Ne.__actions__.push({func:Ha,args:[z],thisArg:s}),new xs(Ne,le)}return Te&&Be?n.apply(this,O):(Ne=this.thru(z),Te?f?Ne.value()[0]:Ne.value():Ne)})}),Es(["pop","push","shift","sort","splice","unshift"],function(n){var o=pa[n],l=/^(?:push|sort|unshift)$/.test(n)?"tap":"thru",f=/^(?:pop|shift)$/.test(n);v.prototype[n]=function(){var m=arguments;if(f&&!this.__chain__){var b=this.value();return o.apply(Re(b)?b:[],m)}return this[l](function(E){return o.apply(Re(E)?E:[],m)})}}),tn(ze.prototype,function(n,o){var l=v[o];if(l){var f=l.name+"";ot.call(Qr,f)||(Qr[f]=[]),Qr[f].push({name:o,func:l})}}),Qr[Va(s,N).name]=[{name:"wrapper",func:s}],ze.prototype.clone=bN,ze.prototype.reverse=yN,ze.prototype.value=wN,v.prototype.at=Y4,v.prototype.chain=J4,v.prototype.commit=Q4,v.prototype.next=X4,v.prototype.plant=tA,v.prototype.reverse=sA,v.prototype.toJSON=v.prototype.valueOf=v.prototype.value=nA,v.prototype.first=v.prototype.head,Ko&&(v.prototype[Ko]=eA),v},Zr=Q3();_r?((_r.exports=Zr)._=Zr,Su._=Zr):Bt._=Zr}).call(Ho)}(ea,ea.exports);var Qh=ea.exports;const je=async(e,t)=>{const s={methodname:e,args:Object.assign({},t)};try{return await Vg.call([s])[0]}catch(i){throw Rg.exception(i),i}};async function Zb(e={}){try{return await je("local_offermanager_fetch",{search_string:e.search||"",type:e.type||null,only_active:e.onlyActive===!0,page:e.page||1,per_page:e.perPage||25,sort_by:e.sortBy||"name",sort_direction:e.sortDesc?"DESC":"ASC"})}catch(t){throw t}}async function Yb(e){try{return await je("local_offermanager_get",{id:e})}catch(t){throw t}}async function Xh(e){try{return await je("local_offermanager_save",{id:e.id||0,name:e.name,description:e.description||"",type:e.type||"",status:e.status||0,audienceids:e.audiences||[]})}catch(t){throw t}}async function Jb(e){try{return await je("local_offermanager_delete",{id:e})}catch(t){throw t}}async function Qb(){try{return await je("local_offermanager_get_type_options",{})}catch(e){throw e}}async function Xb(e,t){try{return await je("local_offermanager_delete_course",{offercourseid:t})}catch(s){throw s}}async function ey(e,t,s){try{return await je("local_offermanager_set_course_status",{id:t,status:s?1:0})}catch(i){throw i}}async function ty(e){try{return(await je("local_offermanager_get_audiences",{offerid:0})).all_audiences.filter(i=>i.name.toLowerCase().includes(e.toLowerCase())).map(i=>({id:i.id,name:i.name}))}catch(t){throw t}}async function sy(e,t){try{return await je("local_offermanager_set_status",{id:e,status:!t})}catch(s){throw s}}async function hu(e="",t=0){try{return await je("local_offermanager_get_categories",{search_string:e,offerid:t})}catch(s){throw s}}async function ny(e,t,s="",i=1,r=20){try{return await je("local_offermanager_fetch_potential_courses",{offerid:e,categoryid:t,search_string:s||"",page:i,per_page:r,exclude_courseids:[]})}catch(a){throw a}}async function ry(e,t,s="",i=[],r=!1){try{return await je("local_offermanager_fetch_current_courses",{offerid:e,categoryid:t,search_string:s,exclude_courseids:i||[],only_active:r})}catch(a){throw a}}async function oy(e,t){try{return await je("local_offermanager_add_courses",{offerid:e,courseids:t})}catch(s){throw s}}async function ep(e,t={}){try{return t.sortBy==="name"&&(t.sortBy="fullname"),t.sortBy==="courseClassCount"&&(t.sortBy="class_counter"),await je("local_offermanager_get_current_courses",{offerid:e,only_active:t.onlyActive||!1,courseids:t.courseIds||[],page:t.page||1,per_page:t.perPage||100,sort_by:t.sortBy||"id",sort_direction:t.sortDesc?"DESC":"ASC",course_search:t.courseSearch||"",category_search:t.categorySearch||""})}catch(s){throw s}}async function iy(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={optional_fields:{}};s.classname=e.classname,s.startdate=e.startdate,s.offercourseid=parseInt(e.offercourseid),s.teachers=[...e.teachers],s.enrol=e.enrol,e.optional_fields&&t.forEach(u=>{if(u in e.optional_fields){const d=e.optional_fields[u];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(u)?d!==0&&d!==null&&d!==void 0&&d!==""&&(s.optional_fields[u]=d):typeof d=="boolean"?s.optional_fields[u]=d:Array.isArray(d)?d.length>0&&(s.optional_fields[u]=d):d!=null&&d!==""&&(s.optional_fields[u]=d)}});const r=["offercourseid","classname","startdate","enrol"].filter(u=>!s[u]);if(r.length>0)throw console.error("Campos obrigatórios ausentes no serviço:",r),new Error(`Campos obrigatórios ausentes: ${r.join(", ")}`);return await je("local_offermanager_add_class",s)}catch(t){throw console.error("Erro ao criar turma:",t),t}}async function pu(e){try{return await je("local_offermanager_get_class",{offerclassid:e})}catch(t){throw t}}async function ay(e){try{return await je("local_offermanager_get_course",{offercourseid:e})}catch(t){throw t}}async function ly(e){try{return await je("local_offermanager_get_classes",{offercourseid:e})}catch(t){throw console.error("Error fetching:",t),t}}async function uy(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={offerclassid:e.offerclassid,classname:e.classname,startdate:e.startdate,teachers:e.teachers,optional_fields:{}};return e.optional_fields&&t.forEach(r=>{if(r in e.optional_fields){const a=e.optional_fields[r];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(r)?a!==0&&a!==null&&a!==void 0&&a!==""&&(s.optional_fields[r]=a):typeof a=="boolean"?s.optional_fields[r]=a:Array.isArray(a)?a.length>0&&(s.optional_fields[r]=a):a!=null&&a!==""&&(s.optional_fields[r]=a)}}),console.log("Campos enviados para a API de atualização:",Object.keys(s.optional_fields)),console.log("Objeto completo enviado para a API de atualização:",s),"enrol"in s&&delete s.enrol,await je("local_offermanager_update_class",s)}catch(t){throw t}}async function cy(e){try{return await je("local_offermanager_delete_class",{offerclassid:e})}catch(t){throw t}}async function dy(e,t=0,s="",i=[]){try{return await je("local_offermanager_get_potential_teachers",{offercourseid:e,search_string:s,offerclassid:t,excluded_userids:i})}catch(r){throw r}}async function fy(){try{return await je("local_offermanager_get_situation_list",{})}catch(e){throw e}}async function hy(e,t){try{if(!t)throw new Error("É necessário especificar um curso de destino para duplicar a turma");const s=parseInt(e,10),i=parseInt(t,10);if(isNaN(s)||isNaN(i))throw new Error("IDs inválidos para duplicação de turma");return await je("local_offermanager_duplicate_class",{offerclassid:s,targetoffercourseid:i})}catch(s){throw s}}async function py(e){try{return await je("local_offermanager_get_duplication_courses",{offerclassid:e})}catch(t){throw t}}async function mu(e){try{return await je("local_offermanager_get_course_roles",{offercourseid:e})}catch(t){throw t}}async function tp(e=!0){try{return await je("local_offermanager_get_class_methods",{enabled:e})}catch(t){throw t}}async function my(e,t){try{return await je("local_offermanager_set_class_status",{id:e,status:t?1:0})}catch(s){throw s}}const cM="",gy={name:"CustomTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})}}},_y={class:"table-responsive"},vy={class:"table"},by=["data-value"],yy=["onClick"],wy=["data-column"];function Ey(e,t,s,i,r,a){return D(),S("div",_y,[c("table",vy,[c("thead",null,[c("tr",null,[(D(!0),S(Ie,null,lt(s.headers,u=>(D(),S("th",{key:u.value,class:fe({"text-right":u.align==="right"}),style:as(u.width?{width:u.width}:{}),"data-value":u.value},[u.value==="select"?Lt(e.$slots,"header-select",{key:0},()=>[qe(W(u.text),1)],!0):(D(),S(Ie,{key:1},[qe(W(u.text)+" ",1),u.sortable?(D(),S("span",{key:0,onClick:d=>u.sortable?a.handleSort(u.value):null,class:"sort-icon"},[c("i",{class:fe(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)],8,yy)):Q("",!0)],64))],14,by))),128))])]),c("tbody",null,[(D(!0),S(Ie,null,lt(s.items,u=>(D(),S("tr",{key:u.id},[(D(!0),S(Ie,null,lt(s.headers,d=>(D(),S("td",{key:d.value,class:fe({"text-right":d.align==="right"}),"data-column":d.value},[Lt(e.$slots,"item-"+d.value,{item:u},()=>[qe(W(u[d.value]),1)],!0)],10,wy))),128))]))),128))])])])}const mr=Fe(gy,[["render",Ey],["__scopeId","data-v-4ad20657"]]),dM="",Cy={name:"CustomSelect",props:{modelValue:{type:[Number,String],default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},xy={class:"select-wrapper"},Dy=["value","disabled"],Oy=["value"],Sy={key:1,class:"error-message"};function Ty(e,t,s,i,r,a){return D(),S("div",{ref:"selectContainer",class:"custom-select-container",style:as(a.customWidth)},[s.label?(D(),S("div",{key:0,class:fe(["select-label",{disabled:s.disabled}])},W(s.label),3)):Q("",!0),c("div",xy,[c("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:fe(["custom-select",{error:s.hasError}]),disabled:s.disabled},[(D(!0),S(Ie,null,lt(s.options,u=>(D(),S("option",{key:u.value,value:u.value},W(u.label),9,Oy))),128))],42,Dy),c("div",{class:fe(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(D(),S("div",Sy,W(s.errorMessage),1)):Q("",!0)],4)}const Xs=Fe(Cy,[["render",Ty],["__scopeId","data-v-bbc06e80"]]),fM="",Ny={name:"CustomInput",props:{modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite aqui..."},type:{type:String,default:"text"},hasSearchIcon:{type:Boolean,default:!1},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1},max:{type:[String,Number],default:null}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}},isDateType(){return this.type==="date"},isNumberType(){return this.type==="number"}},methods:{handleInput(e){let t=e.target.value;if(this.isNumberType&&(t.includes("-")&&(t=t.replace(/-/g,""),e.target.value=t),t!=="")){const s=parseFloat(t);s<0||isNaN(s)?(t="",e.target.value=t):this.max!==null&&s>parseFloat(this.max)&&(t=this.max.toString(),e.target.value=t,this.$emit("validate"))}this.$emit("update:modelValue",t),this.hasError&&t&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},Iy={key:0,class:"input-label"},Ay=["type","placeholder","value","disabled","min","max"],My={key:0,class:"search-icon"},Py={key:2,class:"error-message"};function ky(e,t,s,i,r,a){return D(),S("div",{class:"custom-input-container",style:as(a.customWidth)},[s.label?(D(),S("div",Iy,W(s.label),1)):Q("",!0),c("div",{class:fe(["input-wrapper",{"with-icon":s.hasSearchIcon||a.isDateType}])},[c("input",{type:s.type,placeholder:s.placeholder,value:s.modelValue,onInput:t[0]||(t[0]=(...u)=>a.handleInput&&a.handleInput(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),disabled:s.disabled,class:fe(["form-control custom-input",{error:s.hasError}]),min:a.isNumberType?0:null,max:s.max},null,42,Ay),s.hasSearchIcon?(D(),S("div",My,t[2]||(t[2]=[c("i",{class:"fas fa-search"},null,-1)]))):Q("",!0),a.isDateType?(D(),S("div",{key:1,class:fe(["calendar-icon",{disabled:s.disabled}])},t[3]||(t[3]=[c("i",{class:"fas fa-calendar-alt"},null,-1)]),2)):Q("",!0),s.hasError&&s.errorMessage?(D(),S("div",Py,W(s.errorMessage),1)):Q("",!0)],2)],4)}const $r=Fe(Ny,[["render",ky],["__scopeId","data-v-3ce6392d"]]),hM="",Vy={name:"CustomCheckbox",props:{modelValue:{type:Boolean,default:!1},label:{type:String,default:""},id:{type:String,required:!0},disabled:{type:Boolean,default:!1},confirmBeforeChange:{type:Boolean,default:!1}},emits:["update:modelValue","request-change"],methods:{handleClick(e){const t=!this.modelValue;this.confirmBeforeChange?this.$emit("request-change",t):this.$emit("update:modelValue",t)}}},Ry=["id","checked","disabled"],Fy=["for"];function Ly(e,t,s,i,r,a){return D(),S("div",{class:fe(["checkbox-container",{disabled:s.disabled}])},[(D(),S("input",{type:"checkbox",id:s.id,key:s.modelValue,checked:s.modelValue,onClick:t[0]||(t[0]=Pt((...u)=>a.handleClick&&a.handleClick(...u),["prevent"])),class:"custom-checkbox",disabled:s.disabled},null,8,Ry)),c("label",{for:s.id,class:fe(["checkbox-label",{disabled:s.disabled}])},[Lt(e.$slots,"default",{},()=>[qe(W(s.label),1)],!0)],10,Fy)],2)}const qo=Fe(Vy,[["render",Ly],["__scopeId","data-v-0b9d008f"]]),pM="",Uy={name:"CustomButton",props:{variant:{type:String,default:"primary",validator:e=>["primary","secondary","success","danger","warning","info"].includes(e)},label:{type:String,default:""},icon:{type:String,default:""},disabled:{type:Boolean,default:!1},isLoading:{type:Boolean,default:!1}},emits:["click"]},By=["disabled"],$y={key:1,class:"spinner-border spinner-border-sm"},Hy={key:2};function qy(e,t,s,i,r,a){return D(),S("button",{class:fe(["btn custom-button",[`btn-${s.variant}`]]),disabled:s.disabled||s.isLoading,onClick:t[0]||(t[0]=u=>e.$emit("click",u))},[s.icon?(D(),S("i",{key:0,class:fe(s.icon)},null,2)):Q("",!0),s.isLoading?(D(),S("i",$y)):Q("",!0),s.label?(D(),S("span",Hy,W(s.label),1)):Q("",!0),Lt(e.$slots,"default",{},void 0,!0)],10,By)}const _n=Fe(Uy,[["render",qy],["__scopeId","data-v-482c6327"]]),mM="",Wy={name:"FilterSection",props:{title:{type:String,default:"FILTRO"},hasActiveTags:{type:Boolean,default:!1}}},jy={class:"filter-section"},zy={key:0},Gy={class:"filter-content"},Ky={key:1,class:"filter-tags"};function Zy(e,t,s,i,r,a){return D(),S("div",jy,[s.title?(D(),S("h2",zy,W(s.title),1)):Q("",!0),c("div",Gy,[Lt(e.$slots,"default",{},void 0,!0)]),s.hasActiveTags?(D(),S("div",Ky,[Lt(e.$slots,"tags",{},void 0,!0)])):Q("",!0)])}const sp=Fe(Wy,[["render",Zy],["__scopeId","data-v-1ece8e84"]]),gM="",Yy={name:"FilterRow",props:{inline:{type:Boolean,default:!1}}};function Jy(e,t,s,i,r,a){return D(),S("div",{class:fe(["filter-row",{"filter-row-inline":s.inline}])},[Lt(e.$slots,"default",{},void 0,!0)],2)}const ta=Fe(Yy,[["render",Jy],["__scopeId","data-v-83bdb425"]]),_M="",Qy={name:"FilterGroup",props:{label:{type:String,default:""},isCheckbox:{type:Boolean,default:!1}}},Xy={key:0,class:"filter-label"},ew={class:"filter-input"};function tw(e,t,s,i,r,a){return D(),S("div",{class:fe(["filter-group",{"checkbox-group":s.isCheckbox}])},[s.label?(D(),S("div",Xy,W(s.label),1)):Q("",!0),c("div",ew,[Lt(e.$slots,"default",{},void 0,!0)])],2)}const sa=Fe(Qy,[["render",tw],["__scopeId","data-v-d7bf1926"]]),vM="",sw={name:"FilterActions"},nw={class:"filter-actions"};function rw(e,t,s,i,r,a){return D(),S("div",nw,[Lt(e.$slots,"default",{},void 0,!0)])}const np=Fe(sw,[["render",rw],["__scopeId","data-v-68346c90"]]),bM="",ow={name:"LFLoading",props:{isLoading:{type:Boolean,default:!1}}},iw={key:0};function aw(e,t,s,i,r,a){return D(),_t(Vf,null,{default:Se(()=>[s.isLoading?(D(),S("div",iw,t[0]||(t[0]=[c("div",{class:"modal-overlay"},null,-1),c("div",{class:"loader-wrapper"},[c("span",{class:"loader",role:"status"},[c("span",{class:"sr-only"},"Carregando...")])],-1)]))):Q("",!0)]),_:1})}const Wo=Fe(ow,[["render",aw],["__scopeId","data-v-b3cb5b4c"]]),yM="",lw={name:"Toast",props:{show:{type:Boolean,required:!0},message:{type:String,required:!0},type:{type:String,default:"success",validator:function(e){return["success","error","warning","info"].includes(e)}},duration:{type:Number,default:3e3}},computed:{icon(){return{success:"fas fa-check-circle",error:"fas fa-exclamation-circle",warning:"fas fa-exclamation-triangle",info:"fas fa-info-circle"}[this.type]},progressStyle(){return{animation:`progress ${this.duration}ms linear`}}}},uw={class:"toast-content"};function cw(e,t,s,i,r,a){return D(),_t(J1,{to:"body"},[M(Vf,{name:"toast"},{default:Se(()=>[s.show?(D(),S("div",{key:0,class:fe(["toast",s.type])},[c("div",uw,[c("i",{class:fe(a.icon)},null,2),c("span",null,W(s.message),1)]),c("div",{class:"toast-progress",style:as(a.progressStyle)},null,4)],2)):Q("",!0)]),_:1})])}const $n=Fe(lw,[["render",cw],["__scopeId","data-v-4440998c"]]),wM="",dw={name:"Pagination",props:{currentPage:{type:Number,required:!0},perPage:{type:Number,required:!0},total:{type:Number,required:!0},perPageOptions:{type:Array,default:()=>[5,10,25,50,100]}},emits:["update:currentPage","update:perPage"],computed:{totalPages(){return Math.ceil(this.total/this.perPage)},visiblePages(){const t=Math.floor(2.5);let s=Math.max(1,this.currentPage-t),i=Math.min(this.totalPages,s+5-1);i-s+1<5&&(s=Math.max(1,i-5+1));const r=[];for(let a=s;a<=i;a++)r.push(a);return r},from(){return this.total===0?0:(this.currentPage-1)*this.perPage+1},to(){return Math.min(this.from+this.perPage-1,this.total)},perPageModel:{get(){return this.perPage},set(e){this.$emit("update:perPage",e)}}},methods:{handlePageChange(e){e>=1&&e<=this.totalPages&&this.$emit("update:currentPage",e)},handlePerPageChange(){this.$emit("update:currentPage",1)}}},fw={class:"pagination-container mt-3"},hw={class:"pagination-info"},pw=["value"],mw={class:"pagination-text"},gw={class:"pagination-controls"},_w=["disabled"],vw=["onClick"],bw=["disabled"];function yw(e,t,s,i,r,a){return D(),S("div",fw,[c("div",hw,[at(c("select",{"onUpdate:modelValue":t[0]||(t[0]=u=>a.perPageModel=u),class:"per-page-select",onChange:t[1]||(t[1]=(...u)=>a.handlePerPageChange&&a.handlePerPageChange(...u))},[(D(!0),S(Ie,null,lt(s.perPageOptions,u=>(D(),S("option",{key:u,value:u},W(u),9,pw))),128))],544),[[Jl,a.perPageModel]]),c("span",mw," Mostrando de "+W(a.from)+" até "+W(a.to)+" de "+W(s.total)+" resultados ",1)]),c("div",gw,[c("button",{class:"page-item",disabled:s.currentPage<=1,onClick:t[2]||(t[2]=u=>a.handlePageChange(s.currentPage-1))},t[4]||(t[4]=[c("i",{class:"fas fa-chevron-left"},null,-1)]),8,_w),(D(!0),S(Ie,null,lt(a.visiblePages,u=>(D(),S("button",{key:u,class:fe(["page-item",{active:u===s.currentPage}]),onClick:d=>a.handlePageChange(u)},W(u),11,vw))),128)),c("button",{class:"page-item",disabled:s.currentPage>=a.totalPages,onClick:t[3]||(t[3]=u=>a.handlePageChange(s.currentPage+1))},t[5]||(t[5]=[c("i",{class:"fas fa-chevron-right"},null,-1)]),8,bw)])])}const gr=Fe(dw,[["render",yw],["__scopeId","data-v-b3aa038d"]]),EM="",ww={name:"PageHeader",props:{title:{type:String,required:!0}}},Ew={class:"page-header"},Cw={class:"header-actions"};function xw(e,t,s,i,r,a){return D(),S("div",Ew,[c("h2",null,W(s.title),1),c("div",Cw,[Lt(e.$slots,"actions",{},void 0,!0)])])}const Hr=Fe(ww,[["render",xw],["__scopeId","data-v-70ecc472"]]),CM="",Dw={name:"Modal",components:{CustomButton:_n},props:{show:{type:Boolean,default:!1},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},showDefaultFooter:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1}},emits:["close","confirm"],mounted(){document.addEventListener("keydown",this.handleKeyDown),this.show&&(document.body.style.overflow="hidden")},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.body.style.overflow=""},watch:{show(e){document.body.style.overflow=e?"hidden":""}},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")}}},Ow={class:"modal-body"},Sw={key:0,class:"modal-footer"},Tw={key:1,class:"modal-footer"};function Nw(e,t,s,i,r,a){const u=G("custom-button");return s.show?(D(),S("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=d=>s.closeOnBackdrop?e.$emit("close"):null)},[c("div",{class:fe(["modal-container",[`modal-${s.size}`]]),onClick:t[2]||(t[2]=Pt(()=>{},["stop"]))},[c("div",Ow,[Lt(e.$slots,"default",{},void 0,!0)]),e.$slots.footer?(D(),S("div",Sw,[Lt(e.$slots,"footer",{},void 0,!0)])):s.showDefaultFooter?(D(),S("div",Tw,[M(u,{variant:"secondary",label:s.cancelButtonText,onClick:t[0]||(t[0]=d=>e.$emit("close"))},null,8,["label"]),M(u,{variant:"primary",label:s.confirmButtonText,onClick:t[1]||(t[1]=d=>e.$emit("confirm")),disabled:s.confirmDisabled},null,8,["label","disabled"])])):Q("",!0)],2)])):Q("",!0)}const Iw=Fe(Dw,[["render",Nw],["__scopeId","data-v-784205f2"]]),xM="",Aw={name:"ConfirmationModal",components:{Modal:Iw},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Confirmação"},message:{type:String,default:""},listTitle:{type:String,default:""},listItems:{type:Array,default:()=>[]},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1},icon:{type:String,default:"warning",validator:e=>["warning","info","error","success","question",""].includes(e)},size:{type:String,default:"sm"}},emits:["close","confirm"],computed:{iconClass(){return{warning:"fas fa-exclamation-triangle text-warning",info:"fas fa-info-circle text-info",error:"fas fa-times-circle text-danger",success:"fas fa-check-circle text-success",question:"fas fa-question-circle text-primary"}[this.icon]||""},hasListContent(){return this.listItems&&this.listItems.length>0}}},Mw={key:0,class:"icon-container"},Pw={class:"modal-custom-title"},kw={key:1,class:"message-list"},Vw={key:0,class:"list-title"},Rw={key:2,class:"message"},Fw={class:"modal-custom-footer"},Lw=["disabled"];function Uw(e,t,s,i,r,a){const u=G("modal");return D(),_t(u,{show:s.show,"confirm-button-text":s.confirmButtonText,"cancel-button-text":s.cancelButtonText,"confirm-disabled":s.confirmDisabled,size:s.size,"show-default-footer":!1,onClose:t[2]||(t[2]=d=>e.$emit("close")),onConfirm:t[3]||(t[3]=d=>e.$emit("confirm"))},{default:Se(()=>[c("div",{class:fe(["confirmation-content",{"has-list":a.hasListContent}])},[s.icon?(D(),S("div",Mw,[c("i",{class:fe(a.iconClass)},null,2)])):Q("",!0),c("h3",Pw,W(s.title),1),a.hasListContent?(D(),S("div",kw,[s.listTitle?(D(),S("p",Vw,W(s.listTitle),1)):Q("",!0),c("ul",null,[(D(!0),S(Ie,null,lt(s.listItems,(d,h)=>(D(),S("li",{key:h},W(d),1))),128))])])):(D(),S("div",Rw,W(s.message),1)),c("div",Fw,[c("button",{class:"btn-cancel",onClick:t[0]||(t[0]=d=>e.$emit("close"))},W(s.cancelButtonText),1),c("button",{class:"btn-danger",disabled:s.confirmDisabled,onClick:t[1]||(t[1]=d=>e.$emit("confirm"))},W(s.confirmButtonText),9,Lw)])],2)]),_:1},8,["show","confirm-button-text","cancel-button-text","confirm-disabled","size"])}const na=Fe(Aw,[["render",Uw],["__scopeId","data-v-2e1eb4fd"]]),DM="",OM="",Bw={name:"OfferList",components:{CustomTable:mr,CustomSelect:Xs,CustomInput:$r,CustomCheckbox:qo,CustomButton:_n,FilterSection:sp,FilterRow:ta,FilterGroup:sa,FilterActions:np,Pagination:gr,PageHeader:Hr,ConfirmationModal:na,LFLoading:Wo,Toast:$n},setup(){return{router:Jh()}},mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}},data(){return{inputFilters:{search:"",type:"",hideInactive:!1},appliedFilters:{search:"",type:"",hideInactive:!1},typeOptions:[],typeOptionsEnabled:!1,tableHeaders:[{text:"OFERTA",value:"name",sortable:!0},{text:"STATUS",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],offers:[],totalOffers:0,loading:!1,error:null,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,currentPage:1,perPage:10,sortBy:"name",sortDesc:!1,showDeleteModal:!1,offerToDelete:null,showStatusModal:!1,selectedOffer:null}},computed:{typeSelectOptions(){return[{value:"",label:"Todos"},...this.typeOptions]},hasActiveFilters(){return this.appliedFilters.search||this.appliedFilters.hideInactive}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.loadOffers())},currentPage(e,t){e!==t&&this.loadOffers()}},async created(){this.debouncedSearch=Qh.debounce(this.handleSearchInput,300),this.loadTypeOptions(),this.loadOffers()},methods:{getTypeLabel(e){if(!e)return"";const t=this.typeOptions.find(s=>s.value===e||s.label===e);return t?t.label:e},async loadTypeOptions(){const e=await Qb();e.types&&(this.typeOptionsEnabled=e.enabled,e.default&&(this.inputFilters.type=e.default),this.typeOptions=e.types.map(t=>({value:t,label:t})))},async loadOffers(){try{this.loading=!0,this.error=null;const e={search:this.appliedFilters.search||"",type:this.appliedFilters.type||null,onlyActive:this.appliedFilters.hideInactive===!0,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc},t=await Zb(e);this.offers=t.offers||[],this.totalOffers=t.total_items||0}catch(e){this.error=e.message}finally{this.loading=!1}},async handlePageChange(e){e!==this.currentPage&&(this.currentPage=e,await this.loadOffers())},async handlePerPageChange(e){e!==this.perPage&&(this.perPage=e,this.currentPage=1,await this.loadOffers())},async clearFilters(){this.inputFilters.type,this.inputFilters={search:"",type:"",hideInactive:!1},this.appliedFilters={search:"",type:"",hideInactive:!1},this.currentPage=1,await this.loadOffers()},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadOffers()},createNewOffer(){this.router.push({name:"offer.create"})},editOffer(e){this.router.push({name:"offer.edit",params:{id:e.id.toString()}})},deleteOffer(e){e.can_delete&&(this.offerToDelete=e,this.showDeleteModal=!0)},async confirmDeleteOffer(){if(this.offerToDelete)try{this.loading=!0,await Jb(this.offerToDelete.id),await this.loadOffers(),this.showSuccessMessage(`Oferta "${this.offerToDelete.name}" excluída com sucesso`),this.offerToDelete=null,this.showDeleteModal=!1}catch(e){this.error=e.message,this.showErrorMessage(`Erro ao excluir oferta "${this.offerToDelete.name}"`)}finally{this.loading=!1}},toggleOfferStatus(e){e.status===0&&!e.can_activate||(this.selectedOffer=e,this.showStatusModal=!0)},async confirmToggleStatus(){if(this.selectedOffer)try{this.loading=!0,await sy(this.selectedOffer.id,this.selectedOffer.status===1),await this.loadOffers(),this.showSuccessMessage(this.selectedOffer.status===1?`Oferta "${this.selectedOffer.name}" inativada com sucesso`:`Oferta "${this.selectedOffer.name}" inativada com sucesso`),this.selectedOffer=null,this.showStatusModal=!1}catch(e){this.error=e.message,this.showErrorMessage(this.selectedOffer.status===1?`Erro ao inativar oferta "${this.selectedOffer.name}"`:`Erro ao ativar oferta "${this.selectedOffer.name}"`)}finally{this.loading=!1}},getStatusButtonTitle(e){return e.status===1?"Desativar":e.can_activate?"Ativar":"Não é possível ativar esta oferta"},async handleTypeChange(e){this.appliedFilters.type=e,this.currentPage=1,await this.loadOffers()},async handleHideInactiveChange(e){const t=e===!0;this.inputFilters.hideInactive=t,this.appliedFilters.hideInactive=t,this.currentPage=1,await this.loadOffers()},async handleSearchInput(){(this.inputFilters.search.length>=3||this.inputFilters.search==="")&&(this.appliedFilters.search=this.inputFilters.search,this.currentPage=1,await this.loadOffers())},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},$w={id:"offer-manager-view",class:"offer-manager"},Hw={class:"new-offer-container"},qw={key:0,class:"alert alert-danger"},Ww={class:"table-container"},jw=["title"],zw={key:0},Gw={key:1},Kw={class:"action-buttons"},Zw=["onClick"],Yw=["onClick"],Jw=["onClick","disabled","title"],Qw={key:0,class:"fas fa-eye text-white"},Xw={key:1,class:"fas fa-eye-slash"},eE=["onClick","disabled","title"];function tE(e,t,s,i,r,a){var we,X,pe,be,Ae,ae;const u=G("CustomButton"),d=G("PageHeader"),h=G("CustomInput"),_=G("FilterGroup"),p=G("CustomSelect"),g=G("CustomCheckbox"),w=G("FilterActions"),x=G("FilterRow"),P=G("FilterSection"),L=G("CustomTable"),te=G("Pagination"),N=G("ConfirmationModal"),re=G("LFLoading"),J=G("Toast");return D(),S("div",$w,[M(d,{title:"Gerenciamento de ofertas"},{actions:Se(()=>[c("div",Hw,[M(u,{variant:"primary",icon:"fa fa-plus",label:"Adicionar",onClick:a.createNewOffer},null,8,["onClick"])])]),_:1}),M(P,{title:"FILTRO"},{default:Se(()=>[M(x,{inline:!0},{default:Se(()=>[M(_,{label:"Oferta"},{default:Se(()=>[M(h,{modelValue:r.inputFilters.search,"onUpdate:modelValue":t[0]||(t[0]=A=>r.inputFilters.search=A),placeholder:"Buscar...",width:339,"has-search-icon":!0,onInput:e.debouncedSearch},null,8,["modelValue","onInput"])]),_:1}),r.typeOptionsEnabled?(D(),_t(_,{key:0,label:"Tipo"},{default:Se(()=>[M(p,{modelValue:r.inputFilters.type,"onUpdate:modelValue":[t[1]||(t[1]=A=>r.inputFilters.type=A),a.handleTypeChange],options:a.typeSelectOptions,width:144},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1})):Q("",!0),M(_,{"is-checkbox":!0},{default:Se(()=>[M(g,{modelValue:r.inputFilters.hideInactive,"onUpdate:modelValue":[t[2]||(t[2]=A=>r.inputFilters.hideInactive=A),a.handleHideInactiveChange],id:"hideInactive",label:"Não exibir inativas"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),M(w,null,{default:Se(()=>[M(u,{variant:"secondary",label:"Limpar",onClick:a.clearFilters},null,8,["onClick"])]),_:1})]),_:1})]),_:1}),r.error?(D(),S("div",qw,[t[7]||(t[7]=c("i",{class:"fas fa-exclamation-circle"},null,-1)),qe(" "+W(r.error),1)])):Q("",!0),c("div",Ww,[M(L,{headers:r.tableHeaders,items:r.offers,"sort-by":r.sortBy,"sort-desc":r.sortDesc,onSort:a.handleTableSort},{"item-description":Se(({item:A})=>[c("span",{title:A.description},W(A.description.length>50?A.description.slice(0,50)+"...":A.description),9,jw)]),"item-type":Se(({item:A})=>[qe(W(A.type.charAt(0).toUpperCase()+A.type.slice(1)),1)]),"item-status":Se(({item:A})=>[A.status===1?(D(),S("span",zw,t[8]||(t[8]=[c("svg",{class:"mr-1",width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[c("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"}),c("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",stroke:"var(--success)","stroke-width":"2"}),c("path",{d:"M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM15.3589 7.34055C15.2329 7.34314 15.1086 7.37093 14.9937 7.42258C14.8788 7.47425 14.7755 7.54884 14.6899 7.64133L10.3491 13.1726L7.73291 10.5544C7.55519 10.3888 7.31954 10.2992 7.07666 10.3034C6.83383 10.3078 6.60191 10.4061 6.43018 10.5779C6.25849 10.7496 6.16005 10.9815 6.15576 11.2243C6.15152 11.4672 6.24215 11.7019 6.40771 11.8796L9.71533 15.1882C9.80438 15.2771 9.91016 15.3472 10.0269 15.3943C10.1436 15.4413 10.2691 15.4649 10.395 15.4626C10.5206 15.4602 10.6446 15.4327 10.7593 15.3816C10.8742 15.3302 10.9782 15.256 11.064 15.1638L16.0532 8.92648C16.2233 8.74961 16.3183 8.51269 16.3159 8.2673C16.3136 8.02207 16.2147 7.78755 16.0415 7.61398H16.0396C15.9503 7.52501 15.844 7.45488 15.7271 7.40793C15.6101 7.36102 15.4849 7.33798 15.3589 7.34055Z",fill:"var(--success)"})],-1),qe(" Ativa ")]))):(D(),S("span",Gw,t[9]||(t[9]=[c("svg",{class:"mr-1",width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[c("g",{"clip-path":"url(#clip0_572_6021)"},[c("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"}),c("path",{d:"M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM14.7524 7.02512C14.6703 7.02512 14.5891 7.04157 14.5132 7.07297C14.4373 7.10442 14.3682 7.1506 14.3101 7.20871L11.0024 10.5173L7.69482 7.20871C7.57747 7.09135 7.41841 7.02512 7.25244 7.02512C7.08647 7.02512 6.92742 7.09135 6.81006 7.20871C6.6927 7.32607 6.62646 7.48512 6.62646 7.65109C6.62646 7.81706 6.6927 7.97612 6.81006 8.09348L10.1187 11.4011L6.81006 14.7087C6.75195 14.7668 6.70577 14.8359 6.67432 14.9118C6.64292 14.9877 6.62646 15.069 6.62646 15.1511C6.62646 15.2332 6.64292 15.3145 6.67432 15.3904C6.70577 15.4663 6.75195 15.5354 6.81006 15.5935C6.92742 15.7108 7.08647 15.7771 7.25244 15.7771C7.33456 15.7771 7.41583 15.7606 7.4917 15.7292C7.56762 15.6978 7.63671 15.6516 7.69482 15.5935L11.0024 12.2849L14.3101 15.5935C14.3682 15.6516 14.4373 15.6978 14.5132 15.7292C14.5891 15.7606 14.6703 15.7771 14.7524 15.7771C14.8346 15.7771 14.9158 15.7606 14.9917 15.7292C15.0676 15.6978 15.1367 15.6516 15.1948 15.5935C15.2529 15.5354 15.2991 15.4663 15.3306 15.3904C15.362 15.3145 15.3784 15.2332 15.3784 15.1511C15.3784 15.069 15.362 14.9877 15.3306 14.9118C15.2991 14.8359 15.2529 14.7668 15.1948 14.7087L11.8862 11.4011L15.1948 8.09348C15.2529 8.03537 15.2991 7.96627 15.3306 7.89035C15.362 7.81448 15.3784 7.73321 15.3784 7.65109C15.3784 7.56898 15.362 7.48771 15.3306 7.41183C15.2991 7.33591 15.2529 7.26682 15.1948 7.20871C15.1367 7.1506 15.0676 7.10442 14.9917 7.07297C14.9158 7.04157 14.8346 7.02512 14.7524 7.02512Z",fill:"var(--danger)"})]),c("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",stroke:"var(--danger)","stroke-width":"2"}),c("defs",null,[c("clipPath",{id:"clip0_572_6021"},[c("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"})])])],-1),qe(" Inativo ")])))]),"item-actions":Se(({item:A})=>[c("div",Kw,[c("button",{class:"btn-action btn-edit",onClick:Ee=>a.editOffer(A),title:"Visualizar"},t[10]||(t[10]=[c("svg",{width:"38",height:"39",viewBox:"0 0 38 39",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[c("path",{d:"M18.1875 25.5897C20.04 25.5897 21.5417 24.0629 21.5417 22.1795C21.5417 20.296 20.04 18.7692 18.1875 18.7692C16.3351 18.7692 14.8334 20.296 14.8334 22.1795C14.8334 24.0629 16.3351 25.5897 18.1875 25.5897Z",stroke:"white","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),c("path",{d:"M11 29.4872L15.7917 24.6154M11 22.6667V11.9487C11 11.4319 11.2019 10.9362 11.5614 10.5708C11.9208 10.2053 12.4083 10 12.9167 10H20.5833L26.3333 15.8462V27.5385C26.3333 28.0553 26.1314 28.551 25.772 28.9164C25.4125 29.2819 24.925 29.4872 24.4167 29.4872H17.7083",stroke:"white","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)]),8,Zw),c("button",{class:"btn-action btn-edit",onClick:Ee=>a.editOffer(A),title:"Editar"},t[11]||(t[11]=[c("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[c("g",{"clip-path":"url(#clip0_9_197955)"},[c("path",{d:"M12.854 0.145905C12.7602 0.0521694 12.6331 -0.000488281 12.5005 -0.000488281C12.3679 -0.000488281 12.2408 0.0521694 12.147 0.145905L10.5 1.7929L14.207 5.49991L15.854 3.8539C15.9006 3.80746 15.9375 3.75228 15.9627 3.69154C15.9879 3.63079 16.0009 3.56567 16.0009 3.4999C16.0009 3.43414 15.9879 3.36902 15.9627 3.30827C15.9375 3.24753 15.9006 3.19235 15.854 3.1459L12.854 0.145905ZM13.5 6.2069L9.793 2.4999L3.293 8.9999H3.5C3.63261 8.9999 3.75978 9.05258 3.85355 9.14635C3.94732 9.24012 4 9.3673 4 9.4999V9.9999H4.5C4.63261 9.9999 4.75978 10.0526 4.85355 10.1464C4.94732 10.2401 5 10.3673 5 10.4999V10.9999H5.5C5.63261 10.9999 5.75978 11.0526 5.85355 11.1464C5.94732 11.2401 6 11.3673 6 11.4999V11.9999H6.5C6.63261 11.9999 6.75978 12.0526 6.85355 12.1464C6.94732 12.2401 7 12.3673 7 12.4999V12.7069L13.5 6.2069ZM6.032 13.6749C6.01095 13.619 6.00012 13.5597 6 13.4999V12.9999H5.5C5.36739 12.9999 5.24021 12.9472 5.14644 12.8535C5.05268 12.7597 5 12.6325 5 12.4999V11.9999H4.5C4.36739 11.9999 4.24021 11.9472 4.14644 11.8535C4.05268 11.7597 4 11.6325 4 11.4999V10.9999H3.5C3.36739 10.9999 3.24021 10.9472 3.14644 10.8535C3.05268 10.7597 3 10.6325 3 10.4999V9.9999H2.5C2.44022 9.99981 2.38094 9.98897 2.325 9.96791L2.146 10.1459C2.09835 10.1939 2.06093 10.251 2.036 10.3139L0.0359968 15.3139C-0.000373859 15.4048 -0.00927736 15.5043 0.0103901 15.6002C0.0300575 15.6961 0.077431 15.7841 0.146638 15.8533C0.215844 15.9225 0.30384 15.9698 0.399716 15.9895C0.495593 16.0092 0.595133 16.0003 0.685997 15.9639L5.686 13.9639C5.74886 13.939 5.80601 13.9016 5.854 13.8539L6.032 13.6759V13.6749Z",fill:"var(--white)"})]),c("defs",null,[c("clipPath",{id:"clip0_9_197955"},[c("rect",{width:"16",height:"16",fill:"white"})])])],-1)]),8,Yw),c("button",{class:fe(["btn-action",A.status===1?"btn-deactivate":"btn-activate"]),onClick:Ee=>a.toggleOfferStatus(A),disabled:A.status===0&&!A.can_activate,title:a.getStatusButtonTitle(A)},[A.status===1?(D(),S("i",Qw)):(D(),S("i",Xw))],10,Jw),c("button",{class:"btn-action btn-delete",onClick:Ee=>a.deleteOffer(A),disabled:!A.can_delete,title:A.can_delete?"Excluir":"Não é possível excluir esta oferta"},t[12]||(t[12]=[c("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,eE)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),M(te,{"current-page":r.currentPage,"onUpdate:currentPage":t[3]||(t[3]=A=>r.currentPage=A),"per-page":r.perPage,"onUpdate:perPage":t[4]||(t[4]=A=>r.perPage=A),total:r.totalOffers,loading:r.loading},null,8,["current-page","per-page","total","loading"]),M(N,{show:r.showDeleteModal,size:"md",title:"A exclusão desta instância de oferta é uma ação irreversível.",message:"Todos os cursos vinculados serão desassociados e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[5]||(t[5]=A=>r.showDeleteModal=!1),onConfirm:a.confirmDeleteOffer},null,8,["show","onConfirm"]),M(N,{show:r.showStatusModal,size:"md",title:((we=r.selectedOffer)==null?void 0:we.status)===1?"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((X=r.selectedOffer)==null?void 0:X.status)===1?"":"Tem certeza que deseja ativar esta oferta?","list-title":((pe=r.selectedOffer)==null?void 0:pe.status)===1?"Comportamento para os cursos, turmas e matrículas:":"","list-items":((be=r.selectedOffer)==null?void 0:be.status)===1?["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((Ae=r.selectedOffer)==null?void 0:Ae.status)===1?"Inativar oferta":"Ativar","cancel-button-text":"Cancelar",icon:((ae=r.selectedOffer)==null?void 0:ae.status)===1?"warning":"question",onClose:t[6]||(t[6]=A=>r.showStatusModal=!1),onConfirm:a.confirmToggleStatus},null,8,["show","title","message","list-title","list-items","confirm-button-text","icon","onConfirm"]),M(re,{"is-loading":r.loading},null,8,["is-loading"]),M(J,{show:r.showToast,message:r.toastMessage,type:r.toastType,duration:3e3},null,8,["show","message","type"])])}const sE=Fe(Bw,[["render",tE],["__scopeId","data-v-cb31bbe9"]]);async function nE(e={}){try{return await je("local_offermanager_fetch_enrolments",{offerclassid:e.offerclassid,userids:e.userids||[],page:e.page||1,perpage:e.perpage||20,orderby:e.orderby||"fullname",direction:e.direction||"ASC"})}catch(t){throw t}}async function gu(e={}){try{return await je("local_offermanager_get_enroled_users",{offerclassid:e.offerclassid,searchstring:e.searchstring||"",fieldstring:e.fieldstring||"name",excludeduserids:e.excludeduserids||[]})}catch(t){throw t}}async function rE(e={}){try{return await je("local_offermanager_enrol_users",{offerclassid:e.offerclassid,userids:e.userids||[],roleid:e.roleid||5})}catch(t){throw t}}async function oE(e,t="",s){try{return await je("local_offermanager_get_potential_users_to_enrol",{offerclassid:e,search_string:t,excluded_userids:s})}catch(i){throw i}}async function iE(e={}){try{return await je("local_offermanager_edit_offer_user_enrol",{offeruserenrolid:e.offeruserenrolid,status:e.status,timestart:e.timestart,timeend:e.timeend,roleid:e.roleid})}catch(t){throw t}}async function aE(e={}){try{return await je("local_offermanager_edit_offer_user_enrol_bulk",{offeruserenrolids:e.offeruserenrolids||[],status:e.status,timestart:e.timestart,timeend:e.timeend})}catch(t){throw t}}async function lE(e){try{return await je("local_offermanager_delete_offer_user_enrol_bulk",{offeruserenrolids:e})===!0?e.map(i=>({id:i,operation_status:!0})):[]}catch(t){throw t}}async function uE(e){try{return await je("local_offermanager_get_roles",{offeruserenrolid:e})}catch(t){throw t}}async function cE(e,t){try{return await je("local_offermanager_update_roles",{offeruserenrolid:e,roleids:Array.isArray(t)?t:[t]})}catch(s){throw s}}const SM="",dE={name:"HierarchicalSelect",props:{modelValue:{type:String,default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.$emit("navigate",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate","navigate"]},fE={class:"select-wrapper"},hE=["value","disabled"],pE=["label"],mE=["value"],gE={key:1,class:"error-message"};function _E(e,t,s,i,r,a){return D(),S("div",{ref:"selectContainer",class:"hierarchical-select-container",style:as(a.customWidth)},[s.label?(D(),S("div",{key:0,class:fe(["select-label",{disabled:s.disabled}])},W(s.label),3)):Q("",!0),c("div",fE,[c("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:fe(["hierarchical-select",{error:s.hasError}]),disabled:s.disabled},[(D(!0),S(Ie,null,lt(s.options,u=>(D(),S("optgroup",{key:u.value,label:u.label},[(D(!0),S(Ie,null,lt(u.children,d=>(D(),S("option",{key:d.value,value:d.value,class:"child-option"},W(d.label),9,mE))),128))],8,pE))),128))],42,hE),c("div",{class:fe(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(D(),S("div",gE,W(s.errorMessage),1)):Q("",!0)],4)}const vE=Fe(dE,[["render",_E],["__scopeId","data-v-b5d38077"]]),TM="",bE={name:"FilterTag",emits:["remove"]};function yE(e,t,s,i,r,a){return D(),S("div",{class:"tag badge badge-primary",onClick:t[0]||(t[0]=u=>e.$emit("remove"))},[t[1]||(t[1]=c("i",{class:"fas fa-times"},null,-1)),Lt(e.$slots,"default",{},void 0,!0)])}const jo=Fe(bE,[["render",yE],["__scopeId","data-v-fe063554"]]),NM="",wE={name:"FilterTags"},EE={class:"filter-tags"};function CE(e,t,s,i,r,a){return D(),S("div",EE,[Lt(e.$slots,"default",{},void 0,!0)])}const ra=Fe(wE,[["render",CE],["__scopeId","data-v-d8e54e5f"]]),IM="",xE={name:"Autocomplete",components:{FilterTag:jo,FilterTags:ra},props:{modelValue:{type:[Array,Object,String,Number],default:()=>[]},items:{type:Array,default:()=>[]},placeholder:{type:String,default:""},label:{type:String,default:""},width:{type:[Number,String],default:"auto"},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},minChars:{type:Number,default:3},showAllOption:{type:Boolean,default:!1},inputMaxWidth:{type:[String,Number],default:null},autoOpen:{type:Boolean,default:!0},noResultsText:{type:String,default:"Nenhum item disponível"},hasSearchIcon:{type:Boolean,default:!1},showFilterTags:{type:Boolean,default:!0},showSelectedInInput:{type:Boolean,default:!1},maxLabelLength:{type:Number,default:30},loading:{type:Boolean,default:!1},keepOpenOnSelect:{type:Boolean,default:!1}},emits:["update:modelValue","select","select-all","load-more","search"],data(){return{searchQuery:"",isOpen:!1,selectedIndex:-1,internalItems:[],uniqueId:`autocomplete-${Math.random().toString(36).substring(2,9)}`,focusedOptionIndex:-1,blurTimeout:null,debouncedSearch:null}},computed:{displayItems(){let e=this.internalItems;if(this.searchQuery){const t=this.searchQuery.toLowerCase();e=this.internalItems.filter(s=>s.label.toLowerCase().includes(t))}return this.showAllOption&&Array.isArray(this.modelValue)?[{label:"Todos",value:"__ALL__"},...e]:e},inputMaxWidthStyle(){return this.inputMaxWidth?typeof this.inputMaxWidth=="number"?`${this.inputMaxWidth}px`:this.inputMaxWidth:null},getSelectedItemLabel(){return this.modelValue?this.modelValue.label:""},selectedItems(){return Array.isArray(this.modelValue)?this.modelValue.map(e=>{if(e.value&&e.label!=="")return e;const t=this.items.find(i=>i.value===(e.value||e)),s=(t==null?void 0:t.label)||"";return{value:e.value||e,label:s}}):[]}},created(){this.debouncedSearch=Qh.debounce(e=>{this.$emit("search",e)},300)},watch:{items:{handler(e){this.internalItems=Array.isArray(e)?[...e]:[],this.autoOpen&&this.keepOpenOnSelect&&!this.disabled&&this.internalItems.length>0&&this.$refs.inputElement===document.activeElement&&(this.isOpen=!0)},immediate:!0,deep:!0},searchQuery(e){this.isOpen=!0,this.selectedIndex=-1,(e.length===0||e.length>=this.minChars)&&this.debouncedSearch(e)}},methods:{handleFocus(){this.autoOpen&&!this.disabled&&(this.isOpen=!0,this.selectedIndex=-1,this.searchQuery&&(this.searchQuery="",this.$emit("search",""))),this.blurTimeout&&(clearTimeout(this.blurTimeout),this.blurTimeout=null)},openDropdown(){this.disabled||(this.isOpen=!0)},handleBlur(){this.blurTimeout=setTimeout(()=>{this.$el.contains(document.activeElement)||(this.isOpen=!1,this.selectedIndex=-1)},150)},handleInput(){this.disabled||(this.isOpen=!0)},selectItem(e){if(e.value==="__ALL__"){if(Array.isArray(this.modelValue)){if(this.modelValue.length===this.items.length){this.$emit("update:modelValue",[]);return}this.$emit("update:modelValue",this.items),this.$emit("select-all")}this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()});return}if(Array.isArray(this.modelValue)){const t=[...this.modelValue],s=t.findIndex(i=>i.value===e.value);s===-1?t.push(e):t.splice(s,1),this.$emit("update:modelValue",t)}else this.$emit("update:modelValue",e),this.$emit("select",e);this.searchQuery="",this.isOpen=!!this.keepOpenOnSelect,this.selectedIndex=-1,this.$nextTick(()=>{this.autoOpen&&this.focusInput()})},removeItem(e){if(Array.isArray(this.modelValue)){const t=this.modelValue.filter(s=>s.value!==e.value);this.$emit("update:modelValue",t)}else this.$emit("update:modelValue","");Array.isArray(this.modelValue)?(this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1):(this.selectedIndex=-1,this.$nextTick(()=>{this.isOpen=!1})),this.$nextTick(()=>{this.focusInput()})},removeSelectedItem(){this.$emit("update:modelValue",""),this.searchQuery="",this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()})},handleKeydown(e){if(!this.isOpen&&e.key!=="Tab"){this.isOpen=!0;return}switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.min(this.selectedIndex+1,this.displayItems.length-1),this.focusOption(this.selectedIndex);break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.max(this.selectedIndex-1,-1),this.selectedIndex===-1?this.focusInput():this.focusOption(this.selectedIndex);break;case"Enter":e.preventDefault(),this.selectedIndex>=0?this.selectItem(this.displayItems[this.selectedIndex]):this.searchQuery&&this.searchQuery.length>=this.minChars&&this.$emit("search",this.searchQuery);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1;break;case"Tab":this.isOpen&&!e.shiftKey&&this.displayItems.length>0&&(e.preventDefault(),e.stopPropagation(),this.selectedIndex=0,this.focusOption(0));break}},handleOptionKeydown(e,t,s){switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1&&(this.selectedIndex=s+1,this.focusOption(this.selectedIndex));break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput());break;case"Enter":case" ":e.preventDefault(),this.selectItem(t);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1,this.focusInput();break;case"Tab":e.shiftKey?(e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput())):(e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1?(this.selectedIndex=s+1,this.focusOption(this.selectedIndex)):(this.selectedIndex=0,this.focusOption(0)));break}},focusInput(){this.$refs.inputElement&&this.$refs.inputElement.focus()},focusOption(e){requestAnimationFrame(()=>{var s;const t=(s=this.$refs.optionElements)==null?void 0:s[e];t&&t.focus()})},handleClickOutside(e){this.$el.contains(e.target)||(this.isOpen=!1,this.selectedIndex=-1)},truncateLabel(e){return e?e.length<=this.maxLabelLength?e:e.substring(0,this.maxLabelLength)+"...":""},handleScroll(e){if(!e||!e.target)return;const t=e.target;t.scrollHeight&&t.scrollTop!==void 0&&t.clientHeight&&t.scrollHeight-t.scrollTop-t.clientHeight<50&&this.$emit("load-more")}},mounted(){document.addEventListener("click",this.handleClickOutside),this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$nextTick(()=>{this.isOpen=!0})},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside)}},DE={class:"autocomplete-container"},OE=["id"],SE={class:"autocomplete-wrapper"},TE=["placeholder","disabled","aria-expanded","aria-owns","aria-labelledby","aria-controls"],NE={key:0,class:"selected-item"},IE=["title"],AE=["id"],ME=["id","data-index","aria-selected","tabindex","onClick","onKeydown","title"],PE={class:"item-label"},kE={key:0,class:"fas fa-check"},VE={key:0,class:"dropdown-item loading-item"},RE={key:1,class:"dropdown-item no-results"},FE={key:0,class:"tags-container"};function LE(e,t,s,i,r,a){const u=G("FilterTag"),d=G("FilterTags");return D(),S("div",DE,[s.label?(D(),S("label",{key:0,class:fe(["filter-label",{required:s.required}]),id:`${r.uniqueId}-label`},W(s.label),11,OE)):Q("",!0),c("div",SE,[c("div",{class:"input-container",style:as({maxWidth:a.inputMaxWidthStyle})},[c("div",{class:fe(["input-wrapper",{"has-search-icon":s.hasSearchIcon,"has-selected-item":s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!r.isOpen&&!r.searchQuery}])},[at(c("input",{type:"text",class:"form-control",placeholder:s.placeholder,"onUpdate:modelValue":t[0]||(t[0]=h=>r.searchQuery=h),disabled:s.disabled,"aria-expanded":r.isOpen,"aria-owns":`${r.uniqueId}-listbox`,"aria-labelledby":s.label?`${r.uniqueId}-label`:void 0,"aria-autocomplete":"list","aria-controls":`${r.uniqueId}-listbox`,role:"combobox",tabindex:"0",onKeydown:t[1]||(t[1]=(...h)=>a.handleKeydown&&a.handleKeydown(...h)),onFocus:t[2]||(t[2]=h=>!s.disabled&&a.handleFocus),onInput:t[3]||(t[3]=(...h)=>a.handleInput&&a.handleInput(...h)),onClick:t[4]||(t[4]=h=>!s.disabled&&a.openDropdown()),onBlur:t[5]||(t[5]=(...h)=>a.handleBlur&&a.handleBlur(...h)),ref:"inputElement"},null,40,TE),[[Yt,r.searchQuery]]),s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!r.isOpen&&!r.searchQuery?(D(),S("div",NE,[c("span",{class:"selected-text",title:a.getSelectedItemLabel},W(a.truncateLabel(a.getSelectedItemLabel)),9,IE),c("i",{class:"fas fa-times remove-selected",onClick:t[6]||(t[6]=Pt((...h)=>a.removeSelectedItem&&a.removeSelectedItem(...h),["stop"]))})])):Q("",!0),s.hasSearchIcon&&!(s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!r.isOpen&&!r.searchQuery)?(D(),S("i",{key:1,class:fe(["search-icon",{"fas fa-search":!s.loading,"spinner-border spinner-border-sm":s.loading}])},null,2)):Q("",!0)],2),r.isOpen?(D(),S("div",{key:0,class:"dropdown-menu show",id:`${r.uniqueId}-listbox`,role:"listbox",tabindex:"-1",ref:"dropdownMenu",onScroll:t[7]||(t[7]=(...h)=>a.handleScroll&&a.handleScroll(...h))},[a.displayItems.length>0?(D(),S(Ie,{key:0},[(D(!0),S(Ie,null,lt(a.displayItems,(h,_)=>(D(),S("div",{key:h.value==="__ALL__"?"__ALL__":h.value,class:fe(["dropdown-item",{active:r.selectedIndex===_,selected:h.value!=="__ALL__"&&(Array.isArray(a.selectedItems)?a.selectedItems.some(p=>p.value===h.value):a.selectedItems===h.value)}]),id:`${r.uniqueId}-option-${_}`,role:"option","data-index":_,"aria-selected":r.selectedIndex===_,tabindex:r.selectedIndex===_?0:-1,onClick:p=>a.selectItem(h),onKeydown:p=>a.handleOptionKeydown(p,h,_),ref_for:!0,ref:"optionElements",title:h.label},[c("span",PE,W(a.truncateLabel(h.label)),1),h.value!=="__ALL__"&&Array.isArray(a.selectedItems)&&a.selectedItems.some(p=>p.value===h.value)?(D(),S("i",kE)):Q("",!0)],42,ME))),128)),s.loading?(D(),S("div",VE,t[8]||(t[8]=[c("span",null,"Carregando mais itens...",-1)]))):Q("",!0)],64)):(D(),S("div",RE,W(s.noResultsText||"Nenhum item disponível"),1))],40,AE)):Q("",!0)],4),s.showFilterTags&&Array.isArray(s.modelValue)&&s.modelValue.length>0?(D(),S("div",FE,[M(d,null,{default:Se(()=>[(D(!0),S(Ie,null,lt(a.selectedItems,h=>(D(),_t(u,{key:h.value,onRemove:_=>a.removeItem(h)},{default:Se(()=>[qe(W(h.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1})])):Q("",!0)])])}const qr=Fe(xE,[["render",LE],["__scopeId","data-v-b4c89e9e"]]),AM="",UE={name:"EnrolmentModalNew",components:{Toast:$n,CustomSelect:Xs},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Matricular usuários na turma"},size:{type:String,default:"lg",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Salvar"},cancelButtonText:{type:String,default:"Cancelar"},offerclassid:{type:Number,required:!0},roles:{type:Array,required:!0}},emits:["close","success"],data(){return{enrolmentMethod:"manual",enrolmentMethodOptions:[{value:"manual",label:"Manual"},{value:"batch",label:"Em lote"}],selectedRoleId:"",searchQuery:"",isOpen:!1,userOptions:[],selectedUsers:[],debounceTimer:null,selectedFile:null,csvUsers:[],isDragging:!1,csvDelimiter:",",csvEncoding:"UTF-8",delimiterOptions:[{value:",",label:","},{value:";",label:";"},{value:":",label:":"},{value:"	",label:"\\t"},{value:" ",label:"Espaço"}],encodingOptions:[{value:"UTF-8",label:"UTF-8"},{value:"WINDOWS-1252",label:"WINDOWS-1252"},{value:"ISO-8859-1",label:"ISO-8859-1"},{value:"ASCII",label:"ASCII"},{value:"ISO-8859-2",label:"ISO-8859-2"},{value:"ISO-8859-3",label:"ISO-8859-3"},{value:"ISO-8859-4",label:"ISO-8859-4"},{value:"ISO-8859-5",label:"ISO-8859-5"},{value:"ISO-8859-6",label:"ISO-8859-6"},{value:"ISO-8859-7",label:"ISO-8859-7"},{value:"ISO-8859-8",label:"ISO-8859-8"},{value:"ISO-8859-9",label:"ISO-8859-9"},{value:"ISO-8859-10",label:"ISO-8859-10"},{value:"ISO-8859-13",label:"ISO-8859-13"},{value:"ISO-8859-14",label:"ISO-8859-14"},{value:"ISO-8859-15",label:"ISO-8859-15"},{value:"ISO-8859-16",label:"ISO-8859-16"},{value:"WINDOWS-874",label:"WINDOWS-874"},{value:"WINDOWS-1250",label:"WINDOWS-1250"},{value:"WINDOWS-1251",label:"WINDOWS-1251"},{value:"WINDOWS-1253",label:"WINDOWS-1253"},{value:"WINDOWS-1254",label:"WINDOWS-1254"},{value:"WINDOWS-1255",label:"WINDOWS-1255"},{value:"WINDOWS-1256",label:"WINDOWS-1256"},{value:"WINDOWS-1257",label:"WINDOWS-1257"},{value:"WINDOWS-1258",label:"WINDOWS-1258"},{value:"KOI8-R",label:"KOI8-R"},{value:"MACINTOSH",label:"MACINTOSH"},{value:"IBM866",label:"IBM866"},{value:"BIG5",label:"BIG5"},{value:"EUC-JP",label:"EUC-JP"},{value:"SHIFT_JIS",label:"SHIFT_JIS"},{value:"EUC-KR",label:"EUC-KR"},{value:"UTF-7",label:"UTF-7"},{value:"UTF-16",label:"UTF-16"},{value:"UTF-32",label:"UTF-32"},{value:"UCS-2",label:"UCS-2"},{value:"UCS-4",label:"UCS-4"}],loadingUsers:!1,isSubmitting:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,showResultAlerts:!1,batchMessage:"",batchMessageType:"success",failedMessages:[],reenrolMessages:[]}},computed:{isFormValid(){return this.roles.length===0?!1:this.enrolmentMethod==="manual"?this.selectedUsers.length>0&&this.selectedRoleId:this.enrolmentMethod==="batch"?this.csvUsers.length>0&&this.selectedRoleId:!1}},watch:{show(e){document.body.style.overflow=e?"hidden":"",e&&this.initializeForm()}},mounted(){document.addEventListener("keydown",this.handleKeyDown),document.addEventListener("click",this.handleClickOutside),this.show&&(document.body.style.overflow="hidden",this.initializeForm())},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.removeEventListener("click",this.handleClickOutside),document.body.style.overflow=""},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")},handleClickOutside(e){if(this.show===!1)return;const t=document.querySelector(".custom-autocomplete-wrapper");t&&!t.contains(e.target)&&(this.isOpen=!1)},async initializeForm(){this.resetForm()},resetForm(){let e=this.roles.find(t=>t.value==5);this.enrolmentMethod="manual",this.selectedRoleId=e.value,this.searchQuery="",this.selectedUsers=[],this.selectedFile=null,this.csvUsers=[],this.csvDelimiter=",",this.csvEncoding="UTF-8",this.showResultAlerts=!1,this.batchMessage="",this.batchMessageType="success",this.failedMessages=[],this.reenrolMessages=[]},async fetchPotentialUsersToEnrol(e){this.loadingUsers=!0;let t=this.selectedUsers.map(i=>i.value);const s=await oE(this.offerclassid,e,t);this.userOptions=s.data.map(i=>({value:i.id,label:i.fullname})),this.loadingUsers=!1},handleInput(){const e=this.searchQuery.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialUsersToEnrol(e),this.userOptions&&(this.isOpen=!0)},500):(this.isOpen=!1,this.userOptions=[])},selectUser(e){const t=this.selectedUsers.findIndex(s=>s.value===e.value);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1),this.searchQuery="",this.isOpen=!1},removeUser(e){this.selectedUsers=this.selectedUsers.filter(t=>t.value!==e.value)},onDragOver(){this.isDragging=!0},onDragLeave(){this.isDragging=!1},onDrop(e){this.isDragging=!1;const t=e.dataTransfer.files;t.length>0&&this.processFile(t[0])},handleFileSelect(e){const t=e.target.files;t.length>0&&this.processFile(t[0])},removeFile(){this.selectedFile=null,this.csvUsers=[],this.$refs.fileInput&&(this.$refs.fileInput.value="")},processFile(e){if(e.type!=="text/csv"&&!e.name.endsWith(".csv")){this.showErrorMessage("Por favor, selecione um arquivo CSV válido.");return}this.selectedFile=e,this.readCSVFile(e)},readCSVFile(e){const t=new FileReader;t.onload=s=>{const i=s.target.result;this.parseCSV(i)},t.onerror=s=>{if(console.error("Erro ao ler o arquivo:",s),this.csvEncoding!=="UTF-8"){console.log("Tentando ler com UTF-8 como fallback...");const i=new FileReader;i.onload=r=>{const a=r.target.result;this.parseCSV(a)},i.onerror=()=>{this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato e a codificação estão corretos.")},i.readAsText(e,"UTF-8")}else this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato está correto.")};try{t.readAsText(e,this.csvEncoding)}catch(s){console.error("Erro ao tentar ler o arquivo com a codificação selecionada:",s),this.showErrorMessage(`Erro ao ler o arquivo com a codificação ${this.csvEncoding}. Tente selecionar outra codificação.`)}},parseCSV(e){try{const t=this.csvDelimiter,s=/�/.test(e);s&&console.warn("O arquivo contém caracteres inválidos. Pode haver um problema com a codificação selecionada.");const i=e.split(/\r?\n/),r=[];if(i.length<2){console.log("EnrolmentModalNew - Linhas do CSV:",i),this.showErrorMessage("O arquivo CSV deve conter pelo menos uma linha de cabeçalho e uma linha de dados.");return}const a=(_,p)=>{if(p==="\\t")return _.split("	");if(p===" ")return _.split(/\s+/);{const g=p.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");return _.split(new RegExp(g))}},u=a(i[0].toLowerCase(),t);if(u.length<2||!u.some(_=>_.includes("userid"))||!u.some(_=>_.includes("firstname"))){this.showErrorMessage("O arquivo CSV deve conter colunas para UserID e firstname do usuário.");return}const d=u.findIndex(_=>_.includes("userid")),h=u.findIndex(_=>_.includes("firstname"));for(let _=1;_<i.length;_++){const p=i[_].trim();if(!p)continue;const g=a(p,t);if(g.length>Math.max(d,h)){const w=g[d].trim(),x=g[h].trim();if(w&&x){if(!/^\d+$/.test(w)){console.warn(`Linha ${_+1}: ID inválido '${w}'. Deve ser um número.`);continue}r.push({id:w,name:x})}}}if(r.length===0){s?this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Pode haver um problema com a codificação selecionada. Tente selecionar outra codificação."):this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Verifique o formato do arquivo.");return}this.csvUsers=r}catch(t){console.error("Erro ao processar arquivo CSV:",t),this.showErrorMessage("Erro ao processar o arquivo CSV. Verifique o formato e a codificação e tente novamente.")}},formatFileSize(e){if(e===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB","TB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},async handleSubmit(){if(this.isFormValid)try{this.isSubmitting=!0;let e=[];this.enrolmentMethod==="manual"?e=this.selectedUsers.map(s=>s.value):this.enrolmentMethod==="batch"&&(e=this.csvUsers.map(s=>parseInt(s.id))),e||this.showErrorMessage("Nenhum usuário selecionado para efetuar a matrícula");const t=await rE({offerclassid:this.offerclassid,userids:e,roleid:parseInt(this.selectedRoleId)});if(t.data){this.showResultAlerts=!0;const s=t.data.filter(u=>u.success),i=s.length,r=i>0?s.filter(u=>u.reenrol):[],a=t.data.filter(u=>u.success==!1);this.batchMessage=i>0?`${i} de ${e.length} usuário(s) matriculado(s) com sucesso.`:"Nenhuma inscrição foi realizada",this.batchMessageType=i>0?"success":"danger",this.reenrolMessages=r.length>0?r.map(u=>u.message):[],this.failedMessages=a.length>0?a.map(u=>u.message):[],i>0&&this.$emit("success",{count:i,total:e.length})}}catch(e){this.showErrorMessage(e.message||"Erro ao matricular usuários. Tente novamente.")}finally{this.isSubmitting=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},BE={class:"modal-header"},$E={class:"modal-title"},HE={class:"modal-body"},qE={key:0,class:"loading-overlay"},WE={key:1,class:"result-alerts"},jE={key:1,class:"failed-messages"},zE={key:2,class:"reenrol-messages"},GE={key:2,class:"enrolment-modal"},KE={class:"form-row"},ZE={class:"form-group"},YE={class:"limited-width-input"},JE={class:"form-group"},QE={class:"limited-width-input"},XE={key:0,class:"error-message"},eC={key:0,class:"form-group"},tC={class:"user-select-container"},sC={class:"custom-autocomplete-wrapper"},nC={key:0,class:"dropdown-menu show"},rC=["onClick"],oC={key:0,class:"fas fa-check"},iC={key:0,class:"selected-users-container"},aC={class:"filter-tags"},lC=["onClick"],uC={key:1,class:"form-group"},cC={class:"file-name"},dC={class:"file-size"},fC={key:0,class:"csv-users-preview"},hC={class:"preview-header"},pC={class:"selected-users-container"},mC={class:"filter-tags"},gC={key:0,class:"more-users"},_C={class:"csv-info"},vC={class:"csv-example"},bC=["href"],yC={class:"csv-options-row"},wC={class:"csv-option"},EC={class:"csv-option"},CC={key:0,class:"modal-footer"},xC=["disabled"];function DC(e,t,s,i,r,a){const u=G("CustomSelect"),d=G("Toast");return D(),S(Ie,null,[s.show?(D(),S("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[c("div",{class:fe(["modal-container",[`modal-${s.size}`]]),onClick:t[14]||(t[14]=Pt(()=>{},["stop"]))},[c("div",BE,[c("h3",$E,W(s.title),1),c("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[16]||(t[16]=[c("i",{class:"fas fa-times"},null,-1)]))]),c("div",HE,[r.isSubmitting?(D(),S("div",qE,t[17]||(t[17]=[c("div",{class:"loading-content"},[c("div",{class:"spinner-border text-primary",role:"status"},[c("span",{class:"sr-only"},"Carregando...")]),c("p",{class:"loading-text mt-3"},"Processando matrículas...")],-1)]))):Q("",!0),r.showResultAlerts?(D(),S("div",WE,[r.batchMessage?(D(),S("div",{key:0,class:fe(["alert",r.batchMessageType==="success"?"alert-success":"alert-danger"])},[c("i",{class:fe(r.batchMessageType==="success"?"fas fa-check-circle":"fas fa-exclamation-triangle")},null,2),qe(" "+W(r.batchMessage),1)],2)):Q("",!0),r.failedMessages.length>0?(D(),S("div",jE,[(D(!0),S(Ie,null,lt(r.failedMessages,(h,_)=>(D(),S("div",{key:_,class:"alert alert-warning"},[t[18]||(t[18]=c("i",{class:"fas fa-exclamation-triangle"},null,-1)),qe(" "+W(h),1)]))),128))])):Q("",!0),r.reenrolMessages.length>0?(D(),S("div",zE,[(D(!0),S(Ie,null,lt(r.reenrolMessages,(h,_)=>(D(),S("div",{key:_,class:"alert alert-info"},[t[19]||(t[19]=c("i",{class:"fas fa-exclamation-triangle"},null,-1)),qe(" "+W(h),1)]))),128))])):Q("",!0)])):(D(),S("div",GE,[t[34]||(t[34]=c("h3",{class:"section-title"},"OPÇÕES DE MATRÍCULA",-1)),c("div",KE,[c("div",ZE,[t[20]||(t[20]=c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Forma de matrícula"),c("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),c("div",YE,[M(u,{modelValue:r.enrolmentMethod,"onUpdate:modelValue":t[1]||(t[1]=h=>r.enrolmentMethod=h),options:r.enrolmentMethodOptions,style:{width:"100%"},required:""},null,8,["modelValue","options"])])]),c("div",JE,[t[21]||(t[21]=c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Papel para atribuir"),c("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),c("div",QE,[M(u,{modelValue:r.selectedRoleId,"onUpdate:modelValue":t[2]||(t[2]=h=>r.selectedRoleId=h),options:s.roles,class:"w-100",required:""},null,8,["modelValue","options"]),s.roles.length===0?(D(),S("div",XE," Não foi possível carregar os papéis disponíveis para esta turma. ")):Q("",!0)])])]),r.enrolmentMethod==="manual"?(D(),S("div",eC,[t[24]||(t[24]=c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Selecionar usuários"),c("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),c("div",tC,[c("div",sC,[at(c("input",{type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[3]||(t[3]=h=>r.searchQuery=h),onInput:t[4]||(t[4]=(...h)=>a.handleInput&&a.handleInput(...h))},null,544),[[Yt,r.searchQuery]]),t[22]||(t[22]=c("div",{class:"select-arrow"},null,-1)),r.isOpen?(D(),S("div",nC,[(D(!0),S(Ie,null,lt(r.userOptions,(h,_)=>(D(),S("div",{key:h.value,class:"dropdown-item",onClick:p=>a.selectUser(h)},[qe(W(h.label)+" ",1),r.selectedUsers.some(p=>p.value===h.value)?(D(),S("i",oC)):Q("",!0)],8,rC))),128))])):Q("",!0)])]),r.selectedUsers.length>0?(D(),S("div",iC,[c("div",aC,[(D(!0),S(Ie,null,lt(r.selectedUsers,h=>(D(),S("div",{key:h.value,class:"tag badge badge-primary",onClick:_=>a.removeUser(h)},[t[23]||(t[23]=c("i",{class:"fas fa-times"},null,-1)),qe(" "+W(h.label),1)],8,lC))),128))])])):Q("",!0)])):Q("",!0),r.enrolmentMethod==="batch"?(D(),S("div",uC,[t[33]||(t[33]=c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Matricular usuários a partir de um arquivo CSV"),c("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),c("div",{class:fe(["csv-upload-area",{"drag-over":r.isDragging}]),onDragover:t[6]||(t[6]=Pt((...h)=>a.onDragOver&&a.onDragOver(...h),["prevent"])),onDragleave:t[7]||(t[7]=Pt((...h)=>a.onDragLeave&&a.onDragLeave(...h),["prevent"])),onDrop:t[8]||(t[8]=Pt((...h)=>a.onDrop&&a.onDrop(...h),["prevent"])),onClick:t[9]||(t[9]=h=>e.$refs.fileInput.click())},[c("input",{type:"file",ref:"fileInput",accept:".csv",style:{display:"none"},onChange:t[5]||(t[5]=(...h)=>a.handleFileSelect&&a.handleFileSelect(...h))},null,544),r.selectedFile?(D(),S(Ie,{key:1},[t[27]||(t[27]=c("div",{class:"file-icon"},[c("i",{class:"fas fa-file-alt"})],-1)),c("p",cC,W(r.selectedFile.name),1),c("p",dC," ("+W(a.formatFileSize(r.selectedFile.size))+") ",1),t[28]||(t[28]=c("p",{class:"file-replace-text"}," Clique ou arraste outro arquivo para substituir ",-1))],64)):(D(),S(Ie,{key:0},[t[25]||(t[25]=c("div",{class:"upload-icon"},[c("i",{class:"fas fa-arrow-down"})],-1)),t[26]||(t[26]=c("p",{class:"upload-text"}," Você pode arrastar e soltar arquivos aqui para adicioná-los. ",-1))],64))],34),r.csvUsers.length>0?(D(),S("div",fC,[c("div",hC,[c("span",null,"Usuários encontrados no arquivo ("+W(r.csvUsers.length)+"):",1)]),c("div",pC,[c("div",mC,[(D(!0),S(Ie,null,lt(r.csvUsers.slice(0,5),h=>(D(),S("div",{key:h.id,class:"tag badge badge-primary"},W(h.name),1))),128)),r.csvUsers.length>5?(D(),S("span",gC,"+"+W(r.csvUsers.length-5)+" mais",1)):Q("",!0)])])])):Q("",!0),c("div",_C,[t[32]||(t[32]=c("p",{class:"csv-format-text"},"Formatos aceitos: CSV",-1)),c("div",vC,[t[29]||(t[29]=c("span",{class:"example-label"},"Exemplo CSV",-1)),c("a",{href:`/local/offermanager/export_potential_users.php?offerclassid=${s.offerclassid}`,class:"example-csv"},"example.csv",8,bC)]),c("div",yC,[c("div",wC,[t[30]||(t[30]=c("label",null,"Delimitador do CSV",-1)),M(u,{modelValue:r.csvDelimiter,"onUpdate:modelValue":t[10]||(t[10]=h=>r.csvDelimiter=h),options:r.delimiterOptions,width:160},null,8,["modelValue","options"])]),c("div",EC,[t[31]||(t[31]=c("label",null,"Codificação",-1)),M(u,{modelValue:r.csvEncoding,"onUpdate:modelValue":t[11]||(t[11]=h=>r.csvEncoding=h),options:r.encodingOptions,width:160},null,8,["modelValue","options"])])])])])):Q("",!0),t[35]||(t[35]=c("div",{class:"form-info"},[c("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),c("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1))]))]),r.showResultAlerts?Q("",!0):(D(),S("div",CC,[c("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...h)=>a.handleSubmit&&a.handleSubmit(...h)),disabled:r.isSubmitting||!a.isFormValid},W(s.confirmButtonText),9,xC),c("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=h=>e.$emit("close"))},W(s.cancelButtonText),1)]))],2)])):Q("",!0),M(d,{show:r.showToast,message:r.toastMessage,type:r.toastType,duration:3e3},null,8,["show","message","type"])],64)}const OC=Fe(UE,[["render",DC],["__scopeId","data-v-cb610ebd"]]),MM="",SC={name:"EnrollmentDetailsModal",props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},courseName:{type:String,default:""}},emits:["close"],methods:{getEnrolmentMethod(e){if(console.log("EnrollmentDetailsModal - Método de inscrição recebido:",e),!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}}}},TC={class:"modal-header"},NC={key:0,class:"modal-body"},IC={class:"details-container"},AC={class:"detail-row"},MC={class:"detail-value"},PC={class:"detail-row"},kC={class:"detail-value"},VC={class:"detail-row"},RC={class:"detail-value"},FC={class:"detail-row"},LC={class:"detail-value"},UC={class:"detail-row"},BC={class:"detail-value"},$C={key:1,class:"modal-body no-data"},HC={class:"modal-footer"};function qC(e,t,s,i,r,a){return s.show?(D(),S("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=u=>e.$emit("close"))},[c("div",{class:"modal-container",onClick:t[2]||(t[2]=Pt(()=>{},["stop"]))},[c("div",TC,[t[5]||(t[5]=c("h3",{class:"modal-title"},"Informações da matrícula",-1)),c("button",{class:"modal-close",onClick:t[0]||(t[0]=u=>e.$emit("close"))},t[4]||(t[4]=[c("i",{class:"fas fa-times"},null,-1)]))]),s.user?(D(),S("div",NC,[c("div",IC,[c("div",AC,[t[6]||(t[6]=c("div",{class:"detail-label"},"Nome completo",-1)),c("div",MC,W(s.user.fullName),1)]),c("div",PC,[t[7]||(t[7]=c("div",{class:"detail-label"},"Curso",-1)),c("div",kC,W(s.courseName),1)]),c("div",VC,[t[8]||(t[8]=c("div",{class:"detail-label"},"Método de inscrição",-1)),c("div",RC,W(a.getEnrolmentMethod(s.user.enrol)),1)]),c("div",FC,[t[9]||(t[9]=c("div",{class:"detail-label"},"Estado",-1)),c("div",LC,[c("span",{class:fe(["status-tag",s.user.status===0?"status-ativo":"status-inativo"])},W(s.user.statusName),3)])]),c("div",UC,[t[10]||(t[10]=c("div",{class:"detail-label"},"Matrícula criada",-1)),c("div",BC,W(s.user.createdDate),1)])])])):(D(),S("div",$C,"Nenhum dado disponível")),c("div",HC,[c("button",{class:"btn btn-secondary",onClick:t[1]||(t[1]=u=>e.$emit("close"))}," Cancelar ")])])])):Q("",!0)}const WC=Fe(SC,[["render",qC],["__scopeId","data-v-030365c3"]]),PM="",jC={name:"EditEnrollmentModal",components:{CustomSelect:Xs},props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",enableStartDate:!0,startDateStr:"",startTimeStr:"00:00",enableEndDate:!1,endDateStr:"",endTimeStr:"00:00",validityPeriod:"unlimited"},statusOptions:[{value:0,label:"Ativo"},{value:1,label:"Suspenso"}],validityPeriodOptions:[{value:"unlimited",label:"Ilimitado"},...Array.from({length:365},(e,t)=>{const s=t+1;return{value:s.toString(),label:s===1?"1 dia":`${s} dias`}})]}},watch:{show(e){e&&this.user&&this.initializeForm()},user(e){e&&this.show&&this.initializeForm()}},methods:{getEnrolmentMethod(e){if(!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}},initializeForm(){if(!this.user)return;this.formData.status=this.user.status;const e=this.user.timestart,t=e?new Date(e*1e3):new Date;this.formData.startDateStr=this.formatDateForInput(t),this.formData.startTimeStr=this.formatTimeForInput(t),this.formData.enableStartDate=!0;const s=this.validityPeriodOptions.filter(i=>i.value!=="unlimited");if(this.user.timeend){const i=new Date(this.user.timeend*1e3);this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.enableEndDate=this.user.timeend>0;const a=i-t,u=Math.ceil(a/(1e3*60*60*24)),d=s.find(h=>parseInt(h.value)===u);this.formData.validityPeriod=d?d.value:"unlimited"}else{const i=new Date;i.setMonth(i.getMonth()+3),this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.validityPeriod="unlimited",this.formData.enableEndDate=!1}},handleValidityPeriodChange(){if(this.formData.validityPeriod!=="unlimited"){this.formData.enableEndDate=!1;const e=this.formData.enableStartDate&&this.formData.startDateStr?new Date(this.formData.startDateStr):new Date,t=parseInt(this.formData.validityPeriod),s=new Date(e);s.setDate(s.getDate()+t),this.formData.endDateStr=this.formatDateForInput(s),this.formData.endTimeStr=this.formData.startTimeStr}},handleEnableEndDateChange(){this.formData.enableEndDate&&(this.formData.validityPeriod="unlimited")},formatDateForInput(e){const t=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0");return`${t}-${s}-${i}`},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},async saveChanges(){var e;if((e=this.user)!=null&&e.offeruserenrolid)try{this.isSubmitting=!0;const t=Number(this.formData.status)||0,s=this.getStartTimestamp(),i=this.getEndTimestamp(s);if(s>i&&i!==0){this.$emit("error","A data de início da matrícula deve ser menor que a data de fim da matrícula.");return}await iE({offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i})?(this.$emit("success",{userId:this.user.id,offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i}),this.$emit("close")):this.$emit("error","Não foi possível editar a matrícula. Por favor, tente novamente.")}catch{this.$emit("error","Ocorreu um erro ao editar a matrícula. Por favor, tente novamente.")}finally{this.isSubmitting=!1}},getStartTimestamp(){if(this.formData.enableStartDate&&this.formData.startDateStr){const e=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr);return Math.floor(e.getTime()/1e3)}return 0},getEndTimestamp(e){if(this.formData.enableEndDate&&this.formData.endDateStr){const t=this.parseDateTime(this.formData.endDateStr,this.formData.endTimeStr);return Math.floor(t.getTime()/1e3)}if(this.formData.validityPeriod!=="unlimited"){const t=parseInt(this.formData.validityPeriod);if(this.formData.enableStartDate&&this.formData.startDateStr){const s=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr),i=new Date(s);return i.setDate(i.getDate()+t),Math.floor(i.getTime()/1e3)}}return 0},parseDateTime(e,t){const[s,i,r]=e.split("-").map(Number),[a,u]=t.split(":").map(Number);return new Date(s,i-1,r,a,u,0,0)}}},zC={class:"modal-header"},GC={class:"modal-title"},KC={class:"modal-body"},ZC={class:"enrollment-form"},YC={class:"form-row"},JC={class:"form-value"},QC={class:"form-row"},XC={class:"form-field"},ex={class:"select-wrapper"},tx={class:"form-row"},sx={class:"form-field date-time-field"},nx={class:"date-field"},rx={class:"time-field"},ox={class:"enable-checkbox"},ix={class:"form-row"},ax={class:"form-field"},lx={class:"select-wrapper"},ux={class:"form-row"},cx={class:"date-field"},dx=["disabled"],fx={class:"time-field"},hx=["disabled"],px={class:"enable-checkbox"},mx={class:"form-row"},gx={class:"form-value"},_x={class:"modal-footer"},vx={class:"footer-buttons"},bx=["disabled"];function yx(e,t,s,i,r,a){const u=G("CustomSelect");return s.show?(D(),S("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=d=>e.$emit("close"))},[c("div",{class:"modal-container",onClick:t[14]||(t[14]=Pt(()=>{},["stop"]))},[c("div",zC,[c("h3",GC," Editar matrícula de "+W(s.user?s.user.fullName:""),1),c("button",{class:"modal-close",onClick:t[0]||(t[0]=d=>e.$emit("close"))},t[16]||(t[16]=[c("i",{class:"fas fa-times"},null,-1)]))]),c("div",KC,[c("div",ZC,[c("div",YC,[t[17]||(t[17]=c("div",{class:"form-label"},"Método de inscrição",-1)),c("div",JC,W(a.getEnrolmentMethod(s.user&&s.user.enrol?s.user.enrol:"")),1)]),c("div",QC,[t[18]||(t[18]=c("div",{class:"form-label"},"Estado",-1)),c("div",XC,[c("div",ex,[M(u,{modelValue:r.formData.status,"onUpdate:modelValue":t[1]||(t[1]=d=>r.formData.status=d),options:r.statusOptions,width:120,class:"smaller-select"},null,8,["modelValue","options"])])])]),c("div",tx,[t[20]||(t[20]=c("div",{class:"form-label"},"Matrícula começa",-1)),c("div",sx,[c("div",nx,[at(c("input",{type:"date","onUpdate:modelValue":t[2]||(t[2]=d=>r.formData.startDateStr=d),class:"form-control",onChange:t[3]||(t[3]=(...d)=>e.handleStartDateChange&&e.handleStartDateChange(...d))},null,544),[[Yt,r.formData.startDateStr]])]),c("div",rx,[at(c("input",{type:"time","onUpdate:modelValue":t[4]||(t[4]=d=>r.formData.startTimeStr=d),class:"form-control",onChange:t[5]||(t[5]=(...d)=>e.handleStartTimeChange&&e.handleStartTimeChange(...d))},null,544),[[Yt,r.formData.startTimeStr]])]),c("div",ox,[at(c("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[6]||(t[6]=d=>r.formData.enableStartDate=d),class:"custom-checkbox"},null,512),[[Wi,r.formData.enableStartDate]]),t[19]||(t[19]=c("label",{for:"enable-start-date"},"Habilitar",-1))])])]),c("div",ix,[t[21]||(t[21]=c("div",{class:"form-label"},"Período de validade da matrícula",-1)),c("div",ax,[c("div",lx,[M(u,{modelValue:r.formData.validityPeriod,"onUpdate:modelValue":t[7]||(t[7]=d=>r.formData.validityPeriod=d),options:r.validityPeriodOptions,width:120,class:"smaller-select",onChange:a.handleValidityPeriodChange,disabled:r.formData.enableEndDate},null,8,["modelValue","options","onChange","disabled"])])])]),c("div",ux,[t[23]||(t[23]=c("div",{class:"form-label"},"Matrícula termina",-1)),c("div",{class:fe(["form-field date-time-field",{"disabled-inputs-only":!r.formData.enableEndDate}])},[c("div",cx,[at(c("input",{type:"date","onUpdate:modelValue":t[8]||(t[8]=d=>r.formData.endDateStr=d),class:"form-control",disabled:!r.formData.enableEndDate},null,8,dx),[[Yt,r.formData.endDateStr]])]),c("div",fx,[at(c("input",{type:"time","onUpdate:modelValue":t[9]||(t[9]=d=>r.formData.endTimeStr=d),class:"form-control",disabled:!r.formData.enableEndDate},null,8,hx),[[Yt,r.formData.endTimeStr]])]),c("div",px,[at(c("input",{type:"checkbox",id:"enable-enddate","onUpdate:modelValue":t[10]||(t[10]=d=>r.formData.enableEndDate=d),class:"custom-checkbox",onChange:t[11]||(t[11]=(...d)=>a.handleEnableEndDateChange&&a.handleEnableEndDateChange(...d))},null,544),[[Wi,r.formData.enableEndDate]]),t[22]||(t[22]=c("label",{for:"enable-enddate"},"Habilitar",-1))])],2)]),c("div",mx,[t[24]||(t[24]=c("div",{class:"form-label"},"Matrícula criada",-1)),c("div",gx,W(s.user&&s.user.createdDate?s.user.createdDate:"Não disponível"),1)])])]),c("div",_x,[t[25]||(t[25]=c("div",{class:"footer-spacer"},null,-1)),c("div",vx,[c("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...d)=>a.saveChanges&&a.saveChanges(...d)),disabled:r.isSubmitting},W(r.isSubmitting?"Salvando...":"Salvar mudanças"),9,bx),c("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=d=>e.$emit("close"))}," Cancelar ")])])])])):Q("",!0)}const wx=Fe(jC,[["render",yx],["__scopeId","data-v-24ba0708"]]),kM="",VM="",Ex={name:"BulkEditEnrollmentModal",components:{Pagination:gr,CustomTable:mr,CustomSelect:Xs},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",startDateStr:"",startTimeStr:"00:00",enableStartDate:!1,endDateStr:"",endTimeStr:"23:59",enableEndDate:!1},statusOptions:[{value:1,label:"Ativo"},{value:0,label:"Suspenso"}],currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,r)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<r[this.sortBy]?-1*a:i[this.sortBy]>r[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}},watch:{show(e){e&&this.initializeForm()}},methods:{initializeForm(){const e=new Date;this.formData={status:"1",startDateStr:this.formatDateForInput(e),startTimeStr:"00:00",enableStartDate:!1,endDateStr:this.formatDateForInput(e),endTimeStr:"23:59",enableEndDate:!1}},formatDateForInput(e){return e.toISOString().split("T")[0]},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},handleStartDateChange(){},handleStartTimeChange(){},handleEndDateChange(){},handleEndTimeChange(){},async saveChanges(){if(!this.users||this.users.length===0){console.error("Nenhum usuário selecionado"),this.$emit("error","Nenhum usuário selecionado para edição em lote.");return}try{this.isSubmitting=!0;const e=parseInt(this.formData.status);let t=0;if(this.formData.enableStartDate&&this.formData.startDateStr){const[a,u,d]=this.formData.startDateStr.split("-").map(Number),[h,_]=this.formData.startTimeStr.split(":").map(Number),p=new Date(a,u-1,d,h,_,0,0);t=Math.floor(p.getTime()/1e3);const g=p.getTimezoneOffset()*60;t+=g}let s=0;if(this.formData.enableEndDate&&this.formData.endDateStr){const[a,u,d]=this.formData.endDateStr.split("-").map(Number),[h,_]=this.formData.endTimeStr.split(":").map(Number),p=new Date(a,u-1,d,h,_,0,0);s=Math.floor(p.getTime()/1e3);const g=p.getTimezoneOffset()*60;s+=g}const i=this.users.filter(a=>a.offeruserenrolid).map(a=>a.offeruserenrolid);if(i.length===0){console.error("Nenhum ID de matrícula encontrado"),this.$emit("error","Não foi possível encontrar os IDs das matrículas dos usuários selecionados.");return}const r=await aE({offeruserenrolids:i,status:e,timestart:t,timeend:s});if(Array.isArray(r)&&r.length>0){const a=r.filter(h=>h.operation_status).length,u=r.length-a;let d="";if(a===r.length)d=`${a} matrícula(s) editada(s) com sucesso.`;else if(a>0)d=`${a} de ${r.length} matrícula(s) editada(s) com sucesso. ${u} matrícula(s) não puderam ser editadas.`;else{d="Nenhuma matrícula pôde ser editada.",this.$emit("error",d);return}this.$emit("success",{message:d,count:a,total:r.length}),this.$emit("close")}else console.error("Resposta inválida da API:",r),this.$emit("error","Não foi possível editar as matrículas. Por favor, tente novamente.")}catch(e){console.error("Erro ao salvar alterações:",e),this.$emit("error","Ocorreu um erro ao editar as matrículas. Por favor, tente novamente.")}finally{this.isSubmitting=!1}}}},Cx={class:"modal-header"},xx={class:"modal-body"},Dx={class:"enrollment-form"},Ox={class:"table-container"},Sx={class:"form-row"},Tx={class:"form-field"},Nx={class:"select-wrapper"},Ix={class:"form-row"},Ax={class:"form-field date-time-field"},Mx={class:"date-field"},Px=["disabled"],kx={class:"time-field"},Vx=["disabled"],Rx={class:"enable-checkbox"},Fx={class:"form-row"},Lx={class:"form-field date-time-field"},Ux={class:"date-field"},Bx=["disabled"],$x={class:"time-field"},Hx=["disabled"],qx={class:"enable-checkbox"},Wx={class:"modal-footer"},jx={class:"footer-buttons"},zx=["disabled"];function Gx(e,t,s,i,r,a){const u=G("CustomTable"),d=G("Pagination"),h=G("CustomSelect");return s.show?(D(),S("div",{key:0,class:"modal-backdrop",onClick:t[17]||(t[17]=_=>e.$emit("close"))},[c("div",{class:"modal-container",onClick:t[16]||(t[16]=Pt(()=>{},["stop"]))},[c("div",Cx,[t[19]||(t[19]=c("h3",{class:"modal-title"},"Edição de Matrículas em Lote",-1)),c("button",{class:"modal-close",onClick:t[0]||(t[0]=_=>e.$emit("close"))},t[18]||(t[18]=[c("i",{class:"fas fa-times"},null,-1)]))]),c("div",xx,[c("div",Dx,[c("div",null,[c("div",Ox,[M(u,{headers:r.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?at((D(),_t(d,{key:0,"current-page":r.currentPage,"onUpdate:currentPage":t[1]||(t[1]=_=>r.currentPage=_),"per-page":r.perPage,"onUpdate:perPage":t[2]||(t[2]=_=>r.perPage=_),total:s.users.length},null,8,["current-page","per-page","total"])),[[Kl,s.users.length>r.perPage]]):Q("",!0),t[20]||(t[20]=c("span",{class:"d-block w-100 border-bottom mt-4"},null,-1))]),c("div",Sx,[t[21]||(t[21]=c("div",{class:"form-label"},"Alterar o status",-1)),c("div",Tx,[c("div",Nx,[M(h,{modelValue:r.formData.status,"onUpdate:modelValue":t[3]||(t[3]=_=>r.formData.status=_),options:r.statusOptions,width:235,class:"smaller-select"},null,8,["modelValue","options"])])])]),c("div",Ix,[t[23]||(t[23]=c("div",{class:"form-label"},"Alterar data de início",-1)),c("div",Ax,[c("div",Mx,[at(c("input",{type:"date","onUpdate:modelValue":t[4]||(t[4]=_=>r.formData.startDateStr=_),class:"form-control",onChange:t[5]||(t[5]=(..._)=>a.handleStartDateChange&&a.handleStartDateChange(..._)),disabled:!r.formData.enableStartDate},null,40,Px),[[Yt,r.formData.startDateStr]])]),c("div",kx,[at(c("input",{type:"time","onUpdate:modelValue":t[6]||(t[6]=_=>r.formData.startTimeStr=_),class:"form-control",onChange:t[7]||(t[7]=(..._)=>a.handleStartTimeChange&&a.handleStartTimeChange(..._)),disabled:!r.formData.enableStartDate},null,40,Vx),[[Yt,r.formData.startTimeStr]])]),c("div",Rx,[at(c("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[8]||(t[8]=_=>r.formData.enableStartDate=_),class:"custom-checkbox"},null,512),[[Wi,r.formData.enableStartDate]]),t[22]||(t[22]=c("label",{for:"enable-start-date"},"Habilitar",-1))])])]),c("div",Fx,[t[25]||(t[25]=c("div",{class:"form-label"},"Alterar data de fim",-1)),c("div",Lx,[c("div",Ux,[at(c("input",{type:"date","onUpdate:modelValue":t[9]||(t[9]=_=>r.formData.endDateStr=_),class:"form-control",onChange:t[10]||(t[10]=(..._)=>a.handleEndDateChange&&a.handleEndDateChange(..._)),disabled:!r.formData.enableEndDate},null,40,Bx),[[Yt,r.formData.endDateStr]])]),c("div",$x,[at(c("input",{type:"time","onUpdate:modelValue":t[11]||(t[11]=_=>r.formData.endTimeStr=_),class:"form-control",onChange:t[12]||(t[12]=(..._)=>a.handleEndTimeChange&&a.handleEndTimeChange(..._)),disabled:!r.formData.enableEndDate},null,40,Hx),[[Yt,r.formData.endTimeStr]])]),c("div",qx,[at(c("input",{type:"checkbox",id:"enable-end-date","onUpdate:modelValue":t[13]||(t[13]=_=>r.formData.enableEndDate=_),class:"custom-checkbox"},null,512),[[Wi,r.formData.enableEndDate]]),t[24]||(t[24]=c("label",{for:"enable-end-date"},"Habilitar",-1))])])])])]),c("div",Wx,[t[26]||(t[26]=c("div",{class:"footer-spacer"},null,-1)),c("div",jx,[c("button",{class:"btn btn-primary",onClick:t[14]||(t[14]=(..._)=>a.saveChanges&&a.saveChanges(..._)),disabled:r.isSubmitting},W(r.isSubmitting?"Salvando...":"Salvar mudanças"),9,zx),c("button",{class:"btn btn-secondary",onClick:t[15]||(t[15]=_=>e.$emit("close"))}," Cancelar ")])])])])):Q("",!0)}const Kx=Fe(Ex,[["render",Gx],["__scopeId","data-v-92e8899f"]]),RM="",Zx={name:"BulkDeleteEnrollmentModal",components:{Pagination:gr,CustomSelect:Xs,CustomTable:mr},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","confirm","error"],data(){return{isSubmitting:!1,currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,r)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<r[this.sortBy]?-1*a:i[this.sortBy]>r[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}}},Yx={class:"modal-header"},Jx={class:"modal-body"},Qx={class:"enrollment-form"},Xx={class:"table-container"},eD={class:"modal-footer"},tD={class:"footer-buttons"},sD=["disabled"];function nD(e,t,s,i,r,a){const u=G("CustomTable"),d=G("Pagination");return s.show?(D(),S("div",{key:0,class:"modal-backdrop",onClick:t[6]||(t[6]=h=>e.$emit("close"))},[c("div",{class:"modal-container",onClick:t[5]||(t[5]=Pt(()=>{},["stop"]))},[c("div",Yx,[t[8]||(t[8]=c("h3",{class:"modal-title"},"Remoção de Matrículas",-1)),c("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[7]||(t[7]=[c("i",{class:"fas fa-times"},null,-1)]))]),c("div",Jx,[c("div",Qx,[c("div",Xx,[M(u,{headers:r.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?at((D(),_t(d,{key:0,"current-page":r.currentPage,"onUpdate:currentPage":t[1]||(t[1]=h=>r.currentPage=h),"per-page":r.perPage,"onUpdate:perPage":t[2]||(t[2]=h=>r.perPage=h),total:s.users.length},null,8,["current-page","per-page","total"])),[[Kl,s.users.length>r.perPage]]):Q("",!0)]),t[9]||(t[9]=c("div",{class:"text-center mt-5"},[c("h5",{class:"mt-1"}," Tem certeza de que deseja excluir essas inscrições de usuário? ")],-1))]),c("div",eD,[c("div",tD,[c("button",{class:"btn btn-primary",onClick:t[3]||(t[3]=h=>e.$emit("confirm")),disabled:r.isSubmitting},W(r.isSubmitting?"Removendo...":"Remover matrículas"),9,sD),c("button",{class:"btn btn-secondary",onClick:t[4]||(t[4]=h=>e.$emit("close"))}," Cancelar ")])])])])):Q("",!0)}const rD=Fe(Zx,[["render",nD],["__scopeId","data-v-37ea04c6"]]),FM="",oD={name:"BackButton",props:{label:{type:String,default:"Voltar"},route:{type:String,default:"/local/offermanager/"}},methods:{goBack(){this.$emit("click")}}};function iD(e,t,s,i,r,a){return D(),S("button",{class:"btn-back",onClick:t[0]||(t[0]=(...u)=>a.goBack&&a.goBack(...u))},[t[1]||(t[1]=c("i",{class:"fas fa-angle-left"},null,-1)),qe(" "+W(s.label),1)])}const zo=Fe(oD,[["render",iD],["__scopeId","data-v-c577f103"]]),LM="",aD={name:"UserAvatar",props:{imageUrl:{type:String,default:""},fullName:{type:String,required:!0},size:{type:Number,default:32}},computed:{hasImage(){return!!this.imageUrl},initials(){if(!this.fullName)return"";const e=this.fullName.split(" ").filter(i=>i.length>0);if(e.length===0)return"";if(e.length===1)return e[0].substring(0,2).toUpperCase();const t=e[0].charAt(0),s=e[e.length-1].charAt(0);return(t+s).toUpperCase()},backgroundColor(){const e=["#1976D2","#388E3C","#D32F2F","#7B1FA2","#FFA000","#0097A7","#E64A19","#5D4037","#455A64","#616161"];let t=0;for(let i=0;i<this.fullName.length;i++)t=this.fullName.charCodeAt(i)+((t<<5)-t);const s=Math.abs(t)%e.length;return e[s]},avatarStyle(){return{width:`${this.size}px`,height:`${this.size}px`,minWidth:`${this.size}px`,minHeight:`${this.size}px`}}}},lD=["src"];function uD(e,t,s,i,r,a){return D(),S("div",{class:"user-avatar",style:as(a.avatarStyle)},[a.hasImage?(D(),S("img",{key:0,src:s.imageUrl,alt:"Foto de perfil",class:"avatar-image"},null,8,lD)):(D(),S("div",{key:1,class:"avatar-initials",style:as({backgroundColor:a.backgroundColor})},W(a.initials),5))],4)}const cD=Fe(aD,[["render",uD],["__scopeId","data-v-eed19d8a"]]),UM="",dD={name:"RoleSelector",props:{userId:{type:[Number,String],required:!0},offeruserenrolid:{type:[Number,String],required:!0},currentRole:{type:[String,Array],required:!0},offerclassid:{type:[Number,String],required:!0}},data(){return{isEditing:!1,selectedRoles:[],roles:[],loading:!1,initialLoading:!0}},computed:{displayRoleNames(){return Array.isArray(this.currentRole)?this.currentRole.join(", "):String(this.currentRole||"")}},mounted(){this.loadRoles()},methods:{async loadRoles(){var e;this.initialLoading||(this.loading=!0);try{const t=await pu(parseInt(this.offerclassid)),s=((e=t==null?void 0:t.data)==null?void 0:e.offercourseid)||t.offercourseid;if(!s)throw new Error("offercourseid não encontrado");const i=await mu(s);this.roles=Array.isArray(i==null?void 0:i.data)?i.data:Array.isArray(i)?i:[];const r=await uE(this.offeruserenrolid);if(Array.isArray(r)&&r.length)this.selectedRoles=r.map(a=>a.id);else if(Array.isArray(this.currentRole))this.selectedRoles=this.roles.filter(a=>this.currentRole.includes(a.name)).map(a=>a.id);else if(this.currentRole){const a=this.roles.find(u=>u.name.toLowerCase()===String(this.currentRole).toLowerCase());a&&(this.selectedRoles=[a.id])}}catch{this.$emit("error","Não foi possível carregar papéis.")}finally{this.loading=!1,this.initialLoading=!1}},startEditing(){this.isEditing=!0,this.$nextTick(()=>{var e;return(e=this.$refs.roleSelect)==null?void 0:e.focus()})},cancelEdit(){this.isEditing=!1},close(){this.isEditing&&(this.isEditing=!1)},async saveRoles(){if(!this.selectedRoles.length){this.$emit("error","Selecione ao menos um papel.");return}this.loading=!0;try{const e=await cE(this.offeruserenrolid,this.selectedRoles.map(t=>parseInt(t)));if(e===!0||e&&e.error===!1||e&&e.success===!0){const t=this.roles.filter(s=>this.selectedRoles.includes(s.id)).map(s=>s.name);this.$emit("success",{userId:this.userId,offeruserenrolid:this.offeruserenrolid,roleids:this.selectedRoles,roleNames:t}),this.isEditing=!1,this.$emit("reload-table")}else throw new Error("Resposta inesperada do servidor: "+JSON.stringify(e))}catch(e){console.error("Erro ao salvar papéis:",e),this.$emit("error","Não foi possível salvar papéis.")}finally{this.loading=!1}}}},fD={class:"role-selector"},hD={key:1,class:"role-edit-wrapper"},pD={class:"role-edit-container"},mD={class:"select-wrapper"},gD=["value"],_D={class:"role-actions"},vD={key:2,class:"loading-overlay"};function bD(e,t,s,i,r,a){return D(),S("div",fD,[r.isEditing?(D(),S("div",hD,[c("div",pD,[c("div",mD,[at(c("select",{"onUpdate:modelValue":t[1]||(t[1]=u=>r.selectedRoles=u),class:"role-select",ref:"roleSelect",multiple:"",onClick:t[2]||(t[2]=Pt(()=>{},["stop"])),style:as({height:Math.max(4,r.roles.length)*25+"px"})},[(D(!0),S(Ie,null,lt(r.roles,u=>(D(),S("option",{key:u.id,value:u.id},W(u.name),9,gD))),128))],4),[[Jl,r.selectedRoles]])]),c("div",_D,[c("button",{class:"btn-save",onClick:t[3]||(t[3]=Pt((...u)=>a.saveRoles&&a.saveRoles(...u),["stop"])),title:"Salvar"},t[6]||(t[6]=[c("i",{class:"fas fa-check"},null,-1)])),c("button",{class:"btn-cancel",onClick:t[4]||(t[4]=Pt((...u)=>a.cancelEdit&&a.cancelEdit(...u),["stop"])),title:"Cancelar"},t[7]||(t[7]=[c("i",{class:"fas fa-times"},null,-1)]))])])])):(D(),S("div",{key:0,class:"role-display",onClick:t[0]||(t[0]=Pt((...u)=>a.startEditing&&a.startEditing(...u),["stop"]))},[c("span",null,W(a.displayRoleNames),1),t[5]||(t[5]=c("i",{class:"fas fa-pencil-alt edit-icon","aria-hidden":"true"},null,-1))])),r.loading&&r.isEditing?(D(),S("div",vD,t[8]||(t[8]=[c("div",{class:"spinner"},null,-1)]))):Q("",!0)])}const yD=Fe(dD,[["render",bD],["__scopeId","data-v-217c6284"]]),BM="",wD={name:"Enrollments",components:{CustomTable:mr,CustomSelect:Xs,HierarchicalSelect:vE,CustomInput:$r,CustomCheckbox:qo,CustomButton:_n,FilterSection:sp,FilterRow:ta,FilterGroup:sa,FilterActions:np,FilterTag:jo,FilterTags:ra,Pagination:gr,PageHeader:Hr,ConfirmationModal:na,Autocomplete:qr,EnrolmentModalNew:OC,EnrollmentDetailsModal:WC,Toast:$n,EditEnrollmentModal:wx,BulkEditEnrollmentModal:Kx,BulkDeleteEnrollmentModal:rD,BackButton:zo,UserAvatar:cD,RoleSelector:yD,LFLoading:Wo},data(){return{offerid:null,offerclassid:null,offercourseid:null,courseid:null,courseContextId:null,filteredUsers:[],nameOptions:[],cpfOptions:[],emailOptions:[],nameSearchInput:"",cpfSearchInput:"",emailSearchInput:"",showNameDropdown:!1,showCpfDropdown:!1,showEmailDropdown:!1,nameDebounceTimer:null,cpfDebounceTimer:null,emailDebounceTimer:null,tableHeaders:[{text:"",value:"select",sortable:!1,width:"50px"},{text:"NOME/SOBRENOME",value:"fullName",sortable:!0,width:"220px"},{text:"E-MAIL",value:"email",sortable:!0},{text:"CPF",value:"cpf",sortable:!0},{text:"PAPÉIS",value:"roles",sortable:!1},{text:"GRUPOS",value:"groups",sortable:!1},{text:"DATA INÍCIO DA MATRÍCULA",value:"startDate",sortable:!0},{text:"DATA FIM DA MATRÍCULA",value:"endDate",sortable:!0},{text:"PRAZO DE CONCLUSÃO",value:"deadline",sortable:!0},{text:"PROGRESSO",value:"progress",sortable:!1},{text:"SITUAÇÃO DE MATRÍCULA",value:"situation",sortable:!0},{text:"NOTA",value:"grade",sortable:!1},{text:"ESTADO",value:"status",sortable:!0}],enrolments:[],totalEnrolments:0,loading:!1,error:null,currentPage:1,perPage:10,sortBy:"fullName",sortDesc:!1,showBulkDeleteEnrollmentModal:!1,showEnrollmentModal:!1,selectedUser:null,showEnrolmentModal:!1,roleOptions:[],showEditEnrollmentModal:!1,showBulkEditEnrollmentModal:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,classDetails:{},selectedUsers:[],selectedBulkAction:"",selectedPageView:"usuarios_matriculados",pageViewOptions:[{value:"matriculas",label:"Matrículas",children:[{value:"usuarios_matriculados",label:"Usuários matriculados"}]},{value:"grupos",label:"Grupos",children:[{value:"grupos",label:"Grupos"},{value:"agrupamentos",label:"Agrupamentos"},{value:"visao_geral",label:"Visão geral"}]},{value:"permissoes",label:"Permissões",children:[{value:"permissoes",label:"Permissões"},{value:"outros_usuarios",label:"Outros usuários"},{value:"verificar_permissoes",label:"Verificar permissões"}]}]}},setup(){return{router:Jh()}},async created(){var t,s,i,r;if(this.offerclassid=this.offerclassid??this.$route.params.offerclassid,!this.offerclassid)throw new Error("ID da turma não foi definido.");this.offerclassid=parseInt(this.offerclassid);const e=await pu(this.offerclassid);if(e.error)throw new Error("Erro ao requisitar informações da turma");this.classDetails=e.data,this.offerid=parseInt((t=this.classDetails)==null?void 0:t.offerid),this.offercourseid=parseInt((s=this.classDetails)==null?void 0:s.offercourseid),this.corseid=(i=this.classDetails)==null?void 0:i.courseid,this.courseContextId=(r=this.classDetails)==null?void 0:r.course_context_id,await this.loadRoles(),await this.loadRegisteredUsers()},beforeUnmount(){this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer)},computed:{allSelected(){return this.enrolments.length>0&&this.selectedUsers.length===this.enrolments.length},someSelected(){return this.selectedUsers.length>0&&!this.allSelected},excludedUserIds(){return this.filteredUsers.map(e=>e.id||e.value)}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.selectedUsers=[],this.loadRegisteredUsers())},currentPage(e,t){e!==t&&this.loadRegisteredUsers()}},methods:{async loadRegisteredUsers(){this.loading=!0,this.error=null;let e=[];this.filteredUsers.length>0&&(e=this.excludedUserIds);const t={offerclassid:this.offerclassid,userids:e,page:this.currentPage,perpage:this.perPage,orderby:this.mapSortFieldToBackend(this.sortBy||"fullName"),direction:this.sortDesc?"DESC":"ASC"},s=await nE(t);if(s.data){const i=s.data;Array.isArray(i.enrolments)&&(this.enrolments=i.enrolments.map(r=>({id:r.userid,offeruserenrolid:r.offeruserenrolid,fullName:r.fullname,email:r.email,cpf:r.cpf,enrol:r.enrol,roles:this.formatRoles(r.roles),groups:r.groups,timecreated:r.timecreated,createdDate:this.formatDateTime(r.timecreated),timestart:r.timestart,timeend:r.timeend,startDate:this.formatDate(r.timestart),endDate:this.formatDate(r.timeend),deadline:r.enrolperiod,progress:this.formatProgress(r.progress),situation:r.situation,situationName:r.situation_name,grade:r.grade||"-",status:r.status,statusName:r.status!==void 0?r.status===0?"Ativo":"Suspenso":"-"})),this.totalEnrolments=i.total||this.enrolments.length)}else this.enrolments=[],this.totalEnrolments=0;this.loading=!1},formatDate(e){return!e||e===0?"-":new Date(e*1e3).toLocaleDateString("pt-BR")},formatDateTime(e,t={}){return!e||e===0?"-":(Object.keys(t).length===0&&(t={day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),new Date(e*1e3).toLocaleString("pt-BR",t))},formatProgress(e){return e==null?"-":Math.round(e)+"%"},formatRoles(e){return!e||e==="-"?"-":typeof e=="string"?e.split(",").join(", "):Array.isArray(e)&&e.length>0&&typeof e[0]=="object"&&e[0].name?e.map(t=>t.name).join(", "):Array.isArray(e)?e.join(", "):"-"},async loadNameOptions(e){if(!e||e.length<3){this.nameOptions=[],this.showNameDropdown=!1;return}try{const t=await gu({offerclassid:this.offerclassid,fieldstring:"name",searchstring:e,excludeduserids:this.excludedUserIds});t.data?(this.nameOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showNameDropdown=this.nameOptions.length>0):(this.nameOptions=[],this.showNameDropdown=!1)}catch{this.nameOptions=[],this.showNameDropdown=!1}},async loadCpfOptions(e){if(!e||e.length<3){this.cpfOptions=[],this.showCpfDropdown=!1;return}try{const t=await gu({offerclassid:this.offerclassid,fieldstring:"username",searchstring:e,excludeduserids:this.excludedUserIds});t.data?(this.cpfOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showCpfDropdown=this.cpfOptions.length>0):(this.cpfOptions=[],this.showCpfDropdown=!1)}catch{this.cpfOptions=[],this.showCpfDropdown=!1}},async loadEmailOptions(e){if(!e||e.length<3){this.emailOptions=[],this.showEmailDropdown=!1;return}try{const t=await gu({offerclassid:this.offerclassid,fieldstring:"email",searchstring:e,excludeduserids:this.excludedUserIds});!t.error&&t.data?(this.emailOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showEmailDropdown=this.emailOptions.length>0):(this.emailOptions=[],this.showEmailDropdown=!1)}catch{this.emailOptions=[],this.showEmailDropdown=!1}},handleNameInput(){this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.nameSearchInput.length>=3?this.nameDebounceTimer=setTimeout(()=>{this.loadNameOptions(this.nameSearchInput)},500):this.showNameDropdown=!1},handleCpfInput(){this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.cpfSearchInput.length>=3?this.cpfDebounceTimer=setTimeout(()=>{this.loadCpfOptions(this.cpfSearchInput)},500):this.showCpfDropdown=!1},handleEmailInput(){this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer),this.emailSearchInput.length>=3?this.emailDebounceTimer=setTimeout(()=>{this.loadEmailOptions(this.emailSearchInput)},500):this.showEmailDropdown=!1},selectNameOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"name"}),this.nameSearchInput="",this.showNameDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},selectCpfOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"cpf"}),this.cpfSearchInput="",this.showCpfDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},selectEmailOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"email"}),this.emailSearchInput="",this.showEmailDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},clearOptions(e){setTimeout(()=>{switch(e){case"name":this.nameOptions=[];break;case"cpf":this.cpfOptions=[];break;case"email":this.emailOptions=[];break;default:this.nameOptions=[],this.cpfOptions=[],this.emailOptions=[];break}},500)},removeFilter(e){const t=this.filteredUsers.findIndex(s=>s.id===e||s.value===e);t!==-1&&this.filteredUsers.splice(t,1),this.loadRegisteredUsers()},clearFilteredUsers(){this.filteredUsers=[],this.loadRegisteredUsers()},toggleSelectAll(){this.allSelected?this.selectedUsers=[]:this.selectedUsers=this.enrolments.map(e=>e.id)},toggleSelectUser(e){const t=this.selectedUsers.indexOf(e);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1)},isSelected(e){return this.selectedUsers.includes(e)},async handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,await this.loadRegisteredUsers()},mapSortFieldToBackend(e){return{fullName:"fullname",email:"email",cpf:"cpf",startDate:"startdate",endDate:"enddate",deadline:"enrolperiod",situation:"situation",status:"status"}[e]||"fullname"},addNewUser(){var e;if(this.classDetails&&((e=this.classDetails)==null?void 0:e.operational_cycle)===2){this.error="Não é possível matricular usuários em uma turma com ciclo operacional encerrado.";return}this.showEnrolmentModal=!0},closeEnrolmentModal(){this.showEnrolmentModal=!1},async goBack(){this.router.push({name:"offer.edit",params:{id:this.offerid}})},viewUserProfile(e){if(!e)return;const t=`/user/view.php?id=${e}&course=${this.courseid}`;window.location.href=t},async handlePageViewChange(e){let t=this.offerclassid,s=this.courseid,i=this.courseContextId;const r={usuarios_matriculados:`/local/offermanager/new-subscribed-users/${t}`,grupos:`/group/index.php?id=${s}`,agrupamentos:`/group/groupings.php?id=${s}`,visao_geral:`/user/index.php?id=${s}`,permissoes:`/admin/roles/permissions.php?contextid=${i}`,outros_usuarios:`/enrol/otherusers.php?id=${s}`,verificar_permissoes:`/admin/roles/check.php?contextid=${i}`};r[e]&&(window.location.href=r[e])},async handleEnrolmentSuccess(){await this.loadRegisteredUsers()},async loadRoles(){const e=await mu(this.offercourseid);if(e.error)throw new Error("Erro ao requisitar papéis do curso");this.roleOptions=e.data.map(t=>({value:t.id,label:t.name}))},showEnrollmentDetails(e){this.selectedUser={fullName:e.fullName,enrol:e.enrol||"Inscrições manuais",status:e.status||0,statusName:e.statusName||"Ativo",startDate:e.startDate||"Não disponível",createdDate:e.createdDate||e.startDate||"Não disponível"},this.showEnrollmentModal=!0},closeEnrollmentModal(){this.showEnrollmentModal=!1,this.selectedUser=null},closeEditEnrollmentModal(){this.showEditEnrollmentModal=!1,this.selectedUser=null},async handleEditEnrollmentSuccess(e){if(this.showSuccessMessage("Matrícula editada com sucesso."),e.roleid){let t=null;if(this.roleOptions&&this.roleOptions.length>0){const i=this.roleOptions.find(r=>r.value===String(e.roleid));i&&(t=i.name)}if(!t){await this.loadRegisteredUsers(),this.showEditEnrollmentModal=!1,this.selectedUser=null;return}const s=this.enrolments.findIndex(i=>i.id===e.userId);if(s!==-1){if(t&&(this.enrolments[s].roles=t),e.status!==void 0&&(this.enrolments[s].status=e.status,e.status===1?this.enrolments[s].statusName="Ativo":e.status===0&&(this.enrolments[s].statusName="Suspenso")),e.timestart){const i=new Date(e.timestart*1e3);this.enrolments[s].startDate=i.toLocaleDateString("pt-BR")}if(e.timeend){const i=new Date(e.timeend*1e3);this.enrolments[s].endDate=i.toLocaleDateString("pt-BR")}}else await this.loadRegisteredUsers()}else await this.loadRegisteredUsers();this.showEditEnrollmentModal=!1,this.selectedUser=null},handleEditEnrollmentError(e){this.showErrorMessage(e||"Não foi possível editar a matrícula. Por favor, tente novamente.")},handleRoleUpdateSuccess(e){this.showSuccessMessage("Papel atualizado com sucesso.");const t=this.enrolments.findIndex(s=>s.id===e.userId);t!==-1?this.enrolments[t].roles=e.roleName:this.reloadTable()},handleRoleUpdateError(e){this.showErrorMessage(e||"Ocorreu um erro ao atualizar o papel do usuário.")},reloadTable(){this.loadRegisteredUsers()},editUser(e){let t=null;e.roles&&e.roleid&&(t=e.roleid),this.selectedUser={id:e.id,offeruserenrolid:e.offeruserenrolid,fullName:e.fullName,enrol:e.enrol,status:e.status,statusName:e.statusName,roles:e.roles,roleid:t,startDate:e.startDate,timestart:e.timestart,timeend:e.timeend,createdDate:e.createdDate||"-"},this.showEditEnrollmentModal=!0},async confirmeBulkDeleteEnrollment(){this.loading=!0;const e=[];for(const i of this.selectedUsers){const r=this.enrolments.find(a=>a.id===i);r&&r.offeruserenrolid&&e.push(r.offeruserenrolid)}if(e.length===0){this.showErrorMessage("Não foi possível encontrar os IDs das matrículas. Por favor, tente novamente."),this.loading=!1;return}const t=`Processando exclusão de ${e.length} matrícula(s)...`;this.showSuccessMessage(t);const s=await lE(e);if(s&&s.length>0){const i=s.filter(a=>a.operation_status).length,r=s.length-i;i>0?(this.showSuccessMessage(`${i} matrícula(s) cancelada(s) com sucesso.${r>0?` ${r} matrícula(s) não puderam ser canceladas.`:""}`),await this.loadRegisteredUsers(),this.selectedUsers=[]):this.showErrorMessage("Não foi possível cancelar as matrículas. Por favor, tente novamente.")}else this.showSuccessMessage(`${e.length} matrícula(s) cancelada(s) com sucesso.`),await this.loadRegisteredUsers(),this.selectedUsers=[];this.showBulkDeleteEnrollmentModal=!1,this.loading=!1},handleBulkAction(){if(this.selectedBulkAction){if(this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para realizar esta ação."),this.selectedBulkAction="";return}switch(this.selectedBulkAction){case"message":this.sendMessage();break;case"note":this.writeNote();break;case"download_csv":this.downloadData("csv");break;case"download_xlsx":this.downloadData("xlsx");break;case"download_html":this.downloadData("html");break;case"download_json":this.downloadData("json");break;case"download_ods":this.downloadData("ods");break;case"download_pdf":this.downloadData("pdf");break;case"edit_enrolment":this.editEnrolments();break;case"delete_enrolment":this.bulkDeleteEnrollment();break}this.selectedBulkAction=""}},sendMessage(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para enviar mensagem.");return}this.showSendMessageModal(this.selectedUsers)},showSendMessageModal(e){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}window.require(["core_message/message_send_bulk"],t=>{if(typeof t.showModal!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}t.showModal(e,()=>{this.selectedBulkAction=""})},t=>{this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.")})},writeNote(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para escrever anotação.");return}this.showAddNoteModal(this.courseid,this.selectedUsers)},showAddNoteModal(e,t){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}window.require(["core_user/local/participants/bulkactions"],s=>{if(typeof s.showAddNote!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}const i={personal:"Pessoal",course:"Curso",site:"Site"};s.showAddNote(e,t,i,"").then(r=>(r.getRoot().on("hidden.bs.modal",()=>{this.selectedBulkAction=""}),r)).catch(r=>{this.showErrorMessage("Ocorreu um erro ao abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},s=>{this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},downloadData(e){this.selectedUsers.length!==0&&this.prepareLocalDownload(e)},prepareLocalDownload(e){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Nenhum usuário selecionado para download.");return}const t=[];if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(s=>typeof s=="number"))for(const s of this.selectedUsers){const i=this.enrolments.find(r=>r.id===s);if(i){const r={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(r)}}else for(const s of this.selectedUsers)if(typeof s=="number"){const i=this.enrolments.find(r=>r.id===s);if(i){const r={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(r)}}else if(typeof s=="object"&&s!==null){const i={ID:s.id||"",Nome:s.fullName||s.name||"",Email:s.email||"",CPF:s.cpf||"",Papéis:s.roles||"",Grupos:s.groups||"","Data de Início":s.startDate||"","Data de Término":s.endDate||"",Prazo:s.deadline||"",Progresso:s.progress||"",Situação:s.situationName||s.situation||"",Nota:s.grade||"",Estado:s.statusName||""};t.push(i)}if(t.length===0){this.showErrorMessage("Nenhum dado disponível para download.");return}switch(e){case"csv":this.downloadCSV(t);break;case"xlsx":this.downloadXLSX(t);break;case"html":this.downloadHTML(t);break;case"json":this.downloadJSON(t);break;case"ods":this.downloadODS(t);break;case"pdf":this.downloadPDF(t);break;default:this.showErrorMessage("Formato de download não suportado.");break}},downloadCSV(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=s.map(h=>h.replace(/([A-Z])/g," $1").replace(/^./,_=>_.toUpperCase()).trim()),r=t+[i.join(","),...e.map(h=>s.map(_=>{const p=h[_]||"";return`"${String(p).replace(/"/g,'""')}"`}).join(","))].join(`
`),a=new Blob([r],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),d=document.createElement("a");d.setAttribute("href",u),d.setAttribute("download","usuarios_matriculados.csv"),d.style.visibility="hidden",document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(u)},downloadXLSX(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=t+[s.join(","),...e.map(d=>s.map(h=>{const _=d[h]||"";return`"${String(_).replace(/"/g,'""')}"`}).join(","))].join(`
`),r=new Blob([i],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(r),u=document.createElement("a");u.setAttribute("href",a),u.setAttribute("download","usuarios_matriculados.csv"),u.style.visibility="hidden",document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(a),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser aberto no Excel.")},downloadHTML(e){if(e.length===0)return;const t=Object.keys(e[0]),s=[];for(let N=0;N<t.length;N++){const re=t[N].replace(/([A-Z])/g," $1").replace(/^./,J=>J.toUpperCase()).trim();s.push(re)}let i="";for(let N=0;N<s.length;N++)i+="<th>"+s[N]+"</th>";let r="";for(let N=0;N<e.length;N++){let re="<tr>";for(let J=0;J<t.length;J++)re+="<td>"+(e[N][t[J]]||"")+"</td>";re+="</tr>",r+=re}const a='<!DOCTYPE html><html><head><meta charset="utf-8"><title>Usuários Matriculados</title>',u="<style>body{font-family:Arial,sans-serif;margin:20px;color:#333}h1{color:#2c3e50;text-align:center;margin-bottom:20px}table{border-collapse:collapse;width:100%;margin-bottom:20px;box-shadow:0 0 20px rgba(0,0,0,.1)}th,td{border:1px solid #ddd;padding:12px;text-align:left}th{background-color:#3498db;color:white;font-weight:bold;text-transform:uppercase;font-size:14px}tr:nth-child(even){background-color:#f2f2f2}tr:hover{background-color:#e9f7fe}.footer{text-align:center;margin-top:20px;font-size:12px;color:#7f8c8d}</style>",d="</head><body><h1>Usuários Matriculados</h1>",h="<table><thead><tr>",_="</tr></thead><tbody>",p="</tbody></table>",g='<div class="footer">Gerado em '+new Date().toLocaleString()+"</div>",w="</body></html>",x=a+u+d+h+i+_+r+p+g+w,P=new Blob([x],{type:"text/html;charset=utf-8;"}),L=URL.createObjectURL(P),te=document.createElement("a");te.setAttribute("href",L),te.setAttribute("download","usuarios_matriculados.html"),te.style.visibility="hidden",document.body.appendChild(te),te.click(),document.body.removeChild(te),URL.revokeObjectURL(L),this.showSuccessMessage("Download concluído. O arquivo HTML foi salvo com sucesso.")},downloadJSON(e){if(e.length===0)return;const t=JSON.stringify(e,null,2),s=new Blob([t],{type:"application/json;charset=utf-8;"}),i=URL.createObjectURL(s),r=document.createElement("a");r.setAttribute("href",i),r.setAttribute("download","usuarios_matriculados.json"),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(i)},downloadODS(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]);let i=[];i.push(s.join(",")),e.forEach(h=>{const _=s.map(p=>{const g=h[p]||"";return'"'+String(g).replace(/"/g,'""')+'"'});i.push(_.join(","))});const r=t+i.join(`
`),a=new Blob([r],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),d=document.createElement("a");d.setAttribute("href",u),d.setAttribute("download","usuarios_matriculados.csv"),d.style.visibility="hidden",document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(u),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser importado no LibreOffice Calc para salvar como ODS.")},downloadPDF(e){e.length!==0&&(this.downloadHTML(e),this.showSuccessMessage("Página HTML aberta. Use a função de impressão do navegador (Ctrl+P) para salvar como PDF."))},editEnrolments(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para editar matrícula.");return}if(this.selectedUsers.length===1){const e=this.selectedUsers[0],t=this.enrolments.find(s=>s.id===e);t?this.editUser(t):this.showErrorMessage("Usuário não encontrado. Por favor, tente novamente.")}else this.showBulkEditEnrollmentModal=!0},async handleBulkEditEnrollmentSuccess(e){this.showSuccessMessage(e.message||"Matrículas editadas com sucesso."),await this.loadRegisteredUsers(),this.selectedUsers=[],this.showBulkEditEnrollmentModal=!1},handleBulkEditEnrollmentError(e){const t="Não foi possível editar as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},bulkDeleteEnrollment(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para excluir matrícula.");return}this.showBulkDeleteEnrollmentModal=!0},handleBulkDeleteEnrollmentError(e){const t="Não foi possível excluir as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},ED={id:"offer-manager-component",class:"offer-manager"},CD={style:{display:"flex","align-items":"center","margin-bottom":"20px",gap:"10px"}},xD={style:{width:"240px"}},DD={class:"filters-section mb-3"},OD={class:"row"},SD={class:"col-md-3"},TD={class:"filter-input-container position-relative"},ND={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},ID=["onClick"],AD={class:"col-md-3"},MD={class:"filter-input-container position-relative"},PD={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},kD=["onClick"],VD={class:"col-md-3"},RD={class:"filter-input-container position-relative"},FD={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},LD=["onClick"],UD={key:0,class:"my-4"},BD={key:1,class:"alert alert-danger"},$D={class:"table-container"},HD={class:"checkbox-container"},qD=["checked","indeterminate"],WD={class:"checkbox-container"},jD=["checked","onChange"],zD=["href","title"],GD={class:"user-name-link"},KD={class:"progress-container"},ZD={class:"progress-text"},YD={class:"status-container"},JD={class:"status-actions"},QD=["onClick"],XD=["onClick"],e2={class:"selected-users-actions"},t2={class:"bulk-actions-container"},s2={key:2,class:"bottom-enroll-button"};function n2(e,t,s,i,r,a){var be,Ae,ae;const u=G("BackButton"),d=G("PageHeader"),h=G("HierarchicalSelect"),_=G("CustomButton"),p=G("FilterTag"),g=G("FilterTags"),w=G("UserAvatar"),x=G("RoleSelector"),P=G("CustomTable"),L=G("Pagination"),te=G("EnrollmentDetailsModal"),N=G("EnrolmentModalNew"),re=G("EditEnrollmentModal"),J=G("BulkEditEnrollmentModal"),we=G("BulkDeleteEnrollmentModal"),X=G("LFLoading"),pe=G("Toast");return D(),S("div",ED,[M(d,{title:"Usuários matriculados"},{actions:Se(()=>[M(u,{onClick:a.goBack},null,8,["onClick"])]),_:1}),c("div",CD,[c("div",xD,[M(h,{modelValue:r.selectedPageView,"onUpdate:modelValue":t[0]||(t[0]=A=>r.selectedPageView=A),options:r.pageViewOptions,onNavigate:a.handlePageViewChange},null,8,["modelValue","options","onNavigate"])]),!r.classDetails||((be=r.classDetails)==null?void 0:be.operational_cycle)!==2?(D(),_t(_,{key:0,variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])):Q("",!0)]),c("div",DD,[c("div",OD,[c("div",SD,[c("div",TD,[t[21]||(t[21]=c("label",{for:"name-filter",class:"form-label text-muted small"},"Filtrar por nome",-1)),at(c("input",{id:"name-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[1]||(t[1]=A=>r.nameSearchInput=A),onInput:t[2]||(t[2]=(...A)=>a.handleNameInput&&a.handleNameInput(...A)),onFocus:t[3]||(t[3]=A=>r.showNameDropdown=r.nameOptions.length>0),onBlur:t[4]||(t[4]=A=>a.clearOptions("name"))},null,544),[[Yt,r.nameSearchInput]]),r.showNameDropdown&&r.nameOptions.length>0?(D(),S("div",ND,[(D(!0),S(Ie,null,lt(r.nameOptions,A=>(D(),S("button",{key:A.id,type:"button",class:"dropdown-item",onClick:Ee=>a.selectNameOption(A)},W(A.label),9,ID))),128))])):Q("",!0)])]),c("div",AD,[c("div",MD,[t[22]||(t[22]=c("label",{for:"cpf-filter",class:"form-label text-muted small"},"Filtrar por CPF",-1)),at(c("input",{id:"cpf-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[5]||(t[5]=A=>r.cpfSearchInput=A),onInput:t[6]||(t[6]=(...A)=>a.handleCpfInput&&a.handleCpfInput(...A)),onFocus:t[7]||(t[7]=A=>r.showCpfDropdown=r.cpfOptions.length>0),onBlur:t[8]||(t[8]=A=>a.clearOptions("cpf"))},null,544),[[Yt,r.cpfSearchInput]]),r.showCpfDropdown&&r.cpfOptions.length>0?(D(),S("div",PD,[(D(!0),S(Ie,null,lt(r.cpfOptions,A=>(D(),S("button",{key:A.id,type:"button",class:"dropdown-item",onClick:Ee=>a.selectCpfOption(A)},W(A.label),9,kD))),128))])):Q("",!0)])]),c("div",VD,[c("div",RD,[t[23]||(t[23]=c("label",{for:"email-filter",class:"form-label text-muted small"},"Filtrar por E-mail",-1)),at(c("input",{id:"email-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[9]||(t[9]=A=>r.emailSearchInput=A),onInput:t[10]||(t[10]=(...A)=>a.handleEmailInput&&a.handleEmailInput(...A)),onFocus:t[11]||(t[11]=A=>r.showEmailDropdown=r.emailOptions.length>0),onBlur:t[12]||(t[12]=A=>a.clearOptions("email"))},null,544),[[Yt,r.emailSearchInput]]),r.showEmailDropdown&&r.emailOptions.length>0?(D(),S("div",FD,[(D(!0),S(Ie,null,lt(r.emailOptions,A=>(D(),S("button",{key:A.id,type:"button",class:"dropdown-item",onClick:Ee=>a.selectEmailOption(A)},W(A.label),9,LD))),128))])):Q("",!0)])])])]),M(g,null,{default:Se(()=>[(D(!0),S(Ie,null,lt(r.filteredUsers,A=>(D(),_t(p,{key:A.id,onRemove:Ee=>a.removeFilter(A.id||A.value)},{default:Se(()=>[qe(W(A.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1}),r.filteredUsers.length>0?(D(),S("div",UD,[c("button",{type:"button",class:"btn btn-secondary",onClick:t[13]||(t[13]=(...A)=>a.clearFilteredUsers&&a.clearFilteredUsers(...A))}," Limpar ")])):Q("",!0),r.error?(D(),S("div",BD,[t[24]||(t[24]=c("i",{class:"fas fa-exclamation-circle"},null,-1)),qe(" "+W(r.error),1)])):Q("",!0),c("div",$D,[M(P,{headers:r.tableHeaders,items:r.enrolments,"sort-by":r.sortBy,"sort-desc":r.sortDesc,onSort:a.handleTableSort},{"header-select":Se(()=>[c("div",HD,[c("input",{type:"checkbox",checked:a.allSelected,indeterminate:a.someSelected&&!a.allSelected,onChange:t[14]||(t[14]=(...A)=>a.toggleSelectAll&&a.toggleSelectAll(...A)),class:"custom-checkbox"},null,40,qD)])]),"item-select":Se(({item:A})=>[c("div",WD,[c("input",{type:"checkbox",checked:a.isSelected(A.id),onChange:Ee=>a.toggleSelectUser(A.id),class:"custom-checkbox"},null,40,jD)])]),"item-fullName":Se(({item:A})=>[c("a",{class:"user-name-container",href:`/user/view.php?id=${A.id}`,title:"Ver perfil de "+A.fullName},[M(w,{"full-name":A.fullName,size:36},null,8,["full-name"]),c("span",GD,W(A.fullName),1)],8,zD)]),"item-email":Se(({item:A})=>[qe(W(A.email),1)]),"item-cpf":Se(({item:A})=>[qe(W(A.cpf),1)]),"item-roles":Se(({item:A})=>[M(x,{userId:A.id,offeruserenrolid:A.offeruserenrolid,currentRole:A.roles,offerclassid:r.offerclassid,onSuccess:a.handleRoleUpdateSuccess,onError:a.handleRoleUpdateError,onReloadTable:a.reloadTable},null,8,["userId","offeruserenrolid","currentRole","offerclassid","onSuccess","onError","onReloadTable"])]),"item-groups":Se(({item:A})=>[qe(W(A.groups),1)]),"item-startDate":Se(({item:A})=>[qe(W(A.startDate),1)]),"item-endDate":Se(({item:A})=>[qe(W(A.endDate),1)]),"item-deadline":Se(({item:A})=>[qe(W(A.deadline),1)]),"item-progress":Se(({item:A})=>[c("div",KD,[c("div",{class:"progress-bar",style:as({width:A.progress})},null,4),c("span",ZD,W(A.progress),1)])]),"item-situation":Se(({item:A})=>[qe(W(A.situationName),1)]),"item-grade":Se(({item:A})=>[qe(W(A.grade),1)]),"item-status":Se(({item:A})=>[c("div",YD,[c("span",{class:fe(["status-tag badge",A.status===0?"badge-success":"badge-danger"])},W(A.statusName),3),c("div",JD,[c("button",{class:"btn-information",onClick:Ee=>a.showEnrollmentDetails(A),title:"Informações da matrícula"},t[25]||(t[25]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[c("circle",{cx:"12",cy:"12",r:"10"}),c("line",{x1:"12",y1:"16",x2:"12",y2:"12"}),c("line",{x1:"12",y1:"8",x2:"12.01",y2:"8"})],-1)]),8,QD),c("button",{class:"btn-settings",onClick:Ee=>a.editUser(A),title:"Editar matrícula"},t[26]||(t[26]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[c("circle",{cx:"12",cy:"12",r:"3"}),c("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"})],-1)]),8,XD)])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),M(L,{"current-page":r.currentPage,"onUpdate:currentPage":t[15]||(t[15]=A=>r.currentPage=A),"per-page":r.perPage,"onUpdate:perPage":t[16]||(t[16]=A=>r.perPage=A),total:r.totalEnrolments,loading:r.loading},null,8,["current-page","per-page","total","loading"]),c("div",e2,[c("div",t2,[t[28]||(t[28]=c("label",{for:"bulk-actions"},"Com usuários selecionados...",-1)),at(c("select",{id:"bulk-actions",class:"form-control bulk-select","onUpdate:modelValue":t[17]||(t[17]=A=>r.selectedBulkAction=A),onChange:t[18]||(t[18]=(...A)=>a.handleBulkAction&&a.handleBulkAction(...A))},t[27]||(t[27]=[rv('<option value="" data-v-d1a3ec34>Escolher...</option><optgroup label="Comunicação" data-v-d1a3ec34><option value="message" data-v-d1a3ec34>Enviar uma mensagem</option><option value="note" data-v-d1a3ec34>Escrever uma nova anotação</option></optgroup><optgroup label="Baixar dados da tabela como:" data-v-d1a3ec34><option value="download_csv" data-v-d1a3ec34> Valores separados por vírgula (.csv) </option><option value="download_xlsx" data-v-d1a3ec34>Microsoft excel (.xlsx)</option><option value="download_html" data-v-d1a3ec34>Tabela HTML</option><option value="download_json" data-v-d1a3ec34> JavaScript Object Notation (.json) </option><option value="download_ods" data-v-d1a3ec34>OpenDocument (.ods)</option><option value="download_pdf" data-v-d1a3ec34> Formato de documento portável (.pdf) </option></optgroup><optgroup label="Inscrições" data-v-d1a3ec34><option value="edit_enrolment" data-v-d1a3ec34> Editar matrículas de usuários selecionados </option><option value="delete_enrolment" data-v-d1a3ec34> Excluir matrículas de usuários selecionados </option></optgroup>',4)]),544),[[Jl,r.selectedBulkAction]])])]),!r.classDetails||((Ae=r.classDetails)==null?void 0:Ae.operational_cycle)!==2?(D(),S("div",s2,[M(_,{variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])])):Q("",!0),M(te,{show:r.showEnrollmentModal,user:r.selectedUser,"course-name":((ae=r.classDetails)==null?void 0:ae.course_fullname)||"",onClose:a.closeEnrollmentModal},null,8,["show","user","course-name","onClose"]),M(N,{show:r.showEnrolmentModal,offerclassid:r.offerclassid,roles:r.roleOptions,onClose:a.closeEnrolmentModal,onSuccess:a.handleEnrolmentSuccess},null,8,["show","offerclassid","roles","onClose","onSuccess"]),M(re,{show:r.showEditEnrollmentModal,user:r.selectedUser,offerclassid:r.offerclassid,onClose:a.closeEditEnrollmentModal,onSuccess:a.handleEditEnrollmentSuccess,onError:a.handleEditEnrollmentError},null,8,["show","user","offerclassid","onClose","onSuccess","onError"]),M(J,{show:r.showBulkEditEnrollmentModal,users:r.selectedUsers.map(A=>r.enrolments.find(Ee=>Ee.id===A)).filter(Boolean),offerclassid:r.offerclassid,onClose:t[19]||(t[19]=A=>this.showBulkEditEnrollmentModal=!1),onSuccess:a.handleBulkEditEnrollmentSuccess,onError:a.handleBulkEditEnrollmentError},null,8,["show","users","offerclassid","onSuccess","onError"]),M(we,{show:r.showBulkDeleteEnrollmentModal,users:r.selectedUsers.map(A=>r.enrolments.find(Ee=>Ee.id===A)).filter(Boolean),offerclassid:r.offerclassid,onClose:t[20]||(t[20]=A=>r.showBulkDeleteEnrollmentModal=!1),onConfirm:a.confirmeBulkDeleteEnrollment,onError:a.handleBulkDeleteEnrollmentError},null,8,["show","users","offerclassid","onConfirm","onError"]),M(X,{"is-loading":r.loading},null,8,["is-loading"]),M(pe,{show:r.showToast,message:r.toastMessage,type:r.toastType,duration:3e3},null,8,["show","message","type"])])}const r2=Fe(wD,[["render",n2],["__scopeId","data-v-d1a3ec34"]]),oa={data(){return{showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null}},methods:{showToastMessage(e,t="success"){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType=t,this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showSuccessMessage(e){this.showToastMessage(e,"success")},showErrorMessage(e){this.showToastMessage(e,"error")}}},$M="",o2={name:"HelpIcon",props:{title:{type:String,default:"Ajuda"},text:{type:String,required:!0},postion:{type:String,default:"right"}},computed:{content(){return`<div class="no-overflow"><p class="mb-0">${this.text}</p></div>`}}},i2=["data-content","aria-label"],a2=["title","aria-label"];function l2(e,t,s,i,r,a){return D(),S("a",{class:"btn btn-link p-0","data-container":"body","data-toggle":"popover","data-placement":"auto","data-content":a.content,"data-html":"true",tabindex:"0","data-trigger":"focus","aria-label":s.title,role:"button"},[c("i",{class:"icon fa fa-question-circle text-info fa-fw",title:s.title,"aria-label":s.title,role:"img"},null,8,a2)],8,i2)}const _u=Fe(o2,[["render",l2],["__scopeId","data-v-6eb219ea"]]),HM="",u2={name:"TextEditor",props:{modelValue:{type:String,default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite o conteúdo aqui..."},rows:{type:Number,default:5},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],data(){return{showHtmlSource:!1,htmlContent:this.modelValue}},mounted(){this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.modelValue)},watch:{modelValue:{handler(e){this.showHtmlSource?this.htmlContent=e:this.$refs.editableContent&&this.$refs.editableContent.textContent!==e&&(this.$refs.editableContent.textContent=e)},immediate:!0}},methods:{applyFormat(e,t=null){this.showHtmlSource||(document.execCommand(e,!1,t),this.updateContent())},insertLink(){if(this.showHtmlSource)return;const e=prompt("Digite a URL do link:","http://");e&&this.applyFormat("createLink",e)},insertImage(){if(this.showHtmlSource)return;const e=prompt("Digite a URL da imagem:","http://");e&&this.applyFormat("insertImage",e)},toggleHtmlView(){this.showHtmlSource?this.$nextTick(()=>{this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.htmlContent)}):this.htmlContent=this.$refs.editableContent.textContent,this.showHtmlSource=!this.showHtmlSource},updateContent(){if(!this.showHtmlSource&&this.$refs.editableContent){const e=this.$refs.editableContent.textContent;this.$emit("update:modelValue",e)}},updateHtmlContent(){this.showHtmlSource&&this.$emit("update:modelValue",this.htmlContent)}}},c2={class:"text-editor-container"},d2={class:"editor-toolbar"},f2={class:"toolbar-group"},h2=["disabled"],p2=["disabled"],m2=["disabled"],g2=["disabled"],_2={class:"toolbar-group"},v2=["disabled"],b2=["disabled"],y2=["contenteditable"],w2=["rows","placeholder","disabled"];function E2(e,t,s,i,r,a){return D(),S("div",c2,[s.label?(D(),S("label",{key:0,class:fe(["filter-label",{disabled:s.disabled}])},W(s.label),3)):Q("",!0),c("div",{class:fe(["editor-container",{disabled:s.disabled}])},[c("div",d2,[c("div",f2,[c("button",{class:"btn-editor",onClick:t[0]||(t[0]=u=>!s.disabled&&a.applyFormat("bold")),title:"Negrito",disabled:s.disabled},t[10]||(t[10]=[c("i",{class:"fas fa-bold"},null,-1)]),8,h2),c("button",{class:"btn-editor",onClick:t[1]||(t[1]=u=>!s.disabled&&a.applyFormat("italic")),title:"Itálico",disabled:s.disabled},t[11]||(t[11]=[c("i",{class:"fas fa-italic"},null,-1)]),8,p2),c("button",{class:"btn-editor",onClick:t[2]||(t[2]=u=>!s.disabled&&a.applyFormat("underline")),title:"Sublinhado",disabled:s.disabled},t[12]||(t[12]=[c("i",{class:"fas fa-underline"},null,-1)]),8,m2),c("button",{class:"btn-editor",onClick:t[3]||(t[3]=u=>!s.disabled&&a.applyFormat("strikethrough")),title:"Tachado",disabled:s.disabled},t[13]||(t[13]=[c("i",{class:"fas fa-strikethrough"},null,-1)]),8,g2)]),t[16]||(t[16]=c("div",{class:"toolbar-divider"},null,-1)),c("div",_2,[c("button",{class:"btn-editor",onClick:t[4]||(t[4]=u=>!s.disabled&&a.applyFormat("insertUnorderedList")),title:"Lista não ordenada",disabled:s.disabled},t[14]||(t[14]=[c("i",{class:"fas fa-list-ul"},null,-1)]),8,v2),c("button",{class:"btn-editor",onClick:t[5]||(t[5]=u=>!s.disabled&&a.applyFormat("insertOrderedList")),title:"Lista ordenada",disabled:s.disabled},t[15]||(t[15]=[c("i",{class:"fas fa-list-ol"},null,-1)]),8,b2)])]),r.showHtmlSource?at((D(),S("textarea",{key:1,"onUpdate:modelValue":t[8]||(t[8]=u=>r.htmlContent=u),class:"editor-textarea",rows:s.rows,placeholder:s.placeholder,onInput:t[9]||(t[9]=(...u)=>a.updateHtmlContent&&a.updateHtmlContent(...u)),disabled:s.disabled},null,40,w2)),[[Yt,r.htmlContent]]):(D(),S("div",{key:0,class:"editor-content",contenteditable:!s.disabled,onInput:t[6]||(t[6]=(...u)=>a.updateContent&&a.updateContent(...u)),onKeyup:t[7]||(t[7]=(...u)=>a.updateContent&&a.updateContent(...u)),ref:"editableContent"},null,40,y2))],2)])}const vu=Fe(u2,[["render",E2],["__scopeId","data-v-57cfe760"]]),qM="",C2={name:"OfferForm",mixins:[oa],components:{Toast:$n,HelpIcon:_u,TextEditor:vu,CustomInput:$r,CustomSelect:Xs,Autocomplete:qr,CustomCheckbox:qo,ConfirmationModal:na},props:{offer:{type:Object,required:!0},isEditing:{type:Boolean,required:!0}},emits:["update:offer","validate"],data(){return{localOffer:{...this.offer},showOfferStatusModal:!1,typeOptions:[],typeEnabled:!1,audienceOptions:[],formErrors:{name:{hasError:!1,message:"Nome da oferta é obrigatório"},audiences:{hasError:!1,message:"Selecione pelo menos um público-alvo"}}}},async created(){this.typeEnabled&&await this.getTypes(),await this.getAudiences()},watch:{offer:{handler(e){var t;if(JSON.stringify(e)!==JSON.stringify(this.localOffer)){const s=(t=e==null?void 0:e.audiences)==null?void 0:t.map(i=>({value:i,label:""}));this.localOffer={...e,audiences:s}}},deep:!0,immediate:!0},localOffer:{handler(e){var s;const t=(s=e==null?void 0:e.audiences)==null?void 0:s.map(i=>i.value);e={...e,audiences:t},JSON.stringify(e)!==JSON.stringify(this.offer)&&this.$emit("update:offer",{...e,audiences:t})},deep:!0}},methods:{async getTypes(){try{const e=await getTypeOptions(),{enabled:t,types:s,default:i}=e;this.typeEnabled=!!t,t&&Array.isArray(s)&&(this.typeOptions=s.map(r=>({value:r,label:r.charAt(0).toUpperCase()+r.slice(1)})))}catch(e){this.showErrorMessage(e.message||"Erro ao carregar opções de tipos.")}},async getAudiences(){this.loading=!0;try{const e=await ty("");this.audienceOptions=e.map(t=>({value:t.id,label:t.name.toUpperCase()}))}catch(e){console.log(e),this.showErrorMessage("Erro ao carregar públicos-alvo.")}finally{this.loading=!1}},handleStatusChange(e){if(console.log(e),!e){this.showOfferStatusModal=!0;return}this.localOffer.status=!0,this.validateForm()},confirmInactivateStatus(){this.showOfferStatusModal=!1,this.localOffer.status=!1,this.validateForm()},validateForm(){let e=!0;return Object.keys(this.formErrors).forEach(t=>{this.isValidField(t)||(e=!1)}),this.$emit("validate",e),e},isValidField(e){var t;switch(e){case"name":this.formErrors.name.hasError=!this.localOffer.name;break;case"audiences":this.formErrors.audiences.hasError=!((t=this.localOffer)!=null&&t.audiences)||this.localOffer.audiences.length===0;break}return!this.formErrors[e].hasError}}},x2={class:"form-row mb-3"},D2={class:"form-group"},O2={class:"input-container"},S2={key:0,class:"form-group"},T2={class:"input-container"},N2={key:1,class:"form-group"},I2={class:"input-container pt-4"},A2={class:"form-row mb-3"},M2={class:"form-group"},P2={class:"label-container"},k2={class:"label-with-help"},V2={class:"input-container"},R2={class:"form-group text-editor-container"},F2={class:"limited-width-editor"};function L2(e,t,s,i,r,a){const u=G("CustomInput"),d=G("CustomSelect"),h=G("CustomCheckbox"),_=G("HelpIcon"),p=G("Autocomplete"),g=G("TextEditor"),w=G("ConfirmationModal"),x=G("Toast");return D(),S("div",null,[c("div",x2,[c("div",D2,[t[9]||(t[9]=c("div",{class:"label-container"},[c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Nome da Oferta"),c("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})])],-1)),c("div",O2,[M(u,{modelValue:r.localOffer.name,"onUpdate:modelValue":t[0]||(t[0]=P=>r.localOffer.name=P),placeholder:"Oferta 0001",width:280,required:"","has-error":r.formErrors.name.hasError,"error-message":r.formErrors.name.message,onValidate:t[1]||(t[1]=P=>a.validateForm())},null,8,["modelValue","has-error","error-message"])])]),r.typeEnabled?(D(),S("div",S2,[t[10]||(t[10]=c("div",{class:"label-container"},[c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Tipo da oferta")])],-1)),c("div",T2,[M(d,{modelValue:r.localOffer.type,"onUpdate:modelValue":t[2]||(t[2]=P=>r.localOffer.type=P),options:r.typeOptions,width:280},null,8,["modelValue","options"])])])):Q("",!0),s.isEditing?(D(),S("div",N2,[c("div",I2,[M(h,{modelValue:r.localOffer.status,"onUpdate:modelValue":t[3]||(t[3]=P=>r.localOffer.status=P),id:"status",label:"Ativar oferta",confirmBeforeChange:!0,onRequestChange:a.handleStatusChange},null,8,["modelValue","onRequestChange"])])])):Q("",!0)]),c("div",A2,[c("div",M2,[c("div",P2,[c("div",k2,[t[11]||(t[11]=c("label",{class:"form-label"},"Público-alvo",-1)),t[12]||(t[12]=c("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),M(_,{title:"Ajuda com público-alvo",text:`Para atribuir o público-alvo é necessário que o mesmo seja previamente criado no 'Gerenciador de Público-Alvo'.<br><br>
                      Após a exclusão do ‘atributo’ de um público-alvo, os usuários que já estiverem inscritos em um curso permanecerão matriculados, 
                      mantendo acesso ao conteúdo normalmente. <br><br>
                      A exclusão impactará apenas a exibição do curso para novos usuários dentro desse público-alvo.`})])]),c("div",V2,[M(p,{class:"autocomplete-audiences",modelValue:r.localOffer.audiences,"onUpdate:modelValue":[t[4]||(t[4]=P=>r.localOffer.audiences=P),t[5]||(t[5]=P=>a.validateForm())],items:r.audienceOptions,placeholder:"Pesquisar público-alvo...","input-max-width":218,required:!0,"show-all-option":!0,"has-error":r.formErrors.audiences.hasError,"error-message":r.formErrors.audiences.message},null,8,["modelValue","items","has-error","error-message"])])])]),c("div",R2,[t[13]||(t[13]=c("div",{class:"label-container"},[c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Descrição da oferta")])],-1)),c("div",F2,[M(g,{modelValue:r.localOffer.description,"onUpdate:modelValue":[t[6]||(t[6]=P=>r.localOffer.description=P),t[7]||(t[7]=P=>a.validateForm())],placeholder:"Digite a descrição da oferta aqui...",rows:5},null,8,["modelValue"])])]),M(w,{show:r.showOfferStatusModal,size:"md",title:"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma: ","list-title":"Comportamento para os cursos, turmas e matrículas:","list-items":["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."],"confirm-button-text":"Inativar oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[8]||(t[8]=P=>r.showOfferStatusModal=!1),onConfirm:a.confirmInactivateStatus},null,8,["show","onConfirm"]),M(x,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])])}const bu=Fe(C2,[["render",L2],["__scopeId","data-v-dc806bab"]]),U2={name:"OfferEdit",mixins:[oa],components:{Toast:$n,LFLoading:Wo,OfferForm:bu,PageHeader:Hr,BackButton:zo,CustomButton:_n},data(){return{loading:!1,offer:{id:null,name:"",type:"",description:"",status:!1,audiences:[]},showWarning:!0,isEditing:!1,isValidForm:!1}},methods:{async saveOffer(){if(this.loading=!0,!this.isValidForm){this.loading=!1;return}try{const e=await Xh(this.offer);if(e.id){const t=e.id;this.showSuccessMessage("Oferta criada com sucesso!"),this.$router.push({name:"offer.edit",params:{id:t}})}}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},goBack(){this.$router.push({name:"offer.index"})}}},B2={id:"create-offer-component",class:"create-offer"},$2={key:0,class:"alert alert-primary"},H2={class:"section-container mt-3"},q2={key:0,class:"message-container"},W2={class:"d-flex justify-content-between align-items-center"},j2={class:"actions-container offer-actions"};function z2(e,t,s,i,r,a){const u=G("BackButton"),d=G("PageHeader"),h=G("OfferForm"),_=G("CustomButton"),p=G("LFLoading"),g=G("Toast");return D(),S("div",B2,[M(d,{title:"Adicionar Oferta"},{actions:Se(()=>[M(u,{onClick:a.goBack},null,8,["onClick"])]),_:1}),r.showWarning?(D(),S("div",$2,t[2]||(t[2]=[c("i",{class:"fas fa-exclamation-triangle"},null,-1),qe(" Para que uma instância de oferta seja ativada e disponibilize os cursos para os públicos-alvo configurados, é necessário garantir que pelo menos um curso, um grupo de público-alvo, e uma turma estejam configurados à instância de oferta. ")]))):Q("",!0),c("div",H2,[t[3]||(t[3]=c("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),M(h,{offer:r.offer,"onUpdate:offer":t[0]||(t[0]=w=>r.offer=w),isEditing:r.isEditing,onValidate:t[1]||(t[1]=w=>r.isValidForm=w)},null,8,["offer","isEditing"])]),c("div",{class:fe(["section-container",{"no-title-section":!r.isEditing}])},[r.isEditing?Q("",!0):(D(),S("div",q2,t[4]||(t[4]=[c("div",{class:"lock-message"},[c("i",{class:"fas fa-lock lock-icon"}),c("span",null,"Salve a oferta primeiro para gerenciar os cursos")],-1)])))],2),t[6]||(t[6]=c("hr",null,null,-1)),c("div",W2,[t[5]||(t[5]=c("div",{class:"required-fields-message"},[c("div",{class:"form-info"},[qe(" Este formulário contém campos obrigatórios marcados com "),c("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),c("div",j2,[M(_,{variant:"primary",label:"Salvar",onClick:a.saveOffer,disabled:!r.isValidForm},null,8,["onClick","disabled"]),M(_,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])])]),M(p,{"is-loading":r.loading},null,8,["is-loading"]),M(g,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])])}const G2=Fe(U2,[["render",z2]]),WM="",K2={name:"Alert",props:{type:{type:String,default:"info"},text:{type:String,required:!0},icon:{type:String,required:!1}}};function Z2(e,t,s,i,r,a){return D(),S("div",{class:fe(["alert",`alert-${s.type}`])},[s.icon?(D(),S("i",{key:0,class:fe(s.icon)},null,2)):Q("",!0),qe(" "+W(s.text.replace("-","‑")),1)],2)}const Y2=Fe(K2,[["render",Z2],["__scopeId","data-v-03af0515"]]),J2="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCiAgICA8cGF0aCBkPSJNNyAxNHMtMSAwLTEtMSAxLTQgNS00IDUgMyA1IDQtMSAxLTEgMUg3em00LTZhMyAzIDAgMSAwIDAtNiAzIDMgMCAwIDAgMCA2eiIgZmlsbD0iI2ZmZiIvPg0KICAgIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNS4yMTYgMTRBMi4yMzggMi4yMzggMCAwIDEgNSAxM2MwLTEuMzU1LjY4LTIuNzUgMS45MzYtMy43MkE2LjMyNSA2LjMyNSAwIDAgMCA1IDljLTQgMC01IDMtNSA0czEgMSAxIDFoNC4yMTZ6IiBmaWxsPSIjZmZmIi8+DQogICAgPHBhdGggZD0iTTQuNSA4YTIuNSAyLjUgMCAxIDAgMC01IDIuNSAyLjUgMCAwIDAgMCA1eiIgZmlsbD0iI2ZmZiIvPg0KPC9zdmc+DQo=",Q2="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTggMmEuNS41IDAgMCAxIC41LjV2NWg1YS41LjUgMCAwIDEgMCAxaC01djVhLjUuNSAwIDAgMS0xIDB2LTVoLTVhLjUuNSAwIDEgMSAwLTFoNXYtNUEuNS41IDAgMCAxIDggMnoiIGZpbGw9IiNmZmYiLz4NCjwvc3ZnPg0K",jM="",X2={name:"CollapsibleTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1},expandable:{type:Boolean,default:!1}},data(){return{expandedRows:[]}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})},toggleExpand(e){const t=this.expandedRows.indexOf(e);t===-1?this.expandedRows.push(e):this.expandedRows.splice(t,1)}}},eO={class:"table-responsive"},tO={class:"table"},sO={key:0,class:"expand-column"},nO=["onClick","data-value"],rO={key:0,class:"sort-icon"},oO={key:0},iO={key:0,class:"expand-column"},aO=["onClick","title"],lO=["colspan"],uO={class:"expanded-content"},cO={key:1},dO=["colspan"];function fO(e,t,s,i,r,a){return D(),S("div",eO,[c("table",tO,[c("thead",null,[c("tr",null,[s.expandable?(D(),S("th",sO)):Q("",!0),(D(!0),S(Ie,null,lt(s.headers,u=>(D(),S("th",{key:u.value,onClick:d=>u.sortable?a.handleSort(u.value):null,class:fe({sortable:u.sortable}),"data-value":u.value},[qe(W(u.text)+" ",1),u.sortable?(D(),S("span",rO,[c("i",{class:fe(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)])):Q("",!0)],10,nO))),128))])]),s.items.length>0?(D(),S("tbody",oO,[(D(!0),S(Ie,null,lt(s.items,(u,d)=>(D(),S(Ie,{key:u.id},[c("tr",{class:fe({expanded:r.expandedRows.includes(u.id)})},[s.expandable?(D(),S("td",iO,[c("button",{class:"btn-expand",onClick:h=>a.toggleExpand(u.id),title:r.expandedRows.includes(u.id)?"Recolher":"Expandir"},[c("div",{class:fe(["icon-container",{"is-expanded":r.expandedRows.includes(u.id)}])},t[0]||(t[0]=[c("svg",{width:"16",height:"16",viewBox:"0 0 24 24",class:"expand-icon"},[c("rect",{x:"5",y:"11",width:"14",height:"2",fill:"var(--primary)"}),c("rect",{x:"11",y:"5",width:"2",height:"14",fill:"var(--primary)",class:"vertical-line"})],-1)]),2)],8,aO)])):Q("",!0),(D(!0),S(Ie,null,lt(s.headers,h=>(D(),S("td",{key:`${u.id}-${h.value}`},[Lt(e.$slots,"item-"+h.value,{item:u},()=>[qe(W(u[h.value]),1)],!0)]))),128))],2),s.expandable?(D(),S("tr",{key:0,class:fe(["expanded-row",{"is-visible":r.expandedRows.includes(u.id)}])},[c("td",{colspan:s.headers.length+1},[c("div",uO,[Lt(e.$slots,"expanded-content",{item:u},void 0,!0)])],8,lO)],2)):Q("",!0)],64))),128))])):(D(),S("tbody",cO,[c("tr",null,[c("td",{colspan:s.headers.length+(s.expandable?1:0)},[Lt(e.$slots,"empty-state",{},()=>[t[1]||(t[1]=c("div",{class:"empty-state"},[c("span",null,"Não existem registros")],-1))],!0)],8,dO)])]))])])}const hO=Fe(X2,[["render",fO],["__scopeId","data-v-05038124"]]),zM="",GM="",pO={name:"AddOfferCourseModal",components:{CustomInput:$r,CustomButton:_n,CustomTable:mr,Pagination:gr,Autocomplete:qr,FilterTag:jo},props:{modelValue:{type:Boolean,required:!0},offerId:{type:Number,required:!0}},emits:["update:modelValue","confirm"],data(){return{selectedCategory:null,selectedCourse:null,categoryOptions:[],courseOptions:[],currentPage:1,perPage:5,sortBy:"name",sortDesc:!1,loading:!1,loadingCategories:!1,loadingCourses:!1,loadingCurrentOfferCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingMoreCourses:!1,tableHeaders:[{text:"CURSO",value:"name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],selectedCoursesPreview:[],currentOfferCourses:[]}},computed:{filteredCourses(){const e=[...this.selectedCoursesPreview].sort((i,r)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<r[this.sortBy]?-1*a:i[this.sortBy]>r[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)},totalPages(){return Math.ceil(this.selectedCoursesPreview.length/this.perPage)},courseNoResultsText(){return this.loadingCourses?"Buscando cursos...":this.loadingMoreCourses?"Carregando mais cursos...":this.selectedCategory?this.courseOptions.length===0&&this.selectedCategory?"Todos os cursos já foram adicionados":"Nenhum curso encontrado":"Selecione uma categoria primeiro"}},watch:{modelValue(e,t){e?(this.getCurrentOfferCourses(),this.getCategories()):(this.selectedCategory=null,this.selectedCourse=null,this.categoryOptions=[],this.courseOptions=[],this.selectedCoursesPreview=[])},selectedCategory(e){if(this.courseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,!e){this.getCurrentOfferCourses();return}this.getCoursesForCategory(e.value)},courseOptions(e){e.length<10&&this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&this.$nextTick(()=>{this.loadMoreCourses(),console.log("Carregando mais cursos... via watch")})}},methods:{async getCurrentOfferCourses(){try{this.loadingCurrentOfferCourses=!0;const e=await ep(this.offerId);this.currentOfferCourses=e.courses.map(t=>({id:t.courseid,name:t.fullname,offerCourseId:t.id}))}catch{}finally{this.loadingCurrentOfferCourses=!1}},async getCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await hu("");this.categoryOptions=e.map(t=>({value:t.id,label:t.name}))}catch{}finally{this.loadingCategories=!1}},async addOfferCourses(){try{if(this.loading=!0,this.selectedCoursesPreview.length===0){this.closeModal();return}const e=this.selectedCoursesPreview.map(t=>t.id);await oy(this.offerId,e),this.$emit("confirm",this.selectedCoursesPreview),this.closeModal()}catch{}finally{this.loading=!1}},async getCoursesForCategory(e,t=1,s=!1,i=""){if(e)try{t===1?(this.loadingCourses=!0,s||(this.courseOptions=[])):this.loadingMoreCourses=!0;const r=await ny(this.offerId,e,i,t,this.coursesPerPage);let a=null,u=[];if(u=r.courses,a={page:r.page||1,total_pages:r.total_pages||1},a){if(this.coursesPage=a.page||1,this.coursesTotalPages=a.total_pages||1,this.hasMoreCourses=(a.page||1)<(a.total_pages||1),u&&u.length>0){const h=u.filter(_=>!this.currentOfferCourses.some(p=>p.id===_.id)&&!this.selectedCoursesPreview.some(p=>p.id===_.id)).map(_=>({value:_.id,label:_.fullname}));s?this.courseOptions=[...this.courseOptions,...h]:this.courseOptions=h}}else console.warn("Formato de resposta inesperado")}catch(r){console.error("Erro ao carregar cursos da categoria:",r),s||(this.courseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.getCoursesForCategory(this.selectedCategory.value,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.getCoursesForCategory(this.selectedCategory.value,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.id===e.value)&&(this.selectedCoursesPreview.push({id:e.value,name:e.label}),this.courseOptions=this.courseOptions.filter(t=>t.value!==e.value),this.currentPage=1),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.id===e.id);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.currentPage>1&&this.currentPage>Math.ceil(this.selectedCoursesPreview.length/this.perPage)&&(this.currentPage=Math.max(1,this.currentPage-1)),this.selectedCategory?this.getCoursesForCategory(this.selectedCategory.value,this.currentPage,!1):this.courseOptions.push({value:s.id,label:s.name})}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handlePageChange(e){this.currentPage=e},handlePerPageChange(e){this.perPage=e,this.currentPage=1},closeModal(){this.$emit("update:modelValue",!1),this.selectedCategory=null,this.selectedCourse=null,this.selectedCoursesPreview=[]}}},mO={class:"modal-header"},gO={class:"modal-body"},_O={class:"search-section"},vO={class:"search-group"},bO={class:"search-group"},yO={class:"table-container"},wO={key:0,class:"empty-preview-message"},EO={class:"action-buttons"},CO=["onClick"],xO={class:"modal-footer"};function DO(e,t,s,i,r,a){const u=G("Autocomplete"),d=G("CustomTable"),h=G("Pagination"),_=G("CustomButton");return s.modelValue?(D(),S("div",{key:0,class:"modal-overlay",onClick:t[6]||(t[6]=(...p)=>a.closeModal&&a.closeModal(...p))},[c("div",{class:"modal-content",onClick:t[5]||(t[5]=Pt(()=>{},["stop"]))},[c("div",mO,[t[8]||(t[8]=c("h2",null,"Adicionar curso",-1)),c("button",{class:"close-button",onClick:t[0]||(t[0]=(...p)=>a.closeModal&&a.closeModal(...p))},t[7]||(t[7]=[c("i",{class:"fas fa-times"},null,-1)]))]),c("div",gO,[t[11]||(t[11]=c("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),c("div",_O,[c("div",vO,[M(u,{modelValue:r.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>r.selectedCategory=p),items:r.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:r.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":r.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada"},null,8,["modelValue","items","loading","no-results-text"])]),c("div",bO,[M(u,{modelValue:r.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>r.selectedCourse=p),items:r.courseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!r.selectedCategory,loading:r.loadingCourses||r.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"keep-open-on-select":!0,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),c("div",yO,[r.selectedCoursesPreview.length===0?(D(),S("div",wO,t[9]||(t[9]=[c("p",null,"Selecione cursos acima para adicioná-los à oferta",-1)]))):(D(),_t(d,{key:1,headers:r.tableHeaders,items:a.filteredCourses,"sort-by":r.sortBy,"sort-desc":r.sortDesc,onSort:a.handleTableSort},{"item-actions":Se(({item:p})=>[c("div",EO,[c("button",{class:"btn-action btn-delete",onClick:g=>a.removeCourse(p),title:"Remover da lista"},t[10]||(t[10]=[c("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,CO)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),r.selectedCoursesPreview.length>0?(D(),_t(h,{key:0,"current-page":r.currentPage,"onUpdate:currentPage":[t[3]||(t[3]=p=>r.currentPage=p),a.handlePageChange],"per-page":r.perPage,"onUpdate:perPage":[t[4]||(t[4]=p=>r.perPage=p),a.handlePerPageChange],total:r.selectedCoursesPreview.length},null,8,["current-page","per-page","total","onUpdate:currentPage","onUpdate:perPage"])):Q("",!0)]),c("div",xO,[M(_,{variant:"primary",label:"Confirmar","is-loading":r.loading,disabled:r.selectedCoursesPreview.length===0,onClick:a.addOfferCourses},null,8,["is-loading","disabled","onClick"]),M(_,{variant:"secondary",label:"Cancelar",onClick:a.closeModal},null,8,["onClick"])])])])):Q("",!0)}const OO=Fe(pO,[["render",DO],["__scopeId","data-v-8cfa07af"]]),KM="",SO={name:"DuplicateOfferClassModal",components:{Autocomplete:qr,CustomTable:mr,Pagination:gr,CustomButton:_n},props:{offerClass:{type:Object,default:null},parentCourse:{type:Object,default:null},offerId:{type:[Number,String],default:null}},emits:["close","success","loading","error"],data(){return{selectedCategory:null,categoryOptions:[],loadingCategories:!1,selectedCourse:null,targetCourseOptions:[],selectedCoursesPreview:[],loading:!1,loadingCourses:!1,loadingMoreCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,currentPage:1,perPage:5,sortBy:"label",sortDesc:!1,tableHeaders:[{text:"CURSO",value:"label",sortable:!0},{text:"CATEGORIA",value:"category_name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],duplicatingCourses:!1,duplicatedCount:0,totalToDuplicate:0,existingCourses:[]}},computed:{courseNoResultsText(){return this.selectedCategory?this.loadingCourses||this.loadingMoreCourses?"Carregando cursos...":this.targetCourseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado":"Selecione uma categoria primeiro"},filteredCourses(){const e=(this.currentPage-1)*this.perPage,t=e+this.perPage;return this.getSortedCourses().slice(e,t)}},watch:{offerClass(){this.resetForm(),this.getCategories()},parentCourse(){this.resetForm(),this.getCategories()},selectedCategory(e){this.resetCourseSelection(),e!=null&&e.value&&this.getCoursesForCategory(e.value)}},async created(){this.initializeComponent()},methods:{async initializeComponent(){this.resetForm(),await this.getCategories()},resetForm(){this.resetCategorySelection(),this.resetCourseSelection(),this.resetPagination(),this.resetDuplicationState(),this.existingCourses=[]},resetCategorySelection(){this.selectedCategory=null,this.categoryOptions=[],this.loadingCategories=!1},resetCourseSelection(){this.selectedCourse=null,this.targetCourseOptions=[],this.selectedCoursesPreview=[],this.loadingCourses=!1,this.loadingMoreCourses=!1,this.resetCoursePagination()},resetCoursePagination(){this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},resetPagination(){this.currentPage=1},resetDuplicationState(){this.duplicatingCourses=!1,this.duplicatedCount=0,this.totalToDuplicate=0},getSortedCourses(){return[...this.selectedCoursesPreview].sort((e,t)=>{const s=e[this.sortBy],i=t[this.sortBy];return s<i?this.sortDesc?1:-1:s>i?this.sortDesc?-1:1:0})},async getCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await hu("",this.offerId);this.categoryOptions=e.map(t=>({value:t.id,label:t.name}))}catch(e){this.handleError("Erro ao carregar categorias:",e)}finally{this.loadingCategories=!1}},async getCoursesForCategory(e,t=1,s=!1,i=""){if(!(!e||!this.offerClass))try{t===1?(this.loadingCourses=!0,s||(this.targetCourseOptions=[])):this.loadingMoreCourses=!0;const u=(await py(this.offerClass.id)).filter(d=>{const h=!i||d.name&&d.name.toLowerCase().includes(i.toLowerCase())||d.fullname&&d.fullname.toLowerCase().includes(i.toLowerCase()),_=!this.selectedCoursesPreview.some(w=>parseInt(w.value)===parseInt(d.id)),p=parseInt(d.categoryid)===parseInt(e),g=parseInt(d.id)!==parseInt(this.parentCourse.courseId);return p&&g&&h&&_}).map(d=>({value:d.id,label:d.name||d.fullname||d.coursename||`Curso ${d.id}`,categoryid:d.categoryid,category_name:d.category_name})).filter(d=>d!==null);s?this.targetCourseOptions=[...this.targetCourseOptions,...u]:this.targetCourseOptions=u,this.hasMoreCourses=u.length>=this.coursesPerPage,t>this.coursesPage&&(this.coursesPage=t)}catch(r){this.handleError("Erro ao carregar cursos da categoria:",r),s||(this.targetCourseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.getCoursesForCategory(this.selectedCategory.value,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.resetCoursePagination(),await this.getCoursesForCategory(this.selectedCategory.value,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.value===e.value)&&(this.selectedCoursesPreview.push({value:e.value,label:e.label,categoryid:e.categoryid,category_name:e.category_name}),this.targetCourseOptions=this.targetCourseOptions.filter(t=>t.value!==e.value),this.selectedCourse=null)},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.value===e.value);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.targetCourseOptions.push(s)}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handleError(e,t){this.$emit("error","Erro ao carregar dados. Por favor, tente novamente.")},async handleConfirm(){if(!(!this.offerClass||this.selectedCoursesPreview.length===0))try{this.loading=!0,this.duplicatingCourses=!0,this.totalToDuplicate=this.selectedCoursesPreview.length,this.duplicatedCount=0,this.$emit("loading",!0);const e=this.offerClass.name,t=parseInt(this.offerClass.id,10),s=[];for(const i of this.selectedCoursesPreview){const r=parseInt(i.value,10);try{const a=await hy(t,r);s.push({offerClassName:e,targetCourseName:i.label,offerClassId:t,targetCourseId:r,result:a}),this.duplicatedCount++}catch(a){this.$emit("error",`Erro ao duplicar para o curso ${i.label}: ${a.message}`)}}if(s.length===0)throw new Error("Nenhuma turma foi duplicada com sucesso.");this.$emit("success",{offerClassName:e,totalSelected:this.totalToDuplicate,totalDuplicates:s.length,duplicates:s}),this.resetForm(),this.$emit("close")}catch(e){this.$emit("error",e.message||"Erro ao duplicar turmas.")}finally{this.duplicatingCourses=!1,this.loading=!1,this.$emit("loading",!1)}}}},TO={class:"modal-header"},NO={class:"modal-title"},IO={class:"modal-body"},AO={class:"search-section"},MO={class:"search-group"},PO={class:"search-group"},kO={class:"table-container"},VO={key:0,class:"empty-preview-message"},RO={class:"action-buttons"},FO=["onClick"],LO={class:"modal-footer"};function UO(e,t,s,i,r,a){var p;const u=G("Autocomplete"),d=G("CustomTable"),h=G("Pagination"),_=G("CustomButton");return D(),S("div",{class:"modal-backdrop",onClick:t[7]||(t[7]=g=>e.$emit("close"))},[c("div",{class:"modal-container",onClick:t[6]||(t[6]=Pt(()=>{},["stop"]))},[c("div",TO,[c("h3",NO,'Duplicar Turma "'+W((p=s.offerClass)==null?void 0:p.name)+'"',1),c("button",{class:"close-button",onClick:t[0]||(t[0]=g=>e.$emit("close"))},t[8]||(t[8]=[c("i",{class:"fas fa-times"},null,-1)]))]),c("div",IO,[t[11]||(t[11]=c("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),c("div",AO,[c("div",MO,[M(u,{modelValue:r.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=g=>r.selectedCategory=g),items:r.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:r.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":r.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada"},null,8,["modelValue","items","loading","no-results-text"])]),c("div",PO,[M(u,{modelValue:r.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=g=>r.selectedCourse=g),items:r.targetCourseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!r.selectedCategory,loading:r.loadingCourses||r.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),c("div",kO,[r.selectedCoursesPreview.length===0?(D(),S("div",VO,t[9]||(t[9]=[c("p",null,"Selecione cursos acima para duplicar a turma",-1)]))):(D(),_t(d,{key:1,headers:r.tableHeaders,items:a.filteredCourses,"sort-by":r.sortBy,"sort-desc":r.sortDesc,onSort:a.handleTableSort},{"item-actions":Se(({item:g})=>[c("div",RO,[c("button",{class:"btn-action btn-delete",onClick:w=>a.removeCourse(g),title:"Remover da lista"},t[10]||(t[10]=[c("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,FO)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),r.selectedCoursesPreview.length>0?(D(),_t(h,{key:0,"current-page":r.currentPage,"onUpdate:currentPage":t[3]||(t[3]=g=>r.currentPage=g),"per-page":r.perPage,"onUpdate:perPage":t[4]||(t[4]=g=>r.perPage=g),total:r.selectedCoursesPreview.length},null,8,["current-page","per-page","total"])):Q("",!0)]),c("div",LO,[M(_,{variant:"primary",label:"Duplicar","is-loading":r.loading,disabled:r.selectedCoursesPreview.length===0,onClick:a.handleConfirm},null,8,["is-loading","disabled","onClick"]),M(_,{variant:"secondary",label:"Cancelar",onClick:t[5]||(t[5]=g=>e.$emit("close"))})])])])}const BO=Fe(SO,[["render",UO],["__scopeId","data-v-12115006"]]),ZM="",$O={name:"EnrolTypeModal",components:{CustomSelect:Xs,HelpIcon:_u},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Tipo de Inscrição"},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Continuar"},cancelButtonText:{type:String,default:"Cancelar"},offerCourseId:{type:[Number,String],required:!0},offerId:{type:[Number,String],required:!1,default:"0"}},emits:["close","confirm"],data(){return{selectedEnrolType:"",enrolmentMethods:[],loading:!1}},mounted(){this.$nextTick(()=>{this.initializePopovers()})},watch:{show(e){e&&(this.loadEnrolmentMethods(),this.$nextTick(()=>{this.initializePopovers()}))}},methods:{async loadEnrolmentMethods(){this.loading=!0;const e=await tp(!0);e&&Array.isArray(e.data)&&(this.enrolmentMethods=e.data.map(t=>({value:t.enrol,label:t.name}))),this.loading=!1},handleConfirm(){this.selectedEnrolType&&this.$emit("confirm",{enrolType:this.selectedEnrolType,offerCourseId:this.offerCourseId,offerId:this.offerId})},initializePopovers(){typeof $<"u"&&typeof $.fn.popover<"u"?$('[data-toggle="popover"]').popover({container:"body",trigger:"focus",html:!0}):console.warn("jQuery ou Bootstrap não estão disponíveis para inicializar popovers")}}},HO={class:"modal-header"},qO={class:"modal-title"},WO={class:"modal-body"},jO={class:"enrol-type-modal"},zO={class:"form-group mb-3"},GO={class:"label-with-help"},KO={class:"limited-width-input",style:{"max-width":"280px"}},ZO={class:"modal-footer"},YO={class:"footer-buttons"},JO=["disabled"];function QO(e,t,s,i,r,a){const u=G("HelpIcon"),d=G("CustomSelect");return s.show?(D(),S("div",{key:0,class:"modal-backdrop",onClick:t[5]||(t[5]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[c("div",{class:fe(["modal-container",[`modal-${s.size}`]]),onClick:t[4]||(t[4]=Pt(()=>{},["stop"]))},[c("div",HO,[c("h3",qO,W(s.title),1),c("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[6]||(t[6]=[c("i",{class:"fas fa-times"},null,-1)]))]),c("div",WO,[c("div",jO,[t[9]||(t[9]=c("p",{class:"modal-description"}," Selecione o tipo de inscrição para esta turma. Esta configuração não poderá ser alterada posteriormente. ",-1)),c("div",zO,[c("div",GO,[t[7]||(t[7]=c("label",{class:"form-label"},"Método de inscrição",-1)),t[8]||(t[8]=c("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw m-0",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),M(u,{title:"Ajuda com método de inscrição",text:`Inscrição automática por público-alvo em ofertas: Nesta opção o usuário será inscrito automaticamente nos cursos que forem atribuídos para o público-alvo dele.\r
Inscrição manual em ofertas: Nesta opção o usuário será matriculado manualmente (em lote ou individualmente) por um perfil autorizado, através da página 'Usuários matriculados' contida em cada turma.\r
Autoisncrição em ofertas: Nesta opção o usuário visualizará os cursos que forem atribuídos para o público-alvo dele, porém, ele precisa clicar no curso para fazer sua matrícula em uma turma. Ou seja, ele não será inscrito automaticamente como no Tipo de inscrição 'Inscrição por público-alvo em ofertas'.`})]),c("div",KO,[M(d,{modelValue:r.selectedEnrolType,"onUpdate:modelValue":t[1]||(t[1]=h=>r.selectedEnrolType=h),options:[{value:"",label:"Selecione um método..."},...r.enrolmentMethods],width:280,required:""},null,8,["modelValue","options"])])])])]),c("div",ZO,[t[10]||(t[10]=c("div",{class:"form-info"},[c("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),c("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1)),c("div",YO,[c("button",{class:"btn btn-primary",onClick:t[2]||(t[2]=(...h)=>a.handleConfirm&&a.handleConfirm(...h)),disabled:!r.selectedEnrolType},W(s.confirmButtonText),9,JO),c("button",{class:"btn btn-secondary",onClick:t[3]||(t[3]=h=>e.$emit("close"))},W(s.cancelButtonText),1)])])],2)])):Q("",!0)}const XO=Fe($O,[["render",QO],["__scopeId","data-v-65de86e6"]]),YM="",eS={name:"TableList",mixins:[oa],components:{OfferForm:bu,CustomTable:mr,CustomSelect:Xs,CustomInput:$r,CustomButton:_n,Pagination:gr,CollapsibleTable:hO,PageHeader:Hr,BackButton:zo,Autocomplete:qr,TextEditor:vu,CustomCheckbox:qo,FilterRow:ta,FilterGroup:sa,FilterTag:jo,FilterTags:ra,AddOfferCourseModal:OO,ConfirmationModal:na,Toast:$n,DuplicateOfferClassModal:BO,EnrolTypeModal:XO,LFLoading:Wo},props:{offerId:{type:Number,required:!0}},data(){return{icons:{users:J2,plus:Q2},showAddCourseModalVisible:!1,showCourseStatusModal:!1,showDeleteOfferCourseModal:!1,offerCourseToDelete:null,showDeleteOfferClassModal:!1,offerClassToDelete:null,classParentCourse:null,showOfferClassStatusModal:!1,showDuplicateOfferClassModal:!1,showEnrolTypeModal:!1,selectedOfferClass:null,offerClassToDuplicate:null,classToDuplicateParentOfferCourse:null,selectedOfferCourseForClass:null,categoryOptions:[],courseOptions:[],selectedOfferCourse:null,loading:!1,inputFilters:{course:null,category:null,onlyActive:!1},offerCourses:[],currentPage:1,perPage:5,totalItems:0,sortBy:"id",sortDesc:!1,filterCoursesPage:1,filterCoursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingCourses:!1,loadingMoreCourses:!1,filterCourseNoResultsText:"Nenhum curso encontrado",courseTableHeaders:[{text:"NOME DO CURSO",value:"name",sortable:!0},{text:"CATEGORIA",value:"category",sortable:!0},{text:"NÚMERO DE TURMAS",value:"courseClassCount",sortable:!0},{text:"STATUS DO CURSO",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}]}},watch:{async"inputFilters.course"(e,t){this.currentPage=1,e!==null&&(this.inputFilters.category=null),await this.getCourses()},async"inputFilters.category"(e,t){e===""&&(this.inputFilters.category=null),e!==null&&(this.inputFilters.course=null),this.currentPage=1,await this.getCourses(),await this.getCourseOptions()},async"inputFilters.onlyActive"(e,t){this.currentPage=1,this.inputFilters.course=null,await this.getCourses(),await this.getCourseOptions()},currentPage(){this.getCourses()},perPage(){this.currentPage=1,this.getCourses()}},async created(){await this.getCourses(),await this.getCategoryOptions(),await this.getCourseOptions()},methods:{async getCourses(){if(this.offerId)try{this.loading=!0;const e={onlyActive:this.inputFilters.onlyActive,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc};this.inputFilters.course&&(e.courseIds=[this.inputFilters.course.value]),this.inputFilters.category&&(e.categorySearch=this.inputFilters.category.label);const t=await ep(this.offerId,e),{page:s,total_pages:i,total_items:r,courses:a}=t;this.currentPage=s;const u=[];for(const d of a)try{const _=(await ly(d.id)).map(p=>({...p,enrol:p.enrol||"-",enrolName:p.enrol_name||"-",vacancies:p.max_users?p.max_users:"Ilimitado",totalEnrolled:p.enrolled_users||0,startDate:this.formatDate(p.startdate),endDate:this.formatDate(p.enddate),status:!!parseInt(p.status),statusName:parseInt(p.status)?"Ativa":"Inativo",canActivate:p.can_activate,canDelete:p.can_delete}));u.push({id:d.id,courseId:d.courseid,name:d.fullname,category:d.category_name||"-",courseClassCount:_.length,status:!!parseInt(d.status),statusName:parseInt(d.status)?"Ativo":"Inativo",canDelete:d.can_delete,canActivate:d.can_activate,offerClasses:_})}catch(h){console.log(h)}this.offerCourses=u,this.totalItems=r}catch{this.offerCourses=[],this.totalItems=0}finally{this.loading=!1}},async getCategoryOptions(){if(this.offerId)try{this.loading=!0;const e=await hu("",this.offerId);this.categoryOptions=e.map(t=>({value:t.id,label:t.name}))}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},async getCourseOptions(e="",t=!0){var s,i;if(this.offerId){this.loading=!0;try{t?(this.filterCoursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;const a=(await ry(this.offerId,(s=this.inputFilters.category)==null?void 0:s.value,e,(i=this.inputFilters.course)!=null&&i.value?[this.inputFilters.course.value]:[],this.inputFilters.onlyActive)).map(u=>({value:u.id||u.courseid,label:u.fullname}));t?this.courseOptions=a:this.courseOptions=[...this.courseOptions,...a],this.hasMoreCourses=!1,this.courseOptions.length===0&&(this.filterCourseNoResultsText="Nenhum curso disponível nesta categoria")}catch{this.showErrorMessage("Erro ao carregar cursos da categoria."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}}},getOperationalCycleClassName(e){switch(e){case 0:return"badge-secondary";case 1:return"badge-primary";case 2:return"badge-success";default:return""}},formatDate(e){return e?new Date(e*1e3).toLocaleDateString("pt-BR"):"-"},clearFilters(){this.inputFilters={course:null,category:null,onlyActive:!1},this.getCourses(),this.getCourseOptions()},async removeFilter(e){this.inputFilters[e]=null},async loadMoreCourses(){var e;this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&(this.filterCoursesPage+=1,(e=this.inputFilters.category)!=null&&e.value&&await this.getCourseOptions("",!1))},handleOnlyActiveChange(){this.currentPage=1,this.getCourses()},async handleAddCourseConfirm(e){try{this.loading=!0,await this.getCourses(),this.showSuccessMessage("Curso(s) adicionado(s) com sucesso à oferta.")}catch(t){this.showErrorMessage(t.message||"Ocorreu um erro ao adicionar os cursos.")}finally{this.loading=!1}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.getCourses()},handlePageChange(e){this.currentPage=e,this.getCourses()},addOfferClass(e){this.selectedOfferCourseForClass=e,this.showEnrolTypeModal=!0},handleEnrolTypeConfirm(e){this.showEnrolTypeModal=!1,this.$router.push({name:"NewClass",params:{offerCourseId:e.offerCourseId,offerid:this.offerId},query:{enrol_type:e.enrolType}})},goToeditOfferClass(e){this.offerCourses.find(t=>t.offerClasses.some(s=>s.id===e.id)),this.$router.push({name:"offer.class.edit",params:{classId:e.id}})},requestToggleOfferClassStatus(e){this.selectedOfferClass={...e},this.showOfferClassStatusModal=!0},async toggleOfferClassStatus(){if(this.selectedOfferClass)try{this.loading=!0;const e=this.selectedOfferClass.name,t=!this.selectedOfferClass.status;await my(this.selectedOfferClass.id,t);const s=this.offerCourses.findIndex(i=>i.offerClasses.some(r=>r.id===this.selectedOfferClass.id));if(s!==-1){const i=this.offerCourses[s],r=i.offerClasses.findIndex(a=>a.id===this.selectedOfferClass.id);if(r!==-1){const a=i.offerClasses[r];a.status=t,a.statusName=t?"Ativo":"Inativo"}}await this.getCourses(),this.showSuccessMessage(t?`Turma "${e}" ativada com sucesso.`:`Turma "${e}" inativada com sucesso.`),this.selectedOfferClass=null,this.showOfferClassStatusModal=!1}catch{}finally{this.loading=!1}},removeOfferClass(e,t){const s=e.offerClasses[t];s.canDelete&&(this.offerClassToDelete=s,this.classParentCourse=e,this.showDeleteOfferClassModal=!0)},goToEnrollments(e){this.$router.push({name:"Enrollments",params:{classId:parseInt(e.id)}})},async handleDuplicateSuccess(e){await this.getCourses(),e.totalDuplicates&&this.showSuccessMessage(`Turma "${e.offerClassName}" duplicada com sucesso para ${e.totalDuplicates} curso(s).`)},duplicateOfferClass(e,t){this.offerClassToDuplicate=e,this.classToDuplicateParentOfferCourse=t,this.showDuplicateOfferClassModal=!0},async deleteOfferClass(){if(!(!this.offerClassToDelete||!this.classParentCourse)){this.loading=!0;try{const e=this.offerClassToDelete.name;await cy(this.offerClassToDelete.id);const t=this.classParentCourse.offerClasses.findIndex(s=>s.id===this.offerClassToDelete.id);t!==-1&&(this.classParentCourse.offerClasses.splice(t,1),this.classParentCourse.courseClassCount=this.classParentCourse.offerClasses.length),this.showSuccessMessage(`Turma ${e} excluída com sucesso.`),this.offerClassToDelete=null,this.classParentCourse=null,this.showDeleteOfferClassModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao excluir turma.")}finally{this.loading=!1}}},requestToggleOfferCourseStatus(e){e.canActivate&&(this.selectedOfferCourse=e,this.showCourseStatusModal=!0)},getStatusButtonTitle(e){return e.status?e.canActivate?"Inativar":"Não é possível inativar este curso":e.canActivate?"Ativar":"Não é possível ativar este curso"},async toggleOfferCourseStatus(){if(this.selectedOfferCourse)try{this.loading=!0;const e=!this.selectedOfferCourse.status,t=this.selectedOfferCourse.name,s=this.selectedOfferCourse.id;await ey(this.offerId,s,e);const i=this.offerCourses.findIndex(r=>r.id===this.selectedOfferCourse.id);if(i!==-1){const r=this.offerCourses[i];r.status=e,r.statusName=e?"Ativo":"Inativo"}this.showCourseStatusModal=!1,this.selectedOfferCourse=null,await this.getCourses(),this.showSuccessMessage(e?`Curso "${t}" ativado com sucesso.`:`Curso "${t}" inativado com sucesso.`)}catch{}finally{this.loading=!1}},requestDeleteOfferCourse(e){e.canDelete&&(this.offerCourseToDelete=e,this.showDeleteOfferCourseModal=!0)},async deleteOfferCourse(){if(this.offerCourseToDelete)try{this.loading=!0;const e=this.offerCourseToDelete.name,t=this.offerCourseToDelete.id;await Xb(this.offerId,t),this.offerCourses=this.offerCourses.filter(i=>i.id!==this.offerCourseToDelete.id),this.offerCourseToDelete=null,this.showDeleteOfferCourseModal=!1,await this.getCourses();const s=`Curso "${e}" excluído com sucesso.`;this.showSuccessMessage(s)}catch(e){this.showErrorMessage(e.message||"Erro ao remover curso.")}finally{this.loading=!1}}}},tS={class:"filters-left-group"},sS={class:"filters-right-group"},nS={class:"empty-state"},rS={class:"no-results"},oS=["title"],iS={key:0},aS={key:1},lS={class:"action-buttons"},uS=["onClick"],cS=["src"],dS=["onClick","disabled","title"],fS=["onClick","disabled","title"],hS={class:"course-class-container"},pS={class:"course-class-content"},mS={key:0},gS={class:"course-class-col"},_S=["title"],vS={class:"course-class-col"},bS={class:"course-class-col"},yS={class:"course-class-col"},wS={class:"course-class-col"},ES={class:"course-class-col"},CS={class:"course-class-col operational-cycle"},xS={class:"course-class-col"},DS={class:"action-buttons"},OS=["onClick"],SS=["onClick"],TS=["src"],NS=["onClick"],IS=["onClick"],AS=["title","onClick"],MS=["onClick","disabled","title"],PS={key:1,class:"empty-course-class"};function kS(e,t,s,i,r,a){var we,X,pe,be,Ae,ae,A,Ee,ue,Ge,mt;const u=G("Autocomplete"),d=G("FilterGroup"),h=G("CustomCheckbox"),_=G("FilterTag"),p=G("FilterTags"),g=G("FilterRow"),w=G("CollapsibleTable"),x=G("Pagination"),P=G("AddOfferCourseModal"),L=G("ConfirmationModal"),te=G("DuplicateOfferClassModal"),N=G("EnrolTypeModal"),re=G("LFLoading"),J=G("Toast");return D(),S("div",null,[M(g,{inline:"",class:"courses-filter-row"},{default:Se(()=>[c("div",tS,[M(d,null,{default:Se(()=>[M(u,{modelValue:r.inputFilters.category,"onUpdate:modelValue":t[0]||(t[0]=ce=>r.inputFilters.category=ce),items:r.categoryOptions,placeholder:"Pesquisar...",label:"Categoria","input-max-width":218,"has-search-icon":!0,"auto-open":!1,"show-filter-tags":!1,"show-selected-in-input":!0,"no-results-text":r.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada"},null,8,["modelValue","items","no-results-text"])]),_:1}),M(d,null,{default:Se(()=>[M(u,{modelValue:r.inputFilters.course,"onUpdate:modelValue":t[1]||(t[1]=ce=>r.inputFilters.course=ce),items:r.courseOptions,placeholder:"Pesquisar...",label:"Curso","input-max-width":218,"has-search-icon":!0,"auto-open":!0,loading:r.loadingCourses||r.loadingMoreCourses,"no-results-text":r.filterCourseNoResultsText,onLoadMore:a.loadMoreCourses,onSearch:t[2]||(t[2]=ce=>a.getCourseOptions(ce)),ref:"courseAutocomplete"},null,8,["modelValue","items","loading","no-results-text","onLoadMore"])]),_:1}),M(d,{isCheckbox:!0,class:"checkbox-filter-group"},{default:Se(()=>[M(h,{modelValue:r.inputFilters.onlyActive,"onUpdate:modelValue":t[3]||(t[3]=ce=>r.inputFilters.onlyActive=ce),id:"onlyActive",label:"Não exibir inativos",onChange:a.handleOnlyActiveChange},null,8,["modelValue","onChange"])]),_:1}),r.inputFilters.course?(D(),_t(p,{key:0,class:"mt-3"},{default:Se(()=>[M(_,{onRemove:t[4]||(t[4]=ce=>a.removeFilter("course"))},{default:Se(()=>[qe(" Curso: "+W(r.inputFilters.course.label),1)]),_:1})]),_:1})):Q("",!0)]),c("div",sS,[c("button",{class:"btn btn-primary",onClick:t[5]||(t[5]=ce=>r.showAddCourseModalVisible=!0)}," Adicionar Curso ")])]),_:1}),M(w,{headers:r.courseTableHeaders,items:r.offerCourses,"sort-by":r.sortBy,"sort-desc":r.sortDesc,onSort:a.handleTableSort,expandable:!0},{"empty-state":Se(()=>[c("div",nS,[c("span",rS,W(r.loading?"Carregando registros...":"Não existem registros"),1)])]),"item-name":Se(({item:ce})=>[c("span",{title:ce.name},W(ce.name.length>50?ce.name.slice(0,50)+"...":ce.name),9,oS)]),"item-status":Se(({item:ce})=>[ce.status?(D(),S("span",iS,t[15]||(t[15]=[c("svg",{class:"mr-1",width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[c("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"}),c("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",stroke:"var(--success)","stroke-width":"2"}),c("path",{d:"M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM15.3589 7.34055C15.2329 7.34314 15.1086 7.37093 14.9937 7.42258C14.8788 7.47425 14.7755 7.54884 14.6899 7.64133L10.3491 13.1726L7.73291 10.5544C7.55519 10.3888 7.31954 10.2992 7.07666 10.3034C6.83383 10.3078 6.60191 10.4061 6.43018 10.5779C6.25849 10.7496 6.16005 10.9815 6.15576 11.2243C6.15152 11.4672 6.24215 11.7019 6.40771 11.8796L9.71533 15.1882C9.80438 15.2771 9.91016 15.3472 10.0269 15.3943C10.1436 15.4413 10.2691 15.4649 10.395 15.4626C10.5206 15.4602 10.6446 15.4327 10.7593 15.3816C10.8742 15.3302 10.9782 15.256 11.064 15.1638L16.0532 8.92648C16.2233 8.74961 16.3183 8.51269 16.3159 8.2673C16.3136 8.02207 16.2147 7.78755 16.0415 7.61398H16.0396C15.9503 7.52501 15.844 7.45488 15.7271 7.40793C15.6101 7.36102 15.4849 7.33798 15.3589 7.34055Z",fill:"var(--success)"})],-1),qe(" Ativo ")]))):(D(),S("span",aS,t[16]||(t[16]=[c("svg",{class:"mr-1",width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[c("g",{"clip-path":"url(#clip0_572_6021)"},[c("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"}),c("path",{d:"M11.0024 1.40109C13.6546 1.40109 16.1984 2.45444 18.0737 4.3298C19.9491 6.20517 21.0024 8.74893 21.0024 11.4011C21.0024 14.0533 19.9491 16.597 18.0737 18.4724C16.1984 20.3477 13.6546 21.4011 11.0024 21.4011C8.35028 21.4011 5.80652 20.3477 3.93115 18.4724C2.05579 16.597 1.00244 14.0533 1.00244 11.4011C1.00244 8.74893 2.05579 6.20517 3.93115 4.3298C5.80652 2.45444 8.35028 1.40109 11.0024 1.40109ZM14.7524 7.02512C14.6703 7.02512 14.5891 7.04157 14.5132 7.07297C14.4373 7.10442 14.3682 7.1506 14.3101 7.20871L11.0024 10.5173L7.69482 7.20871C7.57747 7.09135 7.41841 7.02512 7.25244 7.02512C7.08647 7.02512 6.92742 7.09135 6.81006 7.20871C6.6927 7.32607 6.62646 7.48512 6.62646 7.65109C6.62646 7.81706 6.6927 7.97612 6.81006 8.09348L10.1187 11.4011L6.81006 14.7087C6.75195 14.7668 6.70577 14.8359 6.67432 14.9118C6.64292 14.9877 6.62646 15.069 6.62646 15.1511C6.62646 15.2332 6.64292 15.3145 6.67432 15.3904C6.70577 15.4663 6.75195 15.5354 6.81006 15.5935C6.92742 15.7108 7.08647 15.7771 7.25244 15.7771C7.33456 15.7771 7.41583 15.7606 7.4917 15.7292C7.56762 15.6978 7.63671 15.6516 7.69482 15.5935L11.0024 12.2849L14.3101 15.5935C14.3682 15.6516 14.4373 15.6978 14.5132 15.7292C14.5891 15.7606 14.6703 15.7771 14.7524 15.7771C14.8346 15.7771 14.9158 15.7606 14.9917 15.7292C15.0676 15.6978 15.1367 15.6516 15.1948 15.5935C15.2529 15.5354 15.2991 15.4663 15.3306 15.3904C15.362 15.3145 15.3784 15.2332 15.3784 15.1511C15.3784 15.069 15.362 14.9877 15.3306 14.9118C15.2991 14.8359 15.2529 14.7668 15.1948 14.7087L11.8862 11.4011L15.1948 8.09348C15.2529 8.03537 15.2991 7.96627 15.3306 7.89035C15.362 7.81448 15.3784 7.73321 15.3784 7.65109C15.3784 7.56898 15.362 7.48771 15.3306 7.41183C15.2991 7.33591 15.2529 7.26682 15.1948 7.20871C15.1367 7.1506 15.0676 7.10442 14.9917 7.07297C14.9158 7.04157 14.8346 7.02512 14.7524 7.02512Z",fill:"var(--danger)"})]),c("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",stroke:"var(--danger)","stroke-width":"2"}),c("defs",null,[c("clipPath",{id:"clip0_572_6021"},[c("rect",{x:"1",y:"1.39999",width:"20",height:"20",rx:"10",fill:"white"})])])],-1),qe(" Inativo ")])))]),"item-actions":Se(({item:ce})=>[c("div",lS,[c("button",{class:"btn-action btn-add",onClick:it=>a.addOfferClass(ce),title:"Adicionar turma"},[c("img",{src:r.icons.plus,alt:"Adicionar turma"},null,8,cS)],8,uS),c("button",{class:fe(["btn-action",ce.status?"btn-deactivate":"btn-activate"]),onClick:it=>a.requestToggleOfferCourseStatus(ce),disabled:!ce.status&&!ce.canActivate||!ce.canActivate,title:a.getStatusButtonTitle(ce)},[c("i",{class:fe(ce.status?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,dS),c("button",{class:"btn-action btn-delete",onClick:it=>a.requestDeleteOfferCourse(ce),disabled:!ce.canDelete,title:ce.canDelete?"Excluir":"Não é possível excluir este curso"},t[17]||(t[17]=[c("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,fS)])]),"expanded-content":Se(({item:ce})=>{var it;return[c("div",hS,[t[23]||(t[23]=c("div",{class:"course-class-header"},[c("div",{class:"course-class-col"},"NOME DA TURMA"),c("div",{class:"course-class-col"},"TIPO DE INSCRIÇÃO"),c("div",{class:"course-class-col"},"Nº DE VAGAS"),c("div",{class:"course-class-col"},"Nº DE INSCRITOS"),c("div",{class:"course-class-col"},"DATA INÍCIO"),c("div",{class:"course-class-col"},"DATA FIM"),c("div",{class:"course-class-col"},"STATUS"),c("div",{class:"course-class-col"},"AÇÕES")],-1)),c("div",pS,[((it=ce==null?void 0:ce.offerClasses)==null?void 0:it.length)>0?(D(),S("div",mS,[(D(!0),S(Ie,null,lt(ce.offerClasses,(de,Ce)=>(D(),S("div",{class:"course-class-row",key:Ce},[c("div",gS,[c("span",{title:de.name},W(de.name.length>20?de.name.slice(0,20)+"...":de.name),9,_S)]),c("div",vS,W(de.enrolName),1),c("div",bS,W(de.vacancies),1),c("div",yS,W(de.totalEnrolled),1),c("div",wS,W(de.startDate),1),c("div",ES,W(de.endDate),1),c("div",CS,[de.operational_cycle===0?(D(),S("span",{key:0,class:fe(["badge",a.getOperationalCycleClassName(de.operational_cycle)])},W(de.operational_cycle_name),3)):Q("",!0)]),c("div",xS,[c("div",DS,[c("button",{class:"btn-action btn-edit",onClick:xt=>e.editOffer(ce),title:"Visualizar"},t[18]||(t[18]=[c("svg",{width:"38",height:"39",viewBox:"0 0 38 39",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[c("path",{d:"M18.1875 25.5897C20.04 25.5897 21.5417 24.0629 21.5417 22.1795C21.5417 20.296 20.04 18.7692 18.1875 18.7692C16.3351 18.7692 14.8334 20.296 14.8334 22.1795C14.8334 24.0629 16.3351 25.5897 18.1875 25.5897Z",stroke:"white","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),c("path",{d:"M11 29.4872L15.7917 24.6154M11 22.6667V11.9487C11 11.4319 11.2019 10.9362 11.5614 10.5708C11.9208 10.2053 12.4083 10 12.9167 10H20.5833L26.3333 15.8462V27.5385C26.3333 28.0553 26.1314 28.551 25.772 28.9164C25.4125 29.2819 24.925 29.4872 24.4167 29.4872H17.7083",stroke:"white","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)]),8,OS),c("button",{class:"btn-action btn-users",onClick:xt=>a.goToEnrollments(de),title:"Usuários Matriculados"},[c("img",{src:r.icons.users,alt:"Usuários Matriculados"},null,8,TS)],8,SS),c("button",{class:"btn-action btn-edit",onClick:xt=>a.goToeditOfferClass(de),title:"Editar"},t[19]||(t[19]=[c("i",{class:"fas fa-pencil-alt"},null,-1)]),8,NS),c("button",{class:"btn-action btn-duplicate",onClick:xt=>a.duplicateOfferClass(de,ce),title:"Duplicar Turma"},t[20]||(t[20]=[c("i",{class:"fas fa-copy"},null,-1)]),8,IS),c("button",{class:fe(["btn-action",de.status?"btn-deactivate":"btn-activate"]),title:de.status?"Inativar":"Ativar",onClick:xt=>a.requestToggleOfferClassStatus(de)},[c("i",{class:fe(de.status?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,AS),c("button",{class:"btn-action btn-delete",onClick:xt=>a.removeOfferClass(ce,Ce),disabled:!de.canDelete,title:de.canDelete?"Excluir":"Não é possível excluir esta turma"},t[21]||(t[21]=[c("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,MS)])])]))),128))])):(D(),S("div",PS,t[22]||(t[22]=[c("span",null,"Nenhuma turma encontrada para este curso",-1)])))])])]}),_:1},8,["headers","items","sort-by","sort-desc","onSort"]),M(x,{ref:"pagination","current-page":r.currentPage,"onUpdate:currentPage":[t[6]||(t[6]=ce=>r.currentPage=ce),a.handlePageChange],"per-page":r.perPage,"onUpdate:perPage":t[7]||(t[7]=ce=>r.perPage=ce),total:r.totalItems},null,8,["current-page","per-page","total","onUpdate:currentPage"]),s.offerId?(D(),_t(P,{key:0,modelValue:r.showAddCourseModalVisible,"onUpdate:modelValue":t[8]||(t[8]=ce=>r.showAddCourseModalVisible=ce),offerId:s.offerId,onConfirm:a.handleAddCourseConfirm},null,8,["modelValue","offerId","onConfirm"])):Q("",!0),M(L,{size:"md",show:r.showCourseStatusModal,title:(we=r.selectedOfferCourse)!=null&&we.showOfferClassStatusModal?"Ao inativar este curso da oferta, o curso e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:(X=r.selectedOfferCourse)!=null&&X.status?"":"Tem certeza que deseja ativar este curso?","list-items":(pe=r.selectedOfferCourse)!=null&&pe.status?["O curso não será mais disponibilizados na oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":(be=r.selectedOfferCourse)!=null&&be.status?"Inativar curso":"Ativar","cancel-button-text":"Cancelar",icon:(Ae=r.selectedOfferCourse)!=null&&Ae.status?"warning":"question",onClose:t[9]||(t[9]=ce=>r.showCourseStatusModal=!1),onConfirm:a.toggleOfferCourseStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),M(L,{size:"md",show:r.showDeleteOfferCourseModal,title:"A exclusão deste curso da instância de oferta é uma ação irreversível",message:"Ele será desassociado e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir curso","cancel-button-text":"Cancelar",icon:"warning",onClose:t[10]||(t[10]=ce=>r.showDeleteOfferCourseModal=!1),onConfirm:a.deleteOfferCourse},null,8,["show","onConfirm"]),M(L,{size:"md",show:r.showDeleteOfferClassModal,title:"A exclusão desta turma é uma ação irreversível",message:"Todas as configurações realizadas serão excluídas e a turma será removida do curso. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Turma","cancel-button-text":"Cancelar",icon:"warning",onClose:t[11]||(t[11]=ce=>r.showDeleteOfferClassModal=!1),onConfirm:a.deleteOfferClass},null,8,["show","onConfirm"]),M(L,{show:r.showOfferClassStatusModal,size:"md",title:(ae=r.selectedOfferClass)!=null&&ae.status?"Ao inativar esta turma, as matrículas e o curso associados serão tratados da seguinte forma:":"Confirmar Ativação",message:(A=r.selectedOfferClass)!=null&&A.status?"":"Tem certeza que deseja ativar esta turma?","list-items":(Ee=r.selectedOfferClass)!=null&&Ee.status?["Se o curso não possuir outra turma disponível, ele não será mais disponibilizado para novos usuários da oferta. No entanto, matrículas já realizadas permanecerão ativas.","Usuários já matriculados manterão o acesso ao curso normalmente até o encerramento da turma ou da sua matrícula.","Novos alunos não poderão ser matriculados através da oferta."]:[],"confirm-button-text":(ue=r.selectedOfferClass)!=null&&ue.status?"Inativar Turma":"Ativar","cancel-button-text":"Cancelar",icon:(Ge=r.selectedOfferClass)!=null&&Ge.status?"warning":"question",onClose:t[12]||(t[12]=ce=>r.showOfferClassStatusModal=!1),onConfirm:a.toggleOfferClassStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),r.showDuplicateOfferClassModal?(D(),_t(te,{key:1,offerClass:r.offerClassToDuplicate,parentCourse:r.classToDuplicateParentOfferCourse,offerId:s.offerId,onClose:t[13]||(t[13]=ce=>r.showDuplicateOfferClassModal=!1),onSuccess:a.handleDuplicateSuccess,onError:e.showErrorMessage},null,8,["offerClass","parentCourse","offerId","onSuccess","onError"])):Q("",!0),r.selectedOfferCourseForClass?(D(),_t(N,{key:2,show:r.showEnrolTypeModal,offerCourseId:(mt=r.selectedOfferCourseForClass)==null?void 0:mt.id,offerId:s.offerId||"0",onClose:t[14]||(t[14]=ce=>r.showEnrolTypeModal=!1),onConfirm:a.handleEnrolTypeConfirm},null,8,["show","offerCourseId","offerId","onConfirm"])):Q("",!0),M(re,{"is-loading":r.loading},null,8,["is-loading"]),M(J,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])])}const VS=Fe(eS,[["render",kS],["__scopeId","data-v-0a4ec5fe"]]),JM="",RS={name:"OfferEdit",mixins:[oa],components:{Toast:$n,Alert:Y2,LFLoading:Wo,OfferForm:bu,PageHeader:Hr,BackButton:zo,CustomButton:_n,OfferCourseTableList:VS},data(){return{loading:!1,offer:{id:null,name:"",type:"",description:"",status:!1,audiences:[]},showWarning:!0,isEditing:!1,isValidForm:!1}},async created(){const e=parseInt(this.$route.params.id);e&&(await this.getOffer(e),this.isEditing=!0)},methods:{async getOffer(e){try{this.loading=!0;const t=await Yb(e);this.offer={...t,status:!!parseInt(t.status)}}catch(t){this.showErrorMessage(t)}finally{this.loading=!1}},async saveOffer(){if(this.loading=!0,!this.isValidForm){this.loading=!1;return}try{await Xh(this.offer),this.showSuccessMessage("Oferta atualizada com sucesso!")}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},goBack(){this.router.push({name:"offer.index"})}}},FS={id:"new-edit-component",class:"edit-offer"},LS={class:"section-container mt-3"},US={key:0,class:"section-title"},BS={key:1,class:"message-container"},$S={class:"d-flex justify-content-between align-items-center"},HS={class:"actions-container"};function qS(e,t,s,i,r,a){const u=G("BackButton"),d=G("PageHeader"),h=G("Alert"),_=G("OfferForm"),p=G("OfferCourseTableList"),g=G("CustomButton"),w=G("LFLoading"),x=G("Toast");return D(),S("div",FS,[M(d,{title:r.isEditing?`Editar oferta: ${r.offer.name}`:"Adicionar Oferta"},{actions:Se(()=>[M(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"]),r.showWarning?(D(),_t(h,{key:0,type:"primary",icon:"fas fa-exclamation-triangle",text:`Para que uma instância de oferta seja ativada e disponibilize os cursos\r
      para os públicos-alvo configurados, é necessário garantir que pelo menos\r
      um curso, um grupo de público-alvo, e uma turma estejam configurados à\r
      instância de oferta.`})):Q("",!0),c("div",LS,[t[2]||(t[2]=c("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),M(_,{offer:r.offer,"onUpdate:offer":t[0]||(t[0]=P=>r.offer=P),isEditing:r.isEditing,onValidate:t[1]||(t[1]=P=>r.isValidForm=P)},null,8,["offer","isEditing"])]),c("div",{class:fe(["section-container",{"no-title-section":!r.isEditing}])},[r.isEditing?(D(),S("h2",US,"CURSOS")):Q("",!0),r.isEditing?Q("",!0):(D(),S("div",BS,t[3]||(t[3]=[c("div",{class:"lock-message"},[c("i",{class:"fas fa-lock lock-icon"}),c("span",null,"Salve a oferta primeiro para gerenciar os cursos")],-1)]))),r.isEditing?(D(),_t(p,{key:2,offerId:r.offer.id},null,8,["offerId"])):Q("",!0)],2),t[5]||(t[5]=c("hr",null,null,-1)),c("div",$S,[t[4]||(t[4]=c("div",{class:"required-fields-message"},[c("div",{class:"form-info"},[qe(" Este formulário contém campos obrigatórios marcados com "),c("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),c("div",HS,[M(g,{variant:"primary",label:"Salvar",onClick:a.saveOffer,disabled:!r.isValidForm},null,8,["onClick","disabled"]),M(g,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])])]),M(w,{"is-loading":r.loading},null,8,["is-loading"]),M(x,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])])}const WS=Fe(RS,[["render",qS],["__scopeId","data-v-22b69adc"]]),QM="",XM="",jS={name:"CreateEdit",components:{CustomInput:$r,CustomSelect:Xs,CustomButton:_n,PageHeader:Hr,BackButton:zo,Autocomplete:qr,TextEditor:vu,CustomCheckbox:qo,FilterRow:ta,FilterGroup:sa,Toast:$n,HelpIcon:_u,FilterTag:jo,FilterTags:ra},props:{offerCourseId:{type:[Number,String],required:!1},classId:{type:[Number,String],required:!1,default:null}},data(){return{loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,isEditing:!1,enrolmentMethods:[],classData:{enrol:"",offerCourseId:null,classname:"",startdate:"",teachers:[],optional_fields:{enableenddate:!1,enddate:"",enablepreenrolment:!1,preenrolmentstartdate:"",preenrolmentenddate:"",description:"",enableenrolperiod:!1,enrolperiod:null,minusers:null,maxusers:null,roleid:null,divisions:[],sectors:[],groups:[],dealership:[],enablereenrol:!1,reenrolmentsituations:[],enableextension:!1,extensionperiod:null,extensiondaysavailable:null,extensionmaxrequests:null,extensionallowedsituations:[]}},divisionsOptions:[{label:"Divisão 1",value:1}],selectedTeachers:[],teacherSearchTerm:"",debounceTimer:null,teacherList:[],showTeacherDropdown:!1,highlightedIndex:-1,extensionSituations:[],reenrolSituations:[],roleOptions:[],situationList:[],offerCourse:null,formErrors:{enrol:{hasError:!1,message:"Método de inscrição é obrigatório"},classname:{hasError:!1,message:"Nome da turma é obrigatório"},startdate:{hasError:!1,message:"Data de início é obrigatória"},roleid:{hasError:!1,message:"Papel padrão é obrigatório"},enddate:{hasError:!1,message:"Data fim da turma é obrigatória quando habilitada"},preenrolmentstartdate:{hasError:!1,message:"Data início de pré-inscrição é obrigatória quando habilitada"},preenrolmentenddate:{hasError:!1,message:"Data fim de pré-inscrição é obrigatória quando habilitada"},enrolperiod:{hasError:!1,message:"Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma"},extensionperiod:{hasError:!1,message:"Dias para prorrogação é obrigatório quando habilitada"},extensiondaysavailable:{hasError:!1,message:"Dias antes do término é obrigatório quando habilitada"},extensionmaxrequests:{hasError:!1,message:"Máximo de solicitações é obrigatório quando habilitada"},extensionsituations:{hasError:!1,message:"É necessário selecionar pelo menos uma situação de matrícula para prorrogação"},minusers:{hasError:!1,message:"Mínimo de usuários deve ser maior ou igual a zero"},maxusers:{hasError:!1,message:"Máximo de usuários deve ser maior ou igual a zero"}},validationAlert:{show:!1,message:"Há campos obrigatórios a serem preenchidos. Por favor, verifique os campos destacados."}}},async created(){if(this.classId=parseInt(this.classId),this.classId&&(this.isEditing=!0,await this.getClass()),!this.offerCourseId)throw new Error("offerCourseId não foi definido.");await this.getInitialData()},mounted(){window.scrollTo(0,0),document.addEventListener("click",this.handleClickOutside)},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside),this.debounceTimer&&clearTimeout(this.debounceTimer)},computed:{extensionSituationList(){let e=[0,1];return this.situationList.filter(t=>e.includes(t.value))},reenrolSituationList(){let e=[6,7,8,4,5];return this.situationList.filter(t=>e.includes(t.value))},maxEnrolPeriod(){if(this.classData.startdate&&this.classData.optional_fields.enddate&&this.classData.optional_fields.enableenddate){const e=this.calculateDaysDifference(this.classData.startdate,this.classData.optional_fields.enddate);return e>=1?e:1}return null},isOneDayClass(){return this.classData.startdate&&this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&this.classData.startdate===this.classData.optional_fields.enddate},shouldDisableEnrolPeriod(){return this.isOneDayClass}},watch:{extensionSituations:{handler(e){this.classData.optional_fields.extensionallowedsituations=e.map(t=>t.value)},deep:!0},reenrolSituations:{handler(e){this.classData.optional_fields.reenrolmentsituations=e.map(t=>t.value)},deep:!0},"classData.optional_fields.enableenrolperiod":function(e){!e&&this.classData.optional_fields.enableextension&&(this.classData.optional_fields.enableextension=!1,this.showWarningMessage("Prorrogação de matrícula foi desabilitada automaticamente porque o Prazo de conclusão da turma foi desabilitado.")),this.validateField("extensionsituations")},"classData.startdate":function(){this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&this.validateField("enddate"),this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enddate":function(){this.classData.optional_fields.enableenddate&&this.validateField("enddate"),this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.classData.optional_fields.enableenddate&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enableenddate":function(e){e&&this.classData.optional_fields.enddate&&this.validateField("enddate"),!e&&this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enrolperiod":function(e){this.classData.optional_fields.enableenrolperiod&&e&&this.validateField("enrolperiod")},"classData.optional_fields.preenrolmentstartdate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"classData.optional_fields.preenrolmentenddate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"classData.optional_fields.minusers":function(){this.validateField("minusers"),this.classData.optional_fields.maxusers!==null&&this.classData.optional_fields.maxusers!==void 0&&this.validateField("maxusers")},"classData.optional_fields.maxusers":function(){this.validateField("maxusers"),this.classData.optional_fields.minusers!==null&&this.classData.optional_fields.minusers!==void 0&&this.validateField("minusers")}},methods:{calculateDaysDifference(e,t){if(!e||!t)return 0;const s=new Date(e),i=new Date(t);if(isNaN(s.getTime())||isNaN(i.getTime()))return 0;if(s.getTime()===i.getTime())return 1;const r=Math.abs(i-s);return Math.ceil(r/(1e3*60*60*24))},checkAndDisableEnrolPeriodForOneDayClass(){this.isOneDayClass&&this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.enableenrolperiod=!1,this.classData.optional_fields.enrolperiod=null,this.classData.optional_fields.enableextension&&(this.classData.optional_fields.enableextension=!1,this.classData.optional_fields.extensionperiod=null,this.classData.optional_fields.extensiondaysavailable=null,this.classData.optional_fields.extensionmaxrequests=null,this.classData.optional_fields.extensionallowedsituations=[],this.extensionSituations=[]),this.showWarningMessage("Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim)."))},async getInitialData(){try{this.loading=!0,this.isEditing||(this.classData.offerCourseId=this.offerCourseId),await this.loadOfferCourse(),await this.loadRoles(),await this.loadSituations(),await this.loadEnrolmentMethods()}catch{this.showErrorMessage("Alguns dados não puderam ser carregados.")}finally{this.loading=!1}},async loadOfferCourse(){try{const e=await ay(this.offerCourseId);this.offerCourse=e==null?void 0:e.data}catch{this.showErrorMessage("Erro ao carregar informações do curso da oferta.")}},async loadRoles(){const e=await mu(this.offerCourseId);if(this.roleOptions=e.map(t=>({value:t.id,label:t.name})),!this.classData.optional_fields.roleid){const t=this.roleOptions.find(s=>s.value===5);this.classData.optional_fields.roleid=(t==null?void 0:t.value)??this.roleOptions[0].value}},async loadSituations(){const e=await fy();this.situationList=e.map(t=>({value:t.id,label:t.name}))},async loadEnrolmentMethods(){const e=this.route.query.enrol_type;!this.isEditing&&e&&(this.classData.enrol=e);const t=await tp(!0);t&&Array.isArray(t)&&(this.enrolmentMethods=t.map(s=>({value:s.enrol,label:s.name})))},validate(){Object.keys(this.formErrors).forEach(r=>{this.formErrors[r].hasError=!1}),this.validationAlert.show=!1;let e=!1;this.classData.classname||(this.formErrors.classname.hasError=!0,e=!0),this.classData.startdate?this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&new Date(this.classData.startdate)>new Date(this.classData.optional_fields.enddate)?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0,e=!0):this.formErrors.startdate.hasError=!1:(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0,e=!0),this.classData.optional_fields.roleid||(this.formErrors.roleid.hasError=!0,e=!0),this.classData.optional_fields.enableenddate&&(this.classData.optional_fields.enddate?this.classData.startdate&&new Date(this.classData.optional_fields.enddate)<new Date(this.classData.startdate)?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0,e=!0):this.formErrors.enddate.hasError=!1:(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0,e=!0)),this.classData.optional_fields.enablepreenrolment&&(this.classData.optional_fields.preenrolmentstartdate||(this.formErrors.preenrolmentstartdate.hasError=!0,e=!0),this.classData.optional_fields.preenrolmentenddate||(this.formErrors.preenrolmentenddate.hasError=!0,e=!0)),this.validatePreenrolmentDates()||(e=!0),this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.enrolperiod?this.maxEnrolPeriod!==null&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod&&(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0,e=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0,e=!0)),this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.extensionperiod||(this.formErrors.extensionperiod.hasError=!0,e=!0),this.classData.optional_fields.extensiondaysavailable||(this.formErrors.extensiondaysavailable.hasError=!0,e=!0),this.classData.optional_fields.extensionmaxrequests||(this.formErrors.extensionmaxrequests.hasError=!0,e=!0),(!this.extensionSituations||this.extensionSituations.length===0)&&(this.formErrors.extensionsituations.hasError=!0,e=!0));const s=this.validateField("minusers"),i=this.validateField("maxusers");return(!s||!i)&&(e=!0),e&&(this.validationAlert.show=!0,this.showErrorMessage(this.validationAlert.message),window.scrollTo({top:0,behavior:"smooth"})),!e},validateField(e){switch(e){case"enrol":this.formErrors.enrol.hasError=!1;break;case"classname":this.formErrors.classname.hasError=!this.classData.classname;break;case"startdate":const s=this.classData.startdate,i=s&&this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&new Date(this.classData.startdate)>new Date(this.classData.optional_fields.enddate);s?i?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!1):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0);break;case"roleid":this.formErrors.roleid.hasError=!this.classData.optional_fields.roleid;break;case"enddate":const r=this.classData.optional_fields.enableenddate,a=this.classData.optional_fields.enddate,u=r&&a&&this.classData.startdate&&new Date(this.classData.optional_fields.enddate)<new Date(this.classData.startdate);r&&!a?(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0):u?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0):(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!1);break;case"preenrolmentstartdate":this.formErrors.preenrolmentstartdate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentstartdate,this.validatePreenrolmentDates();break;case"preenrolmentenddate":this.formErrors.preenrolmentenddate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentenddate,this.validatePreenrolmentDates();break;case"enrolperiod":const d=this.classData.optional_fields.enableenrolperiod,h=this.classData.optional_fields.enrolperiod!==null&&this.classData.optional_fields.enrolperiod!==void 0&&this.classData.optional_fields.enrolperiod!=="",_=this.maxEnrolPeriod!==null&&h&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod;d&&!h?(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0):d&&_?(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma",this.formErrors.enrolperiod.hasError=!1);break;case"extensionperiod":this.formErrors.extensionperiod.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionperiod;break;case"extensiondaysavailable":this.formErrors.extensiondaysavailable.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensiondaysavailable;break;case"extensionmaxrequests":this.formErrors.extensionmaxrequests.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionmaxrequests;break;case"extensionsituations":this.formErrors.extensionsituations.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&(!this.extensionSituations||this.extensionSituations.length===0);break;case"minusers":this.validateMinUsers();break;case"maxusers":this.validateMaxUsers();break}const t=Object.values(this.formErrors).some(s=>s.hasError);return this.validationAlert.show=t,!this.formErrors[e].hasError},validatePreenrolmentDates(){let e=!0;if(this.formErrors.preenrolmentstartdate.hasError=!1,this.formErrors.preenrolmentenddate.hasError=!1,this.classData.optional_fields.enablepreenrolment){const t=this.classData.startdate,s=this.classData.optional_fields.enableenddate?this.classData.optional_fields.enddate:null,i=this.classData.optional_fields.preenrolmentstartdate,r=this.classData.optional_fields.preenrolmentenddate,a=this.offerCourse.startdate,u=this.offerCourse.enddate;i||(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início de pré-inscrição é obrigatória",e=!1),r||(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim de pré-inscrição é obrigatória",e=!1),new Date(r)<new Date(i)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser posterior à data início",e=!1),new Date(i)>new Date(t)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou anterior à data início da turma",e=!1),new Date(i)<new Date(a)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou posterior à data início do curso",e=!1),u&&new Date(i)>new Date(u)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data de início deve ser igual ou anterior à data fim do curso",e=!1),s&&new Date(r)>=new Date(s)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser anterior à data fim da turma",e=!1),u&&new Date(r)>new Date(u)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser igual ou anterior à data fim do curso",e=!1)}return e},validateMinUsers(){const e=this.classData.optional_fields.minusers,t=this.classData.optional_fields.maxusers,s=parseInt(e),i=parseInt(t);return this.formErrors.minusers.hasError=!1,s===0?!0:i>0&&s>i?(this.formErrors.minusers.message="Mínimo de usuários inscritos deve ser menor que o máximo de usuários inscritos",this.formErrors.minusers.hasError=!0,!1):!0},validateMaxUsers(){const e=this.classData.optional_fields.minusers,t=this.classData.optional_fields.maxusers,s=parseInt(t),i=parseInt(e);return this.formErrors.maxusers.hasError=!1,s===0?!0:i>0&&s<i?(this.formErrors.maxusers.message="Máximo de usuários inscritos deve ser maior que o mínimo de usuários inscritos",this.formErrors.maxusers.hasError=!0,!1):!0},async getClass(){this.loading=!0;const e=await pu(this.classId);if(e.error==!0)throw new Error(e.exception);this.classData=e,this.offerCourseId=parseInt(e.offercourseid),e.optional_fields&&this.processOptionalFields(e.optional_fields),e.teachers&&(this.selectedTeachers=e.teachers),this.updateUIAfterLoading(),document.title=`Editar Turma: ${this.classData.classname}`,this.loading=!1},processOptionalFields(e){this.processDateFields(e),this.processEnrolmentFields(e),this.processStructureFields(e),this.processUserLimits(e),this.processDescriptionAndRole(e),this.processReenrolment(e),this.processExtensionFields(e)},processStructureFields(e){this.classData.optional_fields.divisions=[],this.classData.optional_fields.sectors=[],this.classData.optional_fields.groups=[],this.classData.optional_fields.dealership=[]},processDateFields(e){e.enableenddate&&(this.classData.optional_fields.enableenddate=!0,this.classData.optional_fields.enddate=e.enddate||null),e.enablepreenrolment&&(this.classData.optional_fields.enablepreenrolment=!0,this.classData.optional_fields.preenrolmentstartdate=e.preenrolmentstartdate||null,this.classData.optional_fields.preenrolmentenddate=e.preenrolmentenddate||null)},processEnrolmentFields(e){e.enableenrolperiod?(this.classData.optional_fields.enableenrolperiod=!0,this.classData.optional_fields.enrolperiod=e.enrolperiod>0?e.enrolperiod:null):this.classData.optional_fields.enrolperiod=null},processUserLimits(e){this.classData.optional_fields.minusers=e.minusers>0?e.minusers:null,this.classData.optional_fields.maxusers=e.maxusers>0?e.maxusers:null},processDescriptionAndRole(e){this.classData.optional_fields.roleid=e.roleid||null,this.classData.optional_fields.description=e.description||""},processReenrolment(e){e.enablereenrol?(this.classData.optional_fields.enablereenrol=!0,this.classData.optional_fields.reenrolmentsituations=e.reenrolmentsituations||[],Array.isArray(e.reenrolmentsituations)&&(this.reenrolSituations=e.reenrolmentsituations.map(t=>this.situationList.find(s=>s.value===parseInt(t))))):this.classData.optional_fields.reenrolmentsituations=[]},processExtensionFields(e){e.enableextension&&e.enableenrolperiod?(this.classData.optional_fields.enableextension=!0,this.processExtensionPeriods(e),this.processExtensionSituations(e)):this.resetExtensionFields()},processExtensionPeriods(e){this.classData.optional_fields.extensionperiod=e.extensionperiod>0?e.extensionperiod:null,this.classData.optional_fields.extensiondaysavailable=e.extensiondaysavailable>0?e.extensiondaysavailable:null,this.classData.optional_fields.extensionmaxrequests=e.extensionmaxrequests>0?e.extensionmaxrequests:null},processExtensionSituations(e){Array.isArray(e.extensionallowedsituations)&&(this.extensionSituations=e.extensionallowedsituations.map(t=>this.situationList.find(s=>s.value===t)))},resetExtensionFields(){this.classData.optional_fields.extensionperiod=null,this.classData.optional_fields.extensiondaysavailable=null,this.classData.optional_fields.extensionmaxrequests=null,this.classData.optional_fields.extensionallowedsituations=[],this.extensionSituations=[]},handleTeacherInput(){const e=this.teacherSearchTerm.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?(this.showTeacherDropdown=!0,this.highlightedIndex=-1,this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialTeachers(e)},500)):(this.showTeacherDropdown=!1,this.teacherList=[])},async fetchPotentialTeachers(e){let t=this.selectedTeachers.map(s=>s.id||s.value)??[];this.teacherList=await dy(this.offerCourseId,this.classId,e,t)},removeTeacher(e){this.selectedTeachers=this.selectedTeachers.filter(t=>t.id!==e)},handleTeacherInputFocus(){this.teacherSearchTerm.length>=3&&this.teacherList.length>0&&(this.showTeacherDropdown=!0)},selectTeacher(e){this.selectedTeachers.push({id:e.id,value:e.id,fullname:e.fullname,email:e.email}),this.teacherSearchTerm="",this.showTeacherDropdown=!1,this.highlightedIndex=-1,this.teacherList=[],this.$nextTick(()=>{this.$refs.teacherSearchInput&&this.$refs.teacherSearchInput.focus()})},handleKeydown(e){if(!(!this.showTeacherDropdown||this.teacherList.length===0))switch(e.key){case"ArrowDown":e.preventDefault(),this.highlightedIndex=Math.min(this.highlightedIndex+1,this.teacherList.length-1);break;case"ArrowUp":e.preventDefault(),this.highlightedIndex=Math.max(this.highlightedIndex-1,0);break;case"Enter":e.preventDefault(),this.highlightedIndex>=0&&this.highlightedIndex<this.teacherList.length&&this.selectTeacher(this.teacherList[this.highlightedIndex]);break;case"Escape":e.preventDefault(),this.showTeacherDropdown=!1,this.highlightedIndex=-1;break}},handleClickOutside(e){this.$refs.teacherSearchContainer&&!this.$refs.teacherSearchContainer.contains(e.target)&&(this.showTeacherDropdown=!1,this.highlightedIndex=-1)},updateUIAfterLoading(){this.$nextTick(()=>{this.updateFormFields(),this.$forceUpdate(),(!this.classData.classname||!this.classData.enrol)&&this.showErrorMessage("Dados incompletos após carregamento.")})},updateFormFields(){this.updateSelectField("enrolSelect",this.classData.enrol),this.updateInputField("classnameInput",this.classData.classname),this.updateInputField("startdateInput",this.classData.startdate)},updateSelectField(e,t){if(this.$refs[e]){this.$refs[e].value=t;const s=new Event("change");this.$refs[e].$el.dispatchEvent(s)}},updateInputField(e,t){if(this.$refs[e]&&t){this.$refs[e].value=t;const s=new Event("input");this.$refs[e].$el.dispatchEvent(s)}},async saveClass(){if(!this.validate())return;this.loading=!0;const e=JSON.parse(JSON.stringify(this.classData));e.teachers=this.selectedTeachers.map(r=>r.id),!e.optional_fields.enableextension||!e.optional_fields.enableenrolperiod?(e.optional_fields.extensionperiod=void 0,e.optional_fields.extensiondaysavailable=void 0,e.optional_fields.extensionmaxrequests=void 0,e.optional_fields.extensionallowedsituations=[],e.optional_fields.enableenrolperiod||(e.optional_fields.enableextension=!1)):e.optional_fields.extensionallowedsituations=this.extensionSituations.map(r=>r.value),e.optional_fields.enableenrolperiod||(e.optional_fields.enrolperiod=void 0),e.optional_fields.enablereenrol?e.optional_fields.reenrolmentsituations=this.reenrolSituations.map(r=>r.value):e.optional_fields.reenrolmentsituations=[],["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].forEach(r=>{const a=e.optional_fields[r];(a===0||a===null||a===""||a===void 0)&&(e.optional_fields[r]=void 0)}),this.isEditing&&"enrol"in e&&delete e.enrol;const i=(this.isEditing?["offerCourseId","classname","startdate"]:["offerCourseId","classname","startdate","enrol"]).filter(r=>!e[r]);if(i.length>0){this.showErrorMessage(`Campos obrigatórios ausentes: ${i.join(", ")}`),this.loading=!1;return}if(e.offerCourseId=parseInt(e.offerCourseId),this.isEditing&&this.classId){e.offerclassid=this.classId;let r=await uy(e);this.showSuccessMessage(r.message),this.getClass()}else{let r=await iy(e);this.showSuccessMessage(r.message),this.isEditing=!0,this.router.push({name:"EditClass",params:{offerCourseId:this.offerCourseId,classId:r.offerclassid}})}this.loading=!1},goBack(){this.offerCourse.offerid?this.router.push({name:"offer.edit",params:{id:this.offerCourse.offerid}}):this.router.push({name:"offer.index"})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},updateFormFields(){if(this.$refs.enrolSelect&&this.classData.enrol)try{this.$refs.enrolSelect.value=this.classData.enrol;const e=new Event("change");this.$refs.enrolSelect.$el.dispatchEvent(e),this.$refs.enrolSelect.$emit("input",this.classData.enrol),this.$refs.enrolSelect.$forceUpdate()}catch{}if(this.$refs.classnameInput&&this.classData.classname)try{this.$refs.classnameInput.value=this.classData.classname;const e=new Event("input");this.$refs.classnameInput.$el.dispatchEvent(e),this.$refs.classnameInput.$emit("input",this.classData.classname),this.$refs.classnameInput.$forceUpdate()}catch{}if(this.$refs.startdateInput&&this.classData.startdate)try{this.$refs.startdateInput.value=this.classData.startdate;const e=new Event("input");this.$refs.startdateInput.$el.dispatchEvent(e),this.$refs.startdateInput.$emit("input",this.classData.startdate),this.$refs.startdateInput.$forceUpdate()}catch{}this.$forceUpdate()},handleSelectAllExtensionSituations(){const e=this.extensionSituationList.every(t=>this.extensionSituations.some(s=>s.value===t.value));this.extensionSituations=e?[]:[...this.extensionSituationList],this.classData.optional_fields.extensionallowedsituations=this.extensionSituations.map(t=>t.value)},handleSelectAllReenrolSituations(){const e=this.reenrolSituationList.every(t=>this.reenrolSituations.some(s=>s.value===t.value));this.reenrolSituations=e?[]:[...this.reenrolSituationList],this.classData.optional_fields.reenrolmentsituations=this.reenrolSituations.map(t=>t.value)},restartComponent(){window.scrollTo(0,0),this.updateFormFields(),this.$forceUpdate(),setTimeout(()=>{this.updateFormFields(),this.$forceUpdate(),window.scrollTo(0,0)},500)}}},zS={class:"new-class",ref:"classView"},GS={class:"page-header-container"},KS={key:0,class:"validation-alert"},ZS={class:"section-container"},YS={class:"form-group mb-3"},JS={class:"label-with-help"},QS={class:"limited-width-input",style:{"max-width":"280px"}},XS={class:"form-row mb-3"},eT={class:"form-group"},tT={class:"label-with-help"},sT={class:"limited-width-input"},nT={class:"label-with-help"},rT={class:"input-with-checkbox"},oT={class:"limited-width-input"},iT={class:"form-row mb-3"},aT={class:"label-with-help"},lT={class:"label-with-help"},uT={key:2,class:"form-group"},cT={class:"form-group mb-3"},dT={class:"label-with-help"},fT={class:"limited-width-editor"},hT={class:"form-row mb-3"},pT={key:0,class:"form-group"},mT={class:"label-with-help"},gT={class:"limited-width-input"},_T={key:1,class:"form-group"},vT={class:"label-with-help"},bT={class:"limited-width-input"},yT={class:"form-group"},wT={class:"label-with-help"},ET={class:"limited-width-input"},CT={class:"form-group"},xT={class:"limited-width-input"},DT={class:"form-row"},OT={class:"label-with-help"},ST={class:"row"},TT={class:"col-md-4 col-lg-3"},NT={class:"form-group"},IT={class:"input-container"},AT={class:"col-md-4 col-lg-3"},MT={class:"form-group"},PT={class:"input-container"},kT={class:"col-md-4 col-lg-3"},VT={class:"form-group"},RT={class:"input-container"},FT={class:"col-md-4 col-lg-3"},LT={class:"form-group"},UT={class:"input-container"},BT={class:"row"},$T={class:"form-group"},HT={class:"label-with-help"},qT={class:"limited-width-input"},WT={class:"form-row mb-3"},jT={class:"label-with-help"},zT={class:"input-with-checkbox"},GT={class:"limited-width-input"},KT={class:"section-container"},ZT={class:"form-row mb-3"},YT={class:"label-with-help"},JT={class:"form-row mb-3"},QT={class:"limited-width-input"},XT={class:"form-row mb-3"},e5={class:"limited-width-input"},t5={class:"form-row mb-3"},s5={class:"limited-width-input"},n5={class:"limited-width-select"},r5={key:0,class:"text-danger"},o5={key:1,class:"section-container"},i5={class:"form-row mb-3"},a5={class:"form-group"},l5={class:"label-with-help"},u5={class:"limited-width-select"},c5={class:"section-container"},d5={class:"form-group mb-3"},f5={class:"label-with-help"},h5={class:"limited-width-select"},p5={class:"position-relative",ref:"teacherSearchContainer"},m5={class:"input-wrapper with-icon"},g5={key:0,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","max-height":"200px","overflow-y":"auto","z-index":"1000","border-top":"none","border-radius":"0 0 0.375rem 0.375rem"},ref:"teacherDropdown"},_5=["onClick","onMouseenter"],v5={key:0,class:"text-muted small"},b5={key:1,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","z-index":"1000","border-top":"none","border-radius":"0 0 0.375rem 0.375rem"}},y5={class:"my-4"},w5=["onClick"],E5={class:"actions-container"},C5={key:2,class:"loading"};function x5(e,t,s,i,r,a){const u=G("BackButton"),d=G("PageHeader"),h=G("HelpIcon"),_=G("CustomInput"),p=G("CustomCheckbox"),g=G("TextEditor"),w=G("CustomSelect"),x=G("Autocomplete"),P=G("CustomButton"),L=G("Toast"),te=p_("tooltip");return D(),S("div",zS,[c("div",GS,[M(d,{title:r.isEditing?"Editar turma":"Adicionar Turma"},{actions:Se(()=>[M(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"])]),r.validationAlert.show?(D(),S("div",KS,[t[45]||(t[45]=c("i",{class:"fas fa-exclamation-triangle"},null,-1)),c("span",null,W(r.validationAlert.message),1)])):Q("",!0),c("div",ZS,[t[69]||(t[69]=c("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),c("div",YS,[c("div",JS,[t[46]||(t[46]=c("label",{class:"form-label"},"Nome da turma",-1)),t[47]||(t[47]=c("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),M(h,{title:"Ajuda com nome da turma",text:"Insira um nome para a turma. Exemplo: Turma ADM 2025."})]),c("div",QS,[M(_,{modelValue:r.classData.classname,"onUpdate:modelValue":t[0]||(t[0]=N=>r.classData.classname=N),placeholder:"Digite o nome da turma",width:280,required:"",ref:"classnameInput","has-error":r.formErrors.classname.hasError,"error-message":r.formErrors.classname.message,onValidate:t[1]||(t[1]=N=>a.validateField("classname"))},null,8,["modelValue","has-error","error-message"])])]),c("div",XS,[c("div",eT,[c("div",tT,[t[48]||(t[48]=c("label",{class:"form-label"},"Data de início",-1)),t[49]||(t[49]=c("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),M(h,{title:"Ajuda com data início da turma",text:"Insira uma data de início para a turma. Exemplo: 07/04/2025."})]),c("div",sT,[M(_,{modelValue:r.classData.startdate,"onUpdate:modelValue":t[2]||(t[2]=N=>r.classData.startdate=N),type:"date",width:180,required:"",class:"date-input",ref:"startdateInput","has-error":r.formErrors.startdate.hasError,"error-message":r.formErrors.startdate.message,onValidate:t[3]||(t[3]=N=>a.validateField("startdate"))},null,8,["modelValue","has-error","error-message"])])]),c("div",{class:fe(["form-group",{disabled:!r.classData.optional_fields.enableenddate}])},[c("div",nT,[t[50]||(t[50]=c("label",{class:"form-label"},"Data de término",-1)),t[51]||(t[51]=c("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),M(h,{title:"Ajuda com data término da turma",text:"Insira uma data de término para a turma, caso haja. Exemplo: 07/12/2025."})]),c("div",rT,[c("div",oT,[M(_,{modelValue:r.classData.optional_fields.enddate,"onUpdate:modelValue":t[4]||(t[4]=N=>r.classData.optional_fields.enddate=N),type:"date",width:180,disabled:!r.classData.optional_fields.enableenddate,required:"",class:"date-input","has-error":r.formErrors.enddate.hasError,"error-message":r.formErrors.enddate.message,onValidate:t[5]||(t[5]=N=>a.validateField("enddate"))},null,8,["modelValue","disabled","has-error","error-message"])]),M(p,{modelValue:r.classData.optional_fields.enableenddate,"onUpdate:modelValue":t[6]||(t[6]=N=>r.classData.optional_fields.enableenddate=N),id:"enableEndDate",label:"Habilitar data de termínio",class:"inline-checkbox",disabled:!1},null,8,["modelValue"])])],2)]),c("div",iT,[r.classData.enrol=="offer_self"?(D(),S("div",{key:0,class:fe(["form-group",{disabled:!r.classData.optional_fields.enablepreenrolment}])},[c("div",aT,[t[52]||(t[52]=c("label",{class:"form-label"},"Data de início da inscrição",-1)),M(h,{title:"Ajuda com data de início da inscrição",text:"Data de início do período de inscrição."})]),M(_,{modelValue:r.classData.optional_fields.preenrolmentstartdate,"onUpdate:modelValue":t[7]||(t[7]=N=>r.classData.optional_fields.preenrolmentstartdate=N),type:"date",width:180,disabled:!r.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":r.formErrors.preenrolmentstartdate.hasError,"error-message":r.formErrors.preenrolmentstartdate.message,onValidate:t[8]||(t[8]=N=>a.validateField("preenrolmentstartdate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):Q("",!0),r.classData.enrol=="offer_self"?(D(),S("div",{key:1,class:fe(["form-group",{disabled:!r.classData.optional_fields.enablepreenrolment}])},[c("div",lT,[t[53]||(t[53]=c("label",{class:"form-label"},"Data de término da inscrição",-1)),M(h,{title:"Ajuda com data término da inscrição",text:"Data de término do período de inscrição."})]),M(_,{modelValue:r.classData.optional_fields.preenrolmentenddate,"onUpdate:modelValue":t[9]||(t[9]=N=>r.classData.optional_fields.preenrolmentenddate=N),type:"date",width:180,disabled:!r.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":r.formErrors.preenrolmentenddate.hasError,"error-message":r.formErrors.preenrolmentenddate.message,onValidate:t[10]||(t[10]=N=>a.validateField("preenrolmentenddate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):Q("",!0),r.classData.enrol=="offer_self"?(D(),S("div",uT,[t[54]||(t[54]=c("div",{class:"label-with-help"},[c("label",{class:"form-label"}," ")],-1)),M(p,{modelValue:r.classData.optional_fields.enablepreenrolment,"onUpdate:modelValue":t[11]||(t[11]=N=>r.classData.optional_fields.enablepreenrolment=N),id:"enablePreEnrolment",label:"Habilitar a inscrição",disabled:!1},null,8,["modelValue"])])):Q("",!0)]),c("div",cT,[c("div",dT,[t[55]||(t[55]=c("label",{class:"form-label"},"Descrição da turma",-1)),M(h,{title:"Ajuda com descrição da turma",text:"Esta descrição estará disponível para os usuários na página intermediária do curso. Exemplo: Esta turma se destina a usuários com os cargos administrativos que foram selecionados para a realização dos cursos obrigatórios do ano de 2025."})]),c("div",fT,[M(g,{modelValue:r.classData.optional_fields.description,"onUpdate:modelValue":t[12]||(t[12]=N=>r.classData.optional_fields.description=N),placeholder:"Digite a descrição da turma aqui...",rows:5,disabled:!1},null,8,["modelValue"])])]),c("div",hT,[r.classData.enrol=="offer_self"?(D(),S("div",pT,[c("div",mT,[t[56]||(t[56]=c("label",{class:"form-label"},"Número mínimo de inscrições",-1)),M(h,{title:"Ajuda com número mínimo de inscrições",text:"Insira um número mínimo de inscrições (vagas) para a turma, se houver. Exemplo: 20."})]),c("div",gT,[M(_,{modelValue:r.classData.optional_fields.minusers,"onUpdate:modelValue":t[13]||(t[13]=N=>r.classData.optional_fields.minusers=N),type:"number",width:180,"has-error":r.formErrors.minusers.hasError,"error-message":r.formErrors.minusers.message,onValidate:t[14]||(t[14]=N=>a.validateField("minusers")),min:0},null,8,["modelValue","has-error","error-message"])])])):Q("",!0),r.classData.enrol=="offer_self"?(D(),S("div",_T,[c("div",vT,[t[57]||(t[57]=c("label",{class:"form-label"},"Número máximo de inscrições",-1)),M(h,{title:"Ajuda com máximo de usuários",text:"Insira um número máximo de inscrições (vagas) para a turma. Exemplo: 100."})]),c("div",bT,[M(_,{modelValue:r.classData.optional_fields.maxusers,"onUpdate:modelValue":t[15]||(t[15]=N=>r.classData.optional_fields.maxusers=N),type:"number",width:180,"has-error":r.formErrors.maxusers.hasError,"error-message":r.formErrors.maxusers.message,onValidate:t[16]||(t[16]=N=>a.validateField("maxusers")),min:0},null,8,["modelValue","has-error","error-message"])])])):Q("",!0),c("div",yT,[c("div",wT,[t[58]||(t[58]=c("label",{class:"form-label"},"Perfil atribuído por padrão",-1)),t[59]||(t[59]=c("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),M(h,{title:"Ajuda com perfil atribuído por padrão",text:`Caso a turma seja criada para perfis de 'Aluno Nissan' e ‘Aluno Concessionária’, o perfil padrão selecionado será ‘Estudante’.<br><br>\r
                    Caso a turma seja criada para perfis de ‘Gestores’, o perfil padrão selecionado será ‘Gestor’ ou outro perfil pertinente.`})]),c("div",ET,[M(w,{modelValue:r.classData.optional_fields.roleid,"onUpdate:modelValue":t[17]||(t[17]=N=>r.classData.optional_fields.roleid=N),options:r.roleOptions,width:180,required:"","has-error":r.formErrors.roleid.hasError,"error-message":r.formErrors.roleid.message,onValidate:t[18]||(t[18]=N=>a.validateField("roleid"))},null,8,["modelValue","options","has-error","error-message"])])]),c("div",CT,[t[60]||(t[60]=c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Modalidade da turma"),c("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),c("div",xT,[M(w,{modelValue:r.classData.optional_fields.roleid,"onUpdate:modelValue":t[19]||(t[19]=N=>r.classData.optional_fields.roleid=N),options:r.roleOptions,width:180,required:"","has-error":r.formErrors.roleid.hasError,"error-message":r.formErrors.roleid.message,onValidate:t[20]||(t[20]=N=>a.validateField("roleid"))},null,8,["modelValue","options","has-error","error-message"])])])]),c("div",null,[c("div",DT,[c("div",{class:fe(["form-group",{disabled:!r.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[c("div",OT,[t[61]||(t[61]=c("label",{class:"form-label"},"Habilitar restrição por estruturas",-1)),M(h,{title:"Ajuda com prorrogação de matrícula",text:"Ao habilitar esta opção, os campos 'Divisão', 'Setor', 'Grupo' e 'Concessionária' serão automaticamente ajustados conforme as seleções realizadas nos respectivos filtros."})]),at(M(p,{modelValue:r.classData.optional_fields.enableextension,"onUpdate:modelValue":t[21]||(t[21]=N=>r.classData.optional_fields.enableextension=N),id:"enableExtension",label:"Habilitar Prorrogação de matrícula",disabled:!r.classData.optional_fields.enableenrolperiod},null,8,["modelValue","disabled"]),[[te,r.classData.optional_fields.enableenrolperiod?"":"É necessário habilitar o Prazo de conclusão da turma primeiro"]])],2)]),c("div",ST,[c("div",TT,[c("div",NT,[t[62]||(t[62]=c("div",{class:"label-container"},[c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Divisão")])],-1)),c("div",IT,[M(x,{class:"autocomplete-audiences",modelValue:r.classData.optional_fields.divisions,"onUpdate:modelValue":t[22]||(t[22]=N=>r.classData.optional_fields.divisions=N),items:r.divisionsOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0},null,8,["modelValue","items"])])])]),c("div",AT,[c("div",MT,[t[63]||(t[63]=c("div",{class:"label-container"},[c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Setor")])],-1)),c("div",PT,[M(x,{class:"autocomplete-audiences",modelValue:r.classData.optional_fields.sectors,"onUpdate:modelValue":t[23]||(t[23]=N=>r.classData.optional_fields.sectors=N),items:r.divisionsOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0},null,8,["modelValue","items"])])])]),c("div",kT,[c("div",VT,[t[64]||(t[64]=c("div",{class:"label-container"},[c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Grupo")])],-1)),c("div",RT,[M(x,{class:"autocomplete-audiences",modelValue:r.classData.optional_fields.groups,"onUpdate:modelValue":t[24]||(t[24]=N=>r.classData.optional_fields.groups=N),items:r.divisionsOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0},null,8,["modelValue","items"])])])]),c("div",FT,[c("div",LT,[t[65]||(t[65]=c("div",{class:"label-container"},[c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Concessionária")])],-1)),c("div",UT,[M(x,{class:"autocomplete-audiences",modelValue:r.classData.optional_fields.dealership,"onUpdate:modelValue":t[25]||(t[25]=N=>r.classData.optional_fields.dealership=N),items:r.divisionsOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0},null,8,["modelValue","items"])])])])])]),c("div",BT,[c("div",$T,[c("div",HT,[t[66]||(t[66]=c("label",{class:"form-label"},"Número máximo de inscrições por concessionária",-1)),M(h,{title:"Ajuda com número máximo de inscrições por concessionária",text:"Insira um número máximo de inscrições por concessionária para a turma, se houver. Exemplo: 5."})]),c("div",qT,[M(_,{modelValue:r.classData.optional_fields.minusers,"onUpdate:modelValue":t[26]||(t[26]=N=>r.classData.optional_fields.minusers=N),type:"number",width:180,"has-error":r.formErrors.minusers.hasError,"error-message":r.formErrors.minusers.message,onValidate:t[27]||(t[27]=N=>a.validateField("minusers")),min:0},null,8,["modelValue","has-error","error-message"])])])]),c("div",WT,[c("div",{class:fe(["form-group",{disabled:!r.classData.optional_fields.enableenrolperiod}])},[c("div",jT,[t[67]||(t[67]=c("label",{class:"form-label"},"Duração da matrícula",-1)),t[68]||(t[68]=c("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),M(h,{title:"Ajuda com duração da matrícula",text:"Insira um período em dias para a duração da matricula dos alunos, se houver, na turma. Exemplo: 15"+(a.maxEnrolPeriod?` O valor máximo permitido é de ${a.maxEnrolPeriod} dias, que corresponde ao período entre as datas de início e fim da turma.`:"")},null,8,["text"])]),c("div",zT,[c("div",GT,[M(_,{modelValue:r.classData.optional_fields.enrolperiod,"onUpdate:modelValue":t[28]||(t[28]=N=>r.classData.optional_fields.enrolperiod=N),type:"number",width:180,disabled:!r.classData.optional_fields.enableenrolperiod,required:"","has-error":r.formErrors.enrolperiod.hasError,"error-message":r.formErrors.enrolperiod.message,max:a.maxEnrolPeriod,onValidate:t[29]||(t[29]=N=>a.validateField("enrolperiod"))},null,8,["modelValue","disabled","has-error","error-message","max"])]),at(M(p,{modelValue:r.classData.optional_fields.enableenrolperiod,"onUpdate:modelValue":t[30]||(t[30]=N=>r.classData.optional_fields.enableenrolperiod=N),id:"enableEnrolPeriod",label:"Habilitar duração da matrícula",class:"inline-checkbox",disabled:a.shouldDisableEnrolPeriod},null,8,["modelValue","disabled"]),[[te,a.shouldDisableEnrolPeriod?"Não é possível habilitar duração da matrícula para turmas de um dia (data início = data fim)":""]])])],2)])]),c("div",KT,[c("div",ZT,[c("div",{class:fe(["form-group",{disabled:!r.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[c("div",YT,[t[70]||(t[70]=c("label",{class:"form-label"},"Prorrogação de matrícula",-1)),M(h,{title:"Ajuda com prorrogação de matrícula",text:"A prorrogação estende a duração da matrícula do usuário, permitindo sua permanência na turma enquanto ela estiver ativa. No entanto, não redefine seu progresso, garantindo que ele retome o curso de onde parou. Nota: A prorrogação só pode ser habilitada quando o Prazo de conclusão da turma estiver habilitado."})]),at(M(p,{modelValue:r.classData.optional_fields.enableextension,"onUpdate:modelValue":t[31]||(t[31]=N=>r.classData.optional_fields.enableextension=N),id:"enableExtension",label:"Habilitar prorrogação de matrícula",disabled:!r.classData.optional_fields.enableenrolperiod},null,8,["modelValue","disabled"]),[[te,r.classData.optional_fields.enableenrolperiod?"":"É necessário habilitar o duração da matrícula primeiro"]])],2)]),c("div",JT,[c("div",{class:fe(["form-group",{disabled:!r.classData.optional_fields.enableextension||!r.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[71]||(t[71]=c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Quantos dias serão acrescentados para prorrogação?"),c("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),c("div",QT,[M(_,{modelValue:r.classData.optional_fields.extensionperiod,"onUpdate:modelValue":t[32]||(t[32]=N=>r.classData.optional_fields.extensionperiod=N),type:"number",width:180,disabled:!r.classData.optional_fields.enableextension||!r.classData.optional_fields.enableenrolperiod,required:"","has-error":r.formErrors.extensionperiod.hasError,"error-message":r.formErrors.extensionperiod.message,onValidate:t[33]||(t[33]=N=>a.validateField("extensionperiod"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),c("div",XT,[c("div",{class:fe(["form-group",{disabled:!r.classData.optional_fields.enableextension||!r.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[72]||(t[72]=c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Quantos dias antes do término do prazo de matrícula o botão de prorrogação deve ser exibido?"),c("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),c("div",e5,[M(_,{modelValue:r.classData.optional_fields.extensiondaysavailable,"onUpdate:modelValue":t[34]||(t[34]=N=>r.classData.optional_fields.extensiondaysavailable=N),type:"number",width:180,disabled:!r.classData.optional_fields.enableextension||!r.classData.optional_fields.enableenrolperiod,required:"","has-error":r.formErrors.extensiondaysavailable.hasError,"error-message":r.formErrors.extensiondaysavailable.message,onValidate:t[35]||(t[35]=N=>a.validateField("extensiondaysavailable"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),c("div",t5,[c("div",{class:fe(["form-group",{disabled:!r.classData.optional_fields.enableextension||!r.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[73]||(t[73]=c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Quantas vezes o usuário pode pedir prorrogação?"),c("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),c("div",s5,[M(_,{modelValue:r.classData.optional_fields.extensionmaxrequests,"onUpdate:modelValue":t[36]||(t[36]=N=>r.classData.optional_fields.extensionmaxrequests=N),type:"number",width:180,disabled:!r.classData.optional_fields.enableextension||!r.classData.optional_fields.enableenrolperiod,required:"","has-error":r.formErrors.extensionmaxrequests.hasError,"error-message":r.formErrors.extensionmaxrequests.message,onValidate:t[37]||(t[37]=N=>a.validateField("extensionmaxrequests"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),c("div",{class:fe(["form-group mb-3",{disabled:!r.classData.optional_fields.enableextension||!r.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[74]||(t[74]=c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Para quais situações de matrícula é permitida a prorrogação?")],-1)),c("div",n5,[M(x,{modelValue:r.extensionSituations,"onUpdate:modelValue":t[38]||(t[38]=N=>r.extensionSituations=N),items:a.extensionSituationList,placeholder:"Selecione as situações...",disabled:!r.classData.optional_fields.enableextension||!r.classData.optional_fields.enableenrolperiod,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllExtensionSituations},null,8,["modelValue","items","disabled","onSelectAll"]),r.formErrors.extensionsituations.hasError?(D(),S("div",r5,W(r.formErrors.extensionsituations.message),1)):Q("",!0)])],2)]),r.classData.enrol=="offer_self"?(D(),S("div",o5,[c("div",i5,[c("div",a5,[c("div",l5,[t[75]||(t[75]=c("label",{class:"form-label"},"Habilitar rematrícula",-1)),M(h,{title:"Ajuda com habilitar rematrícula",text:"Permite que usuários se matriculem novamente na turma após concluírem ou saírem dela."})]),M(p,{modelValue:r.classData.optional_fields.enablereenrol,"onUpdate:modelValue":t[39]||(t[39]=N=>r.classData.optional_fields.enablereenrol=N),id:"enableReenrol",label:"Habilitar rematrícula",disabled:!1},null,8,["modelValue"])])]),c("div",{class:fe(["form-group mb-3",{disabled:!r.classData.optional_fields.enablereenrol}])},[t[76]||(t[76]=c("div",{class:"label-with-help"},[c("label",{class:"form-label"},"Quais situações de matrícula permitem rematrícula?")],-1)),c("div",u5,[M(x,{modelValue:r.reenrolSituations,"onUpdate:modelValue":t[40]||(t[40]=N=>r.reenrolSituations=N),items:a.reenrolSituationList,placeholder:"Selecione as situações...",disabled:!r.classData.optional_fields.enablereenrol,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllReenrolSituations},null,8,["modelValue","items","disabled","onSelectAll"])])],2)])):Q("",!0),c("div",c5,[c("div",d5,[c("div",f5,[t[77]||(t[77]=c("label",{class:"form-label"},"Atribuir corpo docente",-1)),M(h,{title:"Ajuda com atribuir corpo docente",text:"Ao selecionar usuários para a composição do corpo docente, ele será matriculado na turma com o papel “Professor”."})]),c("div",h5,[c("div",p5,[c("div",m5,[at(c("input",{type:"text","onUpdate:modelValue":t[41]||(t[41]=N=>r.teacherSearchTerm=N),placeholder:"Pesquisar ...",class:"form-control custom-input",onInput:t[42]||(t[42]=(...N)=>a.handleTeacherInput&&a.handleTeacherInput(...N)),onFocus:t[43]||(t[43]=(...N)=>a.handleTeacherInputFocus&&a.handleTeacherInputFocus(...N)),onKeydown:t[44]||(t[44]=(...N)=>a.handleKeydown&&a.handleKeydown(...N)),ref:"teacherSearchInput"},null,544),[[Yt,r.teacherSearchTerm]])]),r.showTeacherDropdown&&r.teacherList.length>0?(D(),S("div",g5,[(D(!0),S(Ie,null,lt(r.teacherList,(N,re)=>(D(),S("div",{key:N.id,class:fe(["dropdown-item",{active:r.highlightedIndex===re}]),onClick:J=>a.selectTeacher(N),onMouseenter:J=>r.highlightedIndex=re},[c("div",null,[c("div",null,W(N.fullname),1),N.email?(D(),S("div",v5,W(N.email),1)):Q("",!0)])],42,_5))),128))],512)):Q("",!0),r.showTeacherDropdown&&r.teacherSearchTerm.length>=3&&r.teacherList.length===0?(D(),S("div",b5,t[78]||(t[78]=[c("div",{class:"dropdown-item-text text-center fst-italic"}," Nenhum professor encontrado ",-1)]))):Q("",!0)],512),c("div",y5,[(D(!0),S(Ie,null,lt(r.selectedTeachers,N=>(D(),S("a",{key:N.id,class:"tag badge bg-primary text-white p-2 cursor-pointer mr-2",onClick:re=>a.removeTeacher(N.id)},[t[79]||(t[79]=c("i",{class:"fas fa-times mr-1"},null,-1)),qe(" "+W(N.fullname),1)],8,w5))),128))])])])]),t[81]||(t[81]=c("div",{class:"required-fields-message"},[c("div",{class:"form-info"},[qe(" Este formulário contém campos obrigatórios marcados com "),c("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),c("div",E5,[M(P,{variant:"primary",label:"Salvar",loading:r.loading,onClick:a.saveClass},null,8,["loading","onClick"]),M(P,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])]),r.loading?(D(),S("div",C5,t[80]||(t[80]=[c("div",{class:"spinner-border",role:"status"},[c("span",{class:"sr-only"},"Carregando...")],-1)]))):Q("",!0),M(L,{show:r.showToast,message:r.toastMessage,type:r.toastType,duration:3e3},null,8,["show","message","type"])],512)}const rp=Fe(jS,[["render",x5],["__scopeId","data-v-6c52d31d"]]),D5="/local/offermanager/",O5=(()=>{const e=window.location.host,t=window.location.pathname,s=Lg.wwwroot.replace(/^https?\:\/\//i,"").replace(e,"").concat(D5);return t.includes("index.php")?s+"index.php":s})(),S5=[{path:"/",name:"offer.index",component:sE,meta:{title:"Gerenciar Ofertas"}},{path:"/offers/create",name:"offer.create",component:G2,meta:{title:"Nova Oferta"}},{path:"/offers/:id/edit",name:"offer.edit",component:WS,props:!0,meta:{title:"Editar Oferta"}},{path:"/offers/classes/create",name:"offer.class.create",component:rp,props:!0,meta:{title:"Nova Turma"}},{path:"/offers/classes/:classId/edit",name:"offer.class.edit",component:rp,props:!0,meta:{title:"Editar Turma"}},{path:"/enrollments/:classId",name:"Enrollments",component:r2,props:!0,meta:{title:"Usuários matriculados"}},{path:"/:pathMatch(.*)*",redirect:"/"}],ia=Gb({history:rb(O5),routes:S5,scrollBehavior(){return{top:0}}});ia.beforeEach((e,t,s)=>{document.title=e.meta.title||"Gerenciar Ofertas",s()}),ia.onError(e=>{console.error("Erro de navegação:",e),(e.name==="NavigationDuplicated"||e.message.includes("No match")||e.message.includes("missing required param"))&&ia.push("/")});const e9="",T5=()=>{const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)};return{init:(e,t={})=>{T5();const s=zv(T0);return s.use(x0()),s.use(ia),s.mount(e),s}}});
//# sourceMappingURL=app-lazy.min.js.map
