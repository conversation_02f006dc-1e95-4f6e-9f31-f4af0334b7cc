<template>
  <div class="modal-overlay" v-if="modelValue" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>Adicionar curso</h2>
        <button class="close-button" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <h3 class="section-title">SELECIONAR CURSO</h3>

        <div class="search-section">
          <div class="search-group">
            <Autocomplete
              v-model="selectedCategory"
              :items="categoryOptions"
              label="Categoria"
              placeholder="Pesquisar..."
              :input-max-width="250"
              :loading="loadingCategories"
              :show-filter-tags="false"
              :show-selected-in-input="true"
              :auto-open="true"
              :has-search-icon="true"
              :max-label-length="25"
              :no-results-text="
                categoryOptions.length === 0
                  ? 'Nenhuma categoria disponível'
                  : 'Nenhuma categoria encontrada'
              "
            />
          </div>

          <div class="search-group">
            <Autocomplete
              v-model="selectedCourse"
              :items="courseOptions"
              label="Curso"
              placeholder="Pesquisar..."
              :input-max-width="250"
              :disabled="!selectedCategory"
              :loading="loadingCourses || loadingMoreCourses"
              :auto-open="true"
              :has-search-icon="true"
              :max-label-length="25"
              :keep-open-on-select="true"
              :no-results-text="courseNoResultsText"
              @select="handleCourseSelect"
              @load-more="loadMoreCourses"
              @search="handleCourseSearch"
              ref="courseAutocomplete"
            />
          </div>
        </div>

        <div class="table-container">
          <div
            v-if="selectedCoursesPreview.length === 0"
            class="empty-preview-message"
          >
            <p>Selecione cursos acima para adicioná-los à oferta</p>
          </div>
          <CustomTable
            v-else
            :headers="tableHeaders"
            :items="filteredCourses"
            :sort-by="sortBy"
            :sort-desc="sortDesc"
            @sort="handleTableSort"
          >
            <template #item-actions="{ item }">
              <div class="action-buttons">
                <button
                  class="btn-action btn-delete"
                  @click="removeCourse(item)"
                  title="Remover da lista"
                >
                  <i class="fa fa-trash fa-fw"></i>
                </button>
              </div>
            </template>
          </CustomTable>
        </div>

        <Pagination
          v-if="selectedCoursesPreview.length > 0"
          v-model:current-page="currentPage"
          v-model:per-page="perPage"
          :total="selectedCoursesPreview.length"
          @update:current-page="handlePageChange"
          @update:per-page="handlePerPageChange"
        />
      </div>

      <div class="modal-footer">
        <CustomButton
          variant="primary"
          label="Confirmar"
          :is-loading="loading"
          :disabled="selectedCoursesPreview.length === 0"
          @click="addOfferCourses"
        />
        <CustomButton
          variant="secondary"
          label="Cancelar"
          @click="closeModal"
        />
      </div>
    </div>
  </div>
</template>

<script>
import CustomInput from "@/components/CustomInput.vue";
import CustomButton from "@/components/CustomButton.vue";
import CustomTable from "@/components/CustomTable.vue";
import Pagination from "@/components/Pagination.vue";
import Autocomplete from "@/components/Autocomplete.vue";
import FilterTag from "@/components/FilterTag.vue";
import {
  getCategories,
  getCoursesByCategory,
  addCoursesToOffer,
  getCurrentCourses,
} from "@/services/offer";

export default {
  name: "AddOfferCourseModal",

  components: {
    CustomInput,
    CustomButton,
    CustomTable,
    Pagination,
    Autocomplete,
    FilterTag,
  },

  props: {
    modelValue: {
      type: Boolean,
      required: true,
    },
    offerId: {
      type: Number,
      required: true,
    },
  },

  emits: ["update:modelValue", "confirm"],

  data() {
    return {
      selectedCategory: null,
      selectedCourse: null,
      categoryOptions: [],
      courseOptions: [],
      currentPage: 1,
      perPage: 5,
      sortBy: "name",
      sortDesc: false,

      loading: false,
      loadingCategories: false,
      loadingCourses: false,
      loadingCurrentOfferCourses: false,

      // Paginação de cursos potenciais
      coursesPage: 1,
      coursesPerPage: 20,
      coursesTotalPages: 1,
      hasMoreCourses: false,
      loadingMoreCourses: false,

      // Configuração da tabela
      tableHeaders: [
        { text: "CURSO", value: "name", sortable: true },
        { text: "AÇÕES", value: "actions", sortable: false, align: "right" },
      ],

      // Cursos selecionados na sessão atual (pré-visualização)
      selectedCoursesPreview: [],

      currentOfferCourses: [],
    };
  },

  computed: {
    filteredCourses() {
      const sortedCourses = [...this.selectedCoursesPreview].sort((a, b) => {
        const modifier = this.sortDesc ? -1 : 1;
        if (a[this.sortBy] < b[this.sortBy]) return -1 * modifier;
        if (a[this.sortBy] > b[this.sortBy]) return 1 * modifier;
        return 0;
      });

      // Aplicar paginação
      const startIndex = (this.currentPage - 1) * this.perPage;
      const endIndex = startIndex + this.perPage;
      return sortedCourses.slice(startIndex, endIndex);
    },

    // Calcula o número total de páginas para a paginação
    totalPages() {
      return Math.ceil(this.selectedCoursesPreview.length / this.perPage);
    },

    // Propriedade computada para texto de "sem resultados" do autocomplete de curso
    courseNoResultsText() {
      if (this.loadingCourses) {
        return "Buscando cursos...";
      }
      if (this.loadingMoreCourses) {
        return "Carregando mais cursos...";
      }
      if (!this.selectedCategory) {
        return "Selecione uma categoria primeiro";
      }
      // Verifica se há cursos disponíveis na categoria, mas todos já foram selecionados
      if (this.courseOptions.length === 0 && this.selectedCategory) {
        return "Todos os cursos já foram adicionados";
      }
      return "Nenhum curso encontrado";
    },
  },

  watch: {
    modelValue(newValue, oldValue) {
      if (newValue) {
        this.getCurrentOfferCourses();
        this.getCategories(); // Carrega todas as categorias ao abrir
      } else {
        // Limpa dados ao fechar para garantir estado inicial na próxima abertura
        this.selectedCategory = null;
        this.selectedCourse = null;
        this.categoryOptions = [];
        this.courseOptions = [];
        this.selectedCoursesPreview = []; // Limpa a lista de cursos selecionados na sessão atual
      }
    },

    selectedCategory(newValue) {
      this.courseOptions = [];
      this.selectedCourse = null;

      // Reseta a paginação
      this.coursesPage = 1;
      this.coursesTotalPages = 1;
      this.hasMoreCourses = false;

      if (!newValue) {
        this.getCurrentOfferCourses();
        return;
      }

      this.getCoursesForCategory(newValue.value);
    },

    courseOptions(newOptions) {
      if (
        newOptions.length < 10 &&
        this.hasMoreCourses &&
        !this.loadingMoreCourses &&
        !this.loadingCourses
      ) {
        this.$nextTick(() => {
          this.loadMoreCourses();
          console.log("Carregando mais cursos... via watch");
        });
      }
    },
  },

  methods: {
    /**
     * Fetches and updates the list of current courses associated with the offer.
     *
     * @returns {Promise<void>}
     */
    async getCurrentOfferCourses() {
      try {
        this.loadingCurrentOfferCourses = true;

        const response = await getCurrentCourses(this.offerId);

        this.currentOfferCourses = response.courses.map((course) => ({
          id: course.courseid,
          name: course.fullname,
          offerCourseId: course.id,
        }));
      } catch (error) {
        //
      } finally {
        this.loadingCurrentOfferCourses = false;
      }
    },

    /**
     * Fetches and updates the list of categories available to add to the offer.
     *
     * @returns {Promise<void>}
     */
    async getCategories() {
      try {
        this.loadingCategories = true;
        this.categoryOptions = [];

        const response = await getCategories("");

        this.categoryOptions = response.map((category) => ({
          value: category.id,
          label: category.name,
        }));
      } catch (error) {
        //
      } finally {
        this.loadingCategories = false;
      }
    },

    async addOfferCourses() {
      try {
        this.loading = true;

        if (this.selectedCoursesPreview.length === 0) {
          this.closeModal();
          return;
        }

        const courseIds = this.selectedCoursesPreview.map(
          (course) => course.id
        );

        await addCoursesToOffer(this.offerId, courseIds);

        this.$emit("confirm", this.selectedCoursesPreview);

        this.closeModal();
      } catch (error) {
        //
      } finally {
        this.loading = false;
      }
    },

    async getCoursesForCategory(
      categoryId,
      page = 1,
      append = false,
      searchText = ""
    ) {
      if (!categoryId) return;

      try {
        if (page === 1) {
          this.loadingCourses = true;
          if (!append) {
            this.courseOptions = [];
          }
        } else {
          this.loadingMoreCourses = true;
        }

        const response = await getCoursesByCategory(
          this.offerId,
          categoryId,
          searchText,
          page,
          this.coursesPerPage
        );

        let paginationData = null;
        let coursesList = [];

        coursesList = response.courses;
        paginationData = {
          page: response.page || 1,
          total_pages: response.total_pages || 1,
        };

        if (paginationData) {
          // Atualiza informações de paginação
          this.coursesPage = paginationData.page || 1;
          this.coursesTotalPages = paginationData.total_pages || 1;
          this.hasMoreCourses =
            (paginationData.page || 1) < (paginationData.total_pages || 1);

          if (coursesList && coursesList.length > 0) {
            // Filtra os cursos que já foram adicionados à oferta
            const coursesNotYetAdded = coursesList.filter(
              (course) =>
                // Não mostrar cursos que já estão na oferta
                !this.currentOfferCourses.some((c) => c.id === course.id) &&
                // Não mostrar cursos que já foram selecionados na sessão atual
                !this.selectedCoursesPreview.some((c) => c.id === course.id)
            );

            const newCourseOptions = coursesNotYetAdded.map((course) => ({
              value: course.id,
              label: course.fullname,
            }));

            if (append) {
              // Adiciona os novos cursos aos existentes
              this.courseOptions = [...this.courseOptions, ...newCourseOptions];
            } else {
              // Substitui completamente a lista
              this.courseOptions = newCourseOptions;
            }
          }
        } else {
          console.warn("Formato de resposta inesperado");
        }
      } catch (error) {
        console.error("Erro ao carregar cursos da categoria:", error);
        if (!append) {
          this.courseOptions = []; // Garante que esteja vazio em caso de erro apenas se não estiver anexando
        }
      } finally {
        if (page === 1) {
          this.loadingCourses = false;
        } else {
          this.loadingMoreCourses = false;
        }
      }
    },

    // Carrega mais cursos (próxima página)
    async loadMoreCourses() {
      if (
        this.hasMoreCourses &&
        !this.loadingMoreCourses &&
        !this.loadingCourses
      ) {
        const nextPage = this.coursesPage + 1;
        await this.getCoursesForCategory(
          this.selectedCategory.value,
          nextPage,
          true
        );
      }
    },

    // Manipula a busca por texto no autocomplete de cursos
    async handleCourseSearch(searchText) {
      if (!this.selectedCategory) return;

      // Reseta a paginação para a primeira página
      this.coursesPage = 1;
      this.coursesTotalPages = 1;
      this.hasMoreCourses = false;

      // Carrega cursos com o termo de busca (ou sem termo se searchText for vazio)
      await this.getCoursesForCategory(
        this.selectedCategory.value,
        1,
        false,
        searchText || ""
      );
    },

    // Chamado quando um curso é selecionado no Autocomplete
    handleCourseSelect(course) {
      if (
        course &&
        !this.selectedCoursesPreview.some((c) => c.id === course.value)
      ) {
        // Adiciona o curso à lista de pré-visualização se ainda não estiver lá
        this.selectedCoursesPreview.push({
          id: course.value,
          name: course.label,
        });

        // Atualiza as opções de cursos para remover o curso selecionado
        this.courseOptions = this.courseOptions.filter(
          (c) => c.value !== course.value
        );

        // Reseta a paginação para a primeira página quando um novo curso é adicionado
        this.currentPage = 1;
      }

      // Limpa a seleção do autocomplete de curso após adicionar
      this.selectedCourse = null;
    },

    removeCourse(course) {
      const courseIndex = this.selectedCoursesPreview.findIndex(
        (c) => c.id === course.id
      );

      if (courseIndex !== -1) {
        // Remove o curso da lista de pré-visualização
        const removedCourse = this.selectedCoursesPreview.splice(
          courseIndex,
          1
        )[0];

        // Verifica se após a remoção, a página atual ficou vazia e não é a primeira página
        if (
          this.currentPage > 1 &&
          this.currentPage >
            Math.ceil(this.selectedCoursesPreview.length / this.perPage)
        ) {
          // Volta para a página anterior
          this.currentPage = Math.max(1, this.currentPage - 1);
        }

        if (this.selectedCategory) {
          this.getCoursesForCategory(
            this.selectedCategory.value,
            this.currentPage,
            false
          );
        } else {
          this.courseOptions.push({
            value: removedCourse.id,
            label: removedCourse.name,
          });
        }
      }
    },

    handleTableSort({ sortBy, sortDesc }) {
      this.sortBy = sortBy;
      this.sortDesc = sortDesc;
    },

    // Manipula a mudança de página na paginação
    handlePageChange(page) {
      this.currentPage = page;
    },

    // Manipula a mudança de itens por página
    handlePerPageChange(perPage) {
      this.perPage = perPage;
      this.currentPage = 1; // Volta para a primeira página ao mudar itens por página
    },

    closeModal() {
      this.$emit("update:modelValue", false);
      // Limpar estados ao fechar o modal
      this.selectedCategory = null;
      this.selectedCourse = null;
      this.selectedCoursesPreview = []; // Limpa a lista de cursos selecionados na sessão atual
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #212529;
  border-radius: 4px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 1rem;
  border-bottom: 1px solid #343a40;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    font-size: 1.25rem;
    color: #fff;
    margin: 0;
  }

  .close-button {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    font-size: 1.25rem;

    &:hover {
      color: var(--primary);
    }
  }
}

.modal-body {
  padding: 1rem;

  .section-title {
    color: var(--primary);
    font-size: 1rem;
    font-weight: bold;
    margin-bottom: 1rem;
  }
}

.search-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.search-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  .search-button {
    background-color: var(--primary);
    border: none;
    color: #fff;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    height: 38px;
    width: 38px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: none;

    &:hover {
      background-color: #0b5ed7;
    }
  }
}

.table-container {
  margin-bottom: 1rem;

  .empty-preview-message {
    background-color: #343a40;
    padding: 2rem;
    text-align: center;
    border-radius: 4px;

    p {
      color: #adb5bd;
      font-style: italic;
      margin: 0;
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Os estilos de .btn-action foram movidos para global.scss */
.btn-action {
  padding: 0.5rem;

  i {
    font-size: 1.25rem; /* Mesmo tamanho do ícone de fechar */
  }
}

.modal-footer {
  padding: 1rem;
  border-top: 1px solid #343a40;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}
</style>

<style lang="scss">
.modal-content .custom-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
</style>
