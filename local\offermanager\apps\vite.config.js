import { fileURLToPath, URL } from "node:url";
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "node:path";

export default defineConfig({
  mode: "production",
  plugins: [vue()],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },

  define: {
    "process.env": {},
  },

  css: {
    preprocessorOptions: {
      scss: {
        quietDeps: true,
        logger: {
          warn: () => {},
        },
      },
    },
  },

  build: {
    emptyOutDir: false,
    outDir: path.resolve("../amd/build/app"),
    sourcemap: true,

    lib: {
      entry: path.resolve("./src/main.js"),
      name: "app",
      formats: ["amd"],
      fileName: "app-lazy.min",
    },

    rollupOptions: {
      external: [
        "core/config",
        "core/ajax",
        "core/str",
        "core/localstorage",
        "core/notification",
        "core/toast",
        "tool_lfxp/ajax",
      ],

      preserveEntrySignatures: "strict",

      output: {
        format: "amd",
        sourcemap: true,
        manualChunks: false,
        inlineDynamicImports: true,
        entryFileNames: "app-lazy.min.js",
        assetFileNames: "[name].[ext]",
        amd: {
          id: "local_offermanager/app/app-lazy",
        },

        globals: {
          "core/config": "Config",
          "core/str": "Str",
          "core/ajax": "Ajax",
          "core/notification": "Notification",
          "core/toast": "Toast",
          "tool_lfxp/ajax": "LfxpAjax",
        },
      },
    },
  },
  server: {
    port: 3000,
  },
});
