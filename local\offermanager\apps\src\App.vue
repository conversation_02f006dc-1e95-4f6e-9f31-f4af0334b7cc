<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: "App",
  mounted() {
    // Carregar Font Awesome se ainda não estiver carregado
    if (!document.querySelector('link[href*="font-awesome"]')) {
      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href =
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css";
      document.head.appendChild(link);
    }
  },
};
</script>

<style>
#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #fff;
  padding: 0;
  text-align: left;
  margin-bottom: 2rem;
}

/* Estilos globais para o tema escuro */
body {
  background-color: #212529;
  color: #fff;
}

nav a {
  font-weight: bold;
  color: #fff;
  text-decoration: none;
}

nav a.router-link-exact-active {
  color: var(--primary);
}

/* Estilos globais para botões e elementos de formulário */
button {
  cursor: pointer;
}

input,
select {
  background-color: #212529;
  border: 1px solid #373b3e;
  color: #fff;
}

input[type="date"]::-webkit-calendar-picker-indicator,
input[type="time"]::-webkit-calendar-picker-indicator {
  filter: invert(50%) sepia(100%) saturate(0%) hue-rotate(0deg);
}

/* Estilos para ícones */
.fas {
  display: inline-block;
}
</style>
