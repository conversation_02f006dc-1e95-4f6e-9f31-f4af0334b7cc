<template>
  <div class="alert" :class="`alert-${type}`">
    <i :class="icon" v-if="icon"></i>
    {{ text.replace("-", "\u2011") }}
  </div>
</template>

<script>
export default {
  name: "Alert",
  props: {
    type: {
      type: String,
      default: "info",
    },
    text: {
      type: String,
      required: true,
    },
    icon: {
      type: String,
      required: false,
    },
  },
};
</script>

<style lang="scss" scoped>
.alert {
  display: flex;
  gap: 0.5rem;

  i {
    margin-right: 0.5rem;
    display: flex;
    align-items: center;
  }
}
</style>
